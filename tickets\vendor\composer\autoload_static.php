<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit87f454ad57307c8edeab0671c032c80d
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'PhpImap\\' => 8,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PhpImap\\' => 
        array (
            0 => __DIR__ . '/..' . '/php-imap/php-imap/src/PhpImap',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit87f454ad57307c8edeab0671c032c80d::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit87f454ad57307c8edeab0671c032c80d::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit87f454ad57307c8edeab0671c032c80d::$classMap;

        }, null, ClassLoader::class);
    }
}
