<?php
session_start();
include('includes/config.php');
error_reporting(0);
if (strlen($_SESSION['login']) == 0) {
    header('location:index.php');
} else {

    if ($_GET['action'] = 'del') {
        $articleid = intval($_GET['pid']);
        $query = mysqli_query($con, "update tblarticles set Is_Active=0 where id='$articleid'");
        if ($query) {
            $msg = "Article deleted ";
        } else {
            $error = "Something went wrong . Please try again.";
        }
    }
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="A fully featured admin theme which can be used to build CRM, CMS, etc.">
        <meta name="author" content="Celaeno">

        <!-- App favicon -->
        <link rel="shortcut icon" href="assets/images/favicon.ico">
        <!-- App title -->
        <title>Celaeno Technology | Manage Articles</title>

        <!--Morris Chart CSS -->
        <link rel="stylesheet" href="../plugins/morris/morris.css">

        <!-- jvectormap -->
        <link href="../plugins/jvectormap/jquery-jvectormap-2.0.2.css" rel="stylesheet" />

        <!-- App css -->
        <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
        <link rel="stylesheet" href="../plugins/switchery/switchery.min.css">

        <!-- HTML5 Shiv and Respond.js IE8 support of HTML5 elements and media queries -->
        <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
        <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
        <![endif]-->

        <script src="assets/js/modernizr.min.js"></script>
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    </head>


    <body class="fixed-left">

        <!-- Begin page -->
        <div id="wrapper">

            <!-- Top Bar Start -->
            <?php include('includes/topheader.php'); ?>

            <!-- ========== Left Sidebar Start ========== -->
            <?php include('includes/leftsidebar.php'); ?>


            <!-- ============================================================== -->
            <!-- Start right Content here -->
            <!-- ============================================================== -->
            <div class="content-page">
                <!-- Start content -->
                <div class="content">
                    <div class="container">


                        <div class="row">
                            <div class="col-xs-12">
                                <div class="page-title-box">
                                    <h4 class="page-title">Manage Articles </h4>
                                    <ol class="breadcrumb p-0 m-0">
                                        <li>
                                            <a href="#">Admin</a>
                                        </li>
                                        <li>
                                            <a href="#">Articles</a>
                                        </li>
                                        <li class="active">
                                            Manage Article
                                        </li>
                                    </ol>
                                    <div class="clearfix"></div>
                                </div>
                            </div>
                        </div>
                        <!-- end row -->




                        <div class="row">
                            <div class="col-sm-12">

                                <a href="add-article.php">
                                    <button id="addToTable" class="btn btn-success waves-effect waves-light"
                                        style="margin-bottom: 1rem;">Add
                                        <i class="mdi mdi-plus-circle-outline"></i>
                                    </button>
                                </a>

                                <div class="filter-articles">
                                    <select class="form-control" name="areas" id="areas" required>
                                        <option value="-1">Select Area</option>
                                        <?php
                                        // Feching active areas
                                        $ret = mysqli_query($con, "SELECT id, AreaName FROM tblarea WHERE Is_Active=1");
                                        while ($result = mysqli_fetch_array($ret)) {
                                        ?>
                                            <option value="<?php echo htmlentities($result['id']); ?>">
                                                <?php echo htmlentities($result['AreaName']); ?></option>
                                        <?php } ?>
                                    </select>

                                    <select class="form-control" name="category" id="categories" required>
                                        <option value="">Select Category</option>
                                    </select>
                                </div>

                                <div>
                                    <div class="table-responsive">
                                        <table
                                            class="table table-colored table-centered m-0 table-colored-bordered table-bordered-primary">
                                            <thead>
                                                <tr>
                                                    <th>Title</th>
                                                    <th>Area</th>
                                                    <th>Category</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody id="articles">

                                                <?php
                                                $query = mysqli_query($con, "SELECT tblarticles.id AS articleid, tblarticles.ArticleTitle AS title, tblarea.AreaName AS category,tblcategory.Category AS subcategory FROM tblarticles LEFT JOIN tblarea ON tblarea.id=tblarticles.AreaId LEFT JOIN tblcategory ON tblcategory.CategoryId=tblarticles.CategoryId WHERE tblarticles.Is_Active=1");
                                                $rowcount = mysqli_num_rows($query);
                                                if ($rowcount == 0) {
                                                ?>
                                                    <tr>

                                                        <td colspan="4" align="center">
                                                            <h3 style="color:red">No record found</h3>
                                                        </td>
                                                    <tr>
                                                        <?php
                                                    } else {
                                                        while ($row = mysqli_fetch_array($query)) {
                                                        ?>
                                                    <tr>
                                                        <td><b><?php echo htmlentities($row['title']); ?></b></td>
                                                        <td><?php echo htmlentities($row['category']) ?></td>
                                                        <td><?php echo htmlentities($row['subcategory']) ?></td>

                                                        <td><a
                                                                href="edit-article.php?pid=<?php echo htmlentities($row['articleid']); ?>"><i
                                                                    class="fa fa-pencil" style="color: #29b6f6;"></i></a>
                                                            &nbsp;<a
                                                                href="manage-articles.php?pid=<?php echo htmlentities($row['articleid']); ?>&&action=del"
                                                                onclick="return confirm('Do you really want to delete ?')"> <i
                                                                    class="fa fa-trash-o" style="color: #f05050"></i></a> </td>
                                                    </tr>
                                            <?php }
                                                    } ?>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>



                    </div> <!-- container -->

                </div> <!-- content -->

                <?php include('includes/footer.php'); ?>

            </div>


            <!-- ============================================================== -->
            <!-- End Right content here -->
            <!-- ============================================================== -->


        </div>
        <!-- END wrapper -->



        <script>
            var resizefunc = [];

            $(document).ready(function() {
                $('#areas').change(function() {
                    var areaId = $(this).val();

                    if (areaId) {
                        // Fetch categories based on selected area
                        $.ajax({
                            url: 'includes/article_process.php',
                            type: 'POST',
                            data: {
                                area_id: areaId
                            },
                            success: function(data) {
                                let optionsAndArticles = data.split("*=*");

                                $('#categories').html(optionsAndArticles[0]);

                                $('#articles').html(optionsAndArticles[1]);
                            },
                            error: function() {
                                alert('Failed to fetch categories.');
                            }
                        });
                    } else {
                        $('#categories').html('<option value="">Select Category</option>');
                    }
                });

                $('#categories').change(function() {
                    var categoryId = $(this).val();

                    if (categoryId) {
                        // Fetch categories based on selected area
                        $.ajax({
                            url: 'includes/article_process.php',
                            type: 'POST',
                            data: {
                                category_id: categoryId
                            },
                            success: function(data) {
                                $('#articles').html(data);
                            },
                            error: function() {
                                alert('Failed to fetch categories.');
                            }
                        });
                    } else {
                        $('#categories').html('<option value="">Select Category</option>');
                    }
                });
            });
        </script>

        <!-- jQuery  -->
        <script src="assets/js/jquery.min.js"></script>
        <script src="assets/js/bootstrap.min.js"></script>
        <script src="assets/js/detect.js"></script>
        <script src="assets/js/fastclick.js"></script>
        <script src="assets/js/jquery.blockUI.js"></script>
        <script src="assets/js/waves.js"></script>
        <script src="assets/js/jquery.slimscroll.js"></script>
        <script src="assets/js/jquery.scrollTo.min.js"></script>
        <script src="../plugins/switchery/switchery.min.js"></script>

        <!-- CounterUp  -->
        <script src="../plugins/waypoints/jquery.waypoints.min.js"></script>
        <script src="../plugins/counterup/jquery.counterup.min.js"></script>

        <!--Morris Chart-->
        <script src="../plugins/morris/morris.min.js"></script>
        <script src="../plugins/raphael/raphael-min.js"></script>

        <!-- Load page level scripts-->
        <script src="../plugins/jvectormap/jquery-jvectormap-2.0.2.min.js"></script>
        <script src="../plugins/jvectormap/jquery-jvectormap-world-mill-en.js"></script>
        <script src="../plugins/jvectormap/gdp-data.js"></script>
        <script src="../plugins/jvectormap/jquery-jvectormap-us-aea-en.js"></script>


        <!-- Dashboard Init js -->
        <script src="assets/pages/jquery.blog-dashboard.js"></script>

        <!-- App js -->
        <script src="assets/js/jquery.core.js"></script>
        <script src="assets/js/jquery.app.js"></script>

    </body>

    </html>
<?php } ?>