ALTER TABLE `tickets` ADD `assignee` INT NULL DEFAULT NULL AFTER `status`;
ALTER TABLE `tickets` ADD `due_date` DATE NULL DEFAULT NULL AFTER `attachment`;
ALTER TABLE `tickets` ADD `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER `created_at`;


CREATE TABLE `helpdesk`.`tblticket_conversation` (`id` INT NOT NULL AUTO_INCREMENT , `ticket_id` INT NOT NULL , `sender_id` INT NOT NULL , `sender_type` ENUM('client','system','','') NOT NULL , `message` TEXT NOT NULL , `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP , PRIMARY KEY (`id`)) ENGINE = InnoDB;


ALTER TABLE `tblticket_conversation` CHANGE `ticket_id` `ticket_id` VARCHAR(20) NOT NULL;


INSERT INTO tblticket_conversation (ticket_id, sender_id, sender_type, message) VALUES
('TICK0001', 1, 'client', 'Hello, I am having trouble with my order.'),
('TICK0001', 5, 'system', 'Hi there! Thanks for contacting us. Can you please provide more details about the issue?'),
('TICK0001', 1, 'client', 'Sure, I haven\'t received my shipment yet. The tracking number is ABC-123.'),
('TICK0001', 5, 'system', 'Thank you for the information. Let me check on that for you.'),
('TICK0001', 1, 'system', 'Status update: Checking shipment status for ABC-123.'),
('TICK0001', 5, 'system', 'It appears there was a slight delay. Your package is expected to arrive tomorrow.'),
('TICK0001', 1, 'client', 'Okay, thank you for the update.'),
('TICK0001', 5, 'system', 'You\'re welcome! Please let us know if you have any further questions.');


CREATE TABLE ticket_history ( id INT AUTO_INCREMENT PRIMARY KEY, ticket_id VARCHAR(50), action VARCHAR(255), performed_by VARCHAR(100), created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, details TEXT );






