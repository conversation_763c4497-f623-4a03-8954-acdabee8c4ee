

<?php
include "./includes/config.php";
require_once("./utils/groups-functions.php");

$ticket_id = isset($_GET['ticket_id']) ? $_GET['ticket_id'] : null;
// Assuming you have the current ticket ID in a variable, e.g., $currentTicketId
$currentTicketId = isset($_GET['ticket_id']) ? $_GET['ticket_id'] : null;

if ($ticket_id) {
  $stmt = $con->prepare("SELECT * FROM tickets WHERE id = ?");
  $stmt->bind_param("s", $ticket_id);
  $stmt->execute();
  $ticket_result = $stmt->get_result();
  $ticket = $ticket_result->fetch_assoc();
}

function getMessagesForTicket(mysqli $con, string $ticketId): array
{
  $messages = [];
  $stmt = $con->prepare("SELECT id, sender_id, sender_type, message, created_at FROM tblticket_conversation WHERE ticket_id = ? ORDER BY created_at ASC");

  if ($stmt) {
    $stmt->bind_param("s", $ticketId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result) {
      while ($row = $result->fetch_assoc()) {
        $messages[] = $row;
      }
      $result->free();
    } else {
      error_log("Error fetching messages: " . $stmt->error);
    }
    $stmt->close();
  } else {
    error_log("Error preparing statement: " . $con->error);
  }
  return $messages;
}

function getTicketHistory(mysqli $con, string $ticketId): array
{
  $history = [];
  $stmt = $con->prepare("
        SELECT action, performed_by, created_at, details 
        FROM ticket_history 
        WHERE ticket_id = ? 
        ORDER BY created_at ASC
    ");

  if ($stmt) {
    $stmt->bind_param("s", $ticketId);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
      $history[] = $row;
    }

    $result->free();
    $stmt->close();
  } else {
    error_log("Error preparing history statement: " . $con->error);
  }
  return $history;
}

// Insert resolution if form is submitted
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['newResolution']) && $ticket_id) {
  $resolutionText = $_POST['newResolution'];
  $createdAt = date('Y-m-d H:i:s');

  $stmt = $con->prepare("INSERT INTO ticket_resolutions (ticket_id, resolution_text, created_at) VALUES (?, ?, ?)");
  $stmt->bind_param("sss", $ticket_id, $resolutionText, $createdAt);

  if (!$stmt->execute()) {
    $errorMessage = "Error saving resolution: " . $stmt->error;
  }
  $stmt->close();

  // Redirect to prevent resubmission
  header("Location: " . $_SERVER['REQUEST_URI']);
  exit;
}

// Fetch resolutions for this ticket
$resolutions = [];
if ($ticket_id) {
  $stmt = $con->prepare("SELECT resolution_text, created_at FROM ticket_resolutions WHERE ticket_id = ? ORDER BY created_at ASC");
  $stmt->bind_param("s", $ticket_id);
  $stmt->execute();
  $result = $stmt->get_result();

  while ($row = $result->fetch_assoc()) {
    $resolutions[] = $row;
  }
  $stmt->close();
}


if ($currentTicketId) {
  $messages = getMessagesForTicket($con, $currentTicketId);
  $history = getTicketHistory($con, $currentTicketId);  // Add this line
} else {
  $messages = []; // Or handle the case where no ticket ID is provided
}

// Assuming you have a way to identify if the current viewer is a client or admin
// For example, you might have a session variable: $_SESSION['user_type'] ('client' or 'admin')
$currentUserType = isset($_SESSION['user_type']) ? $_SESSION['user_type'] : 'guest'; // Default to guest if not set
$currentUserId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null; // Get current user ID if logged in

$user_map = getAllUsers($con);

?>

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HelpDesk Tickets</title>

  <link rel="stylesheet" href="./assets/css/ticket-view.css">
  <link rel="stylesheet" href="./assets/css/texteditor.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
    integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A=="
    crossorigin="anonymous" referrerpolicy="no-referrer" />
  <style>
    <style>.chat-container {
      display: flex;
      flex-direction: column;
      height: 400px;
      /* Adjust the height as needed */
      overflow-y: auto;
      /* Enable scrolling for the conversation area */
    }

    .conversations {
      flex-grow: 1;
      /* Allow the conversation area to take up remaining space */
      padding-bottom: 80px;
      /* Add padding at the bottom to prevent messages from being hidden by the fixed input */
    }

    .send-message-form {
      position: sticky;
      /* Stick to the bottom of the parent container */
      bottom: 0;
      background-color: white;
      /* Ensure the background covers the content below */
      padding: 10px;
      border-top: 1px solid #ccc;
      /* Optional: Add a border for separation */
    }

    /* Optional: Style the text editor for better appearance */
    .wsyig {
      border: 1px solid #ddd;
      padding: 8px;
      margin-bottom: 5px;
      /* Add other styling as needed */
      min-height: 50px;
      /* Ensure it has some initial height */
    }

    .attachment-list {
      background: #f8f9fa;
      border-radius: 8px;
    }

    .attachment-card {
      transition: transform 0.2s;
      border: none;
      max-width: 300px;
      margin: 0 auto;
    }

    .attachment-card:hover {
      transform: translateY(-5px);
    }

    .attachment-img {
      height: 200px;
      object-fit: cover;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }

    .file-placeholder {
      height: 200px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .empty-content i {
      opacity: 0.5;
    }

    .card-body {
      padding: 1rem;
    }

    .btn-outline-primary {
      border-radius: 5px;
    }


    .chat-box {
      border-left: 3px solid #007bff;
      padding-left: 10px;
    }

    .chat-message {
      margin-bottom: 10px;
    }
  </style>
</head>

<body>
  <div class="app-container">
    <!-- Sidebar Toggle Button -->
    <button id="sidebar-toggle" class="sidebar-toggle">
      <i class="fa-solid fa-bars"></i>
    </button>

    <!-- Sidebar -->
    <div class="sidebar collapsed" id="sidebar">
      <div class="sidebar-header">
        <h2>Tickets</h2>
      </div>
      <div class="sidebar-search">
        <div class="search-container">
          <i class="fas fa-search search-icon"></i>
          <input type="text" placeholder="Search tickets..." class="search-input" />
        </div>
        <div class="filter-container">
          <select class="filter-select" name="filter" id="filter">
            <option value="all">All Tickets</option>
            <option value="Closed">Closed Tickets</option>
            <option value="My_tickets">My Tickets</option>

            <option value="Open">Open</option>
            <option value="Hold">Hold</option>
            <option value="Resolved">Resolved</option>
            <option value="Closed">Closed</option>
            <option value="Overdue">Overdue</option>

          </select>
        </div>
      </div>
      <div class="sidebar-content">
        <div class="ticket-list">
          <?php
          $all_tickets = $con->query("SELECT id, subject, first_name, created_at, status FROM tickets");
          while ($row = $all_tickets->fetch_assoc()) {
            $ticketId = htmlspecialchars($row['id']);
            $activeClass = ($ticket_id == $ticketId) ? 'active' : '';
            echo "<div class='ticket-item $activeClass' data-ticket-id='$ticketId' onclick=\"window.location.href='?ticket_id=$ticketId'\">";
            echo "<div class='ticket-item-header'>";
            echo "<i class='fas fa-ticket-alt ticket-item-icon'></i>";
            echo "<span class='ticket-item-id'>#$ticketId " . htmlspecialchars($row['subject']) . "</span>";
            echo "</div>";
            echo "<div class='ticket-item-meta'>";
            echo "<span>" . htmlspecialchars($row['first_name']) . "</span>";
            echo "<span>" . date('d M Y', strtotime($row['created_at'])) . "</span>";
            echo "</div>";
            echo "</div>";
          }
          ?>
        </div>
      </div>
      <div class="sidebar-footer">
        <span class="ticket-count">Total: <?php echo $all_tickets->num_rows; ?> tickets</span>
        <!-- <button class="advanced-filter-btn"><i class="fas fa-filter"></i> Advanced Filters</button> -->
      </div>

      <!-- Resize handle -->
      <div id="resize-handle"></div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <?php if (!$ticket_id || !$ticket): ?>
        <div class="empty-state">
          <h2>No Ticket Selected</h2>
          <p>Select a ticket from the sidebar to view details</p>
        </div>
      <?php else: ?>
        <div class="ticket-details">
          <div class="ticket-header">
            <div class="ticket-title">
              <div style="padding: 2px; margin: 0 4px;">
                <a href="./ticket.php" style="text-decoration: none;"><i class="fa-solid fa-arrow-left"></i></a>
              </div>
              <h1>#<?php echo htmlspecialchars($ticket['id']) . " " . htmlspecialchars($ticket['subject']); ?></h1>
            </div>
            <div class="ticket-status">
              <a href="#" class="edit-ticket">
                <button class="btn btn-success waves-effect waves-light">Save</button>
              </a>
              <a href="./ticket-edit.php?ticketId=<?php echo $ticket['id']; ?>" class="edit-ticket">
                <button class="btn btn-success waves-effect waves-light">Edit</button>
              </a>
            </div>
          </div>
          <div class="ticket-body">
            <div class="ticket-content">
              <div class="tabs">
                <div class="tab-list">
                  <button class="tab-button active" data-tab="conversation"><i class="fas fa-comment"></i>
                    Conversation</button>
                  <button class="tab-button" data-tab="resolution"><i class="fas fa-check-circle"></i> Resolution</button>
                  <button class="tab-button" data-tab="attachment"><i class="fas fa-paperclip"></i> Attachment</button>
                  <button class="tab-button" data-tab="related article"><i class="fas fa-history"></i> Related Article</button>
                  <button class="tab-button" data-tab="history"><i class="fas fa-history"></i> History</button>
                </div>
                <div class="tab-content">
                  <div class="tab-pane active" id="conversation">
                    <div class="ticket-description"><?php echo $ticket['description'] ?? 'No description available'; ?></div>
                    <div class="chat-container">
                      <div class="conversations">
                        <?php if ($messages): ?>
                          <?php foreach ($messages as $message): ?>
                            <?php
                            $messageClass = '';
                            $senderName = '';

                            if ($message['sender_type'] === 'client') {
                              $messageClass = 'client-message';
                              $senderName = 'Client #' . $message['sender_id'];
                            } elseif ($message['sender_type'] === 'system') {
                              $messageClass = 'system-message';
                              $senderName = $user_map[$message['sender_id']] ?? 'System';
                            } else {
                              $messageClass = 'admin-message';
                              $senderName = 'Admin #' . $message['sender_id'];
                            }
                            ?>
                            <div class="message <?php echo $messageClass; ?>">
                              <div class="sender-info">
                                <strong><?php echo htmlspecialchars($senderName); ?></strong>
                                <span class="timestamp"><?php echo date('Y-m-d H:i:s', strtotime($message['created_at'])); ?></span>
                              </div>
                              <div class="message-content">
                                <?php echo nl2br($message['message']); ?>
                              </div>
                            </div>
                          <?php endforeach; ?>
                        <?php else: ?>
                          <p>No messages in this conversation yet.</p>
                        <?php endif; ?>
                      </div>

                      <div class="send-message-form">
                        <?php if (isset($errorMessage)): ?>
                          <p class="error"><?php echo htmlspecialchars($errorMessage); ?></p>
                        <?php endif; ?>
                        <form method="post" action="./ticket-conversion-operation.php">
                          <input type="hidden" name="ticketId" value="<?php echo htmlspecialchars($currentTicketId); ?>">
                          <input type="hidden" name="senderType" value="system">
                          <div class="form-input-des item6" style="">
                            <input type="hidden" name="newMessage" id="texteditor-hidden-Ticket-32ik45" value="">
                            <div class="wsyig" id="wsyigTicket-32ik45"></div>
                          </div>
                          <button type="submit">Send</button>
                        </form>
                      </div>
                    </div>
                  </div>
                  <div class="tab-pane" id="resolution">
                    <?php if (empty($resolutions)): ?>
                      <p>No resolution information available yet.</p>
                    <?php else: ?>
                      <div class="chat-box" style="margin-bottom: 20px;">
                        <?php foreach ($resolutions as $res): ?>
                          <div class="chat-message">
                            <div class="message-body" style="background: #f5f5f5; border-radius: 8px; padding: 10px; margin-bottom: 10px;">
                              <div><?php echo $res['resolution_text']; ?></div>
                              <div style="font-size: 12px; color: #888; margin-top: 5px;"><?php echo date("d M Y, h:i A", strtotime($res['created_at'])); ?></div>
                            </div>
                          </div>
                        <?php endforeach; ?>
                      </div>
                    <?php endif; ?>

                    <div class="empty-content">
                      <div class="send-message-form">
                        <?php if (isset($errorMessage)): ?>
                          <p class="error"><?php echo htmlspecialchars($errorMessage); ?></p>
                        <?php endif; ?>

                        <form method="POST">
                          <div class="form-input-des item6">
                            <input type="hidden" name="newResolution" id="texteditor-hidden-resolution-32ik45">
                            <div class="wsyig" id="wsyigResolution-32ik45" contenteditable="true" style="border: 1px solid #ccc; padding: 10px; min-height: 100px;"></div>
                          </div>
                          <button type="submit" onclick="submitResolution()">Send</button>
                        </form>
                      </div>
                    </div>
                  </div>

                  <script>
                    function submitResolution() {
                      const editorContent = document.getElementById('wsyigResolution-32ik45').innerHTML;
                      document.getElementById('texteditor-hidden-resolution-32ik45').value = editorContent;

                      // Make sure content isn't empty
                      if (editorContent.trim() === '') {
                        alert("Please write a resolution message.");
                        return false; // Prevent form submit
                      }

                      return true; // Allow form to submit
                    }
                  </script>


                  <div class="tab-pane" id="attachment">
                    <!-- <div class="empty-content">

                      <p>
                        <?php echo $ticket["attachment"]; ?>
                      </p>
                    </div> -->

                    <?php
                    // Check if ticket and attachment exist
                    if (empty($ticket) || empty($ticket['attachment'])) {
                    ?>
                      <div class="empty-content text-center py-5">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No attachments available.</p>
                      </div>
                    <?php
                    } else {
                      $attachment = $ticket['attachment'];
                      // Get file extension to determine if it's an image
                      $file_extension = strtolower(pathinfo($attachment, PATHINFO_EXTENSION));
                      $is_image = in_array($file_extension, ['jpg', 'jpeg', 'png', 'gif']);
                      // Assume attachment is a relative path; adjust base path if needed
                      $base_path = '../Uploads/'; // Adjust to your actual uploads directory
                      $file_path = $base_path . $attachment;
                      // Check if file exists (for local paths)
                      $file_exists = file_exists($file_path) || filter_var($attachment, FILTER_VALIDATE_URL);
                    ?>
                      <div class="attachment-list p-4">
                        <h5 class="mb-4">Attachment</h5>
                        <?php if ($file_exists) { ?>
                          <div class="card attachment-card shadow-sm">
                            <?php if ($is_image) { ?>
                              <a href="<?php echo htmlspecialchars($file_path); ?>" target="_blank">
                                <img src="<?php echo htmlspecialchars($file_path); ?>" class="card-img-top attachment-img" alt="Attachment">
                              </a>
                            <?php } else { ?>
                              <div class="card-img-top file-placeholder text-center bg-light py-5">
                                <i class="fas fa-file fa-2x text-muted"></i>
                                <p class="mt-2 mb-0"><?php echo htmlspecialchars($file_extension); ?> File</p>
                              </div>
                            <?php } ?>
                            <div class="card-body">
                              <p class="card-text mb-1">
                                <strong>File:</strong> <?php echo htmlspecialchars(basename($attachment)); ?>
                              </p>
                              <a href="<?php echo htmlspecialchars($file_path); ?>" class="btn btn-sm btn-outline-primary" <?php echo $is_image ? '' : 'download'; ?>>
                                <?php echo $is_image ? 'View' : 'Download'; ?>
                              </a>
                            </div>
                          </div>
                        <?php } else { ?>
                          <div class="empty-content text-center py-5">
                            <i class="fas fa-exclamation-circle fa-3x text-danger mb-3"></i>
                            <p class="text-danger">Attachment file not found.</p>
                          </div>
                        <?php } ?>
                      </div>
                    <?php } ?>
                  </div>




                  <div class="tab-pane" id="history">
                    <!-- <div class="history-content">
                      <?php if (!empty($history)): ?>
                        <div class="history-timeline">
                          <?php foreach ($history as $entry): ?>
                            <div class="history-item">
                              <div class="history-timestamp">
                                <?php echo date('Y-m-d H:i:s', strtotime($entry['created_at'])); ?>
                              </div>
                              <div class="history-details">
                                <div class="history-action">
                                  <strong><?php echo htmlspecialchars($entry['action']); ?></strong>
                                </div>
                                <div class="history-performer">
                                  By: <?php echo htmlspecialchars($entry['performed_by']); ?>
                                </div>
                                <?php if ($entry['details']): ?>
                                  <div class="history-description">
                                    <?php echo nl2br(htmlspecialchars($entry['details'])); ?>
                                  </div>
                                <?php endif; ?>
                              </div>
                            </div>
                          <?php endforeach; ?>
                        </div>
                      <?php else: ?>
                        <p>No history available for this ticket.</p>
                      <?php endif; ?>
                    </div> -->


                    <div class="history-content">
                      <svg display="none">
                        <symbol id="arrow">
                          <polyline points="7 10,12 15,17 10" fill="none" stroke="currentcolor" stroke-linecap="round"
                            stroke-linejoin="round" stroke-width="2" />
                        </symbol>
                      </svg>
                      <div class="timeline">
                        <div class="btn-group">
                          <button class="btn" type="button" data-action="expand">Expand All</button>
                          <button class="btn" type="button" data-action="collapse">Collapse All</button>
                        </div>
                        <?php if (!empty($history)): ?>
                          <?php $itemCount = 1; // Unique counter for ARIA attributes 
                          ?>
                          <?php foreach ($history as $entry): ?>
                            <div class="timeline__item">
                              <div class="timeline__item-header">
                                <button class="timeline__arrow" type="button" id="item<?php echo $itemCount; ?>"
                                  aria-labelledby="item<?php echo $itemCount; ?>-name" aria-expanded="false"
                                  aria-controls="item<?php echo $itemCount; ?>-ctrld" aria-haspopup="true"
                                  data-item="<?php echo $itemCount; ?>">
                                  <svg class="timeline__arrow-icon" viewBox="0 0 24 24" width="24px" height="24px">
                                    <use href="#arrow" />
                                  </svg>
                                </button>
                                <span class="timeline__dot"></span>
                                <span id="item<?php echo $itemCount; ?>-name" class="timeline__meta">
                                  <time class="timeline__date"
                                    datetime="<?php echo date('Y-m-d', strtotime($entry['created_at'])); ?>">
                                    <?php echo date('F j, Y', strtotime($entry['created_at'])); ?>
                                  </time><br>
                                  <strong class="timeline__title"><?php echo htmlspecialchars($entry['action']); ?></strong>
                                </span>
                              </div>
                              <div class="timeline__item-body" id="item<?php echo $itemCount; ?>-ctrld" role="region"
                                aria-labelledby="item<?php echo $itemCount; ?>" aria-hidden="true">
                                <div class="timeline__item-body-content">
                                  <p class="timeline__item-p">
                                    By: <?php echo htmlspecialchars($entry['performed_by']); ?>
                                    <?php if ($entry['details']): ?>
                                      <br><?php echo nl2br(htmlspecialchars($entry['details'])); ?>
                                    <?php endif; ?>
                                  </p>
                                </div>
                              </div>
                            </div>
                            <?php $itemCount++; ?>
                          <?php endforeach; ?>
                        <?php else: ?>
                          <div class="timeline__item">
                            <div class="timeline__item-header">
                              <span class="timeline__dot"></span>
                              <span class="timeline__meta">
                                <strong class="timeline__title">No history available for this ticket.</strong>
                              </span>
                            </div>
                          </div>
                        <?php endif; ?>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="ticket-info">
              <h3>Ticket Information</h3>
              <div class="info-card">
                <div class="info-content">
                  <div class="info-item">
                    <div class="info-icon"><i class="fas fa-user"></i></div>
                    <div class="info-details">
                      <div class="info-label">Repoter</div>
                      <div class="info-email"><?php echo htmlspecialchars($ticket['email'] ?? '--'); ?></div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="info-card">
                <div class="info-content">
                  <div class="info-item">
                    <div class="info-icon"><i class="fas fa-user"></i></div>
                    <div class="info-details">
                      <div class="info-label">Assigned to</div>
                      <div class="assignee">
                        <span class="assignee-name">Unassigned</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="info-card">
                <div class="info-content">
                  <div class="info-item">
                    <div class="info-icon"><i class="fas fa-tag"></i></div>
                    <div class="info-details">
                      <div class="info-label">Status</div>
                      <div class="info-value"><?php echo htmlspecialchars($ticket['status'] ?? '--'); ?></div>
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-icon"><i class="fas fa-tag"></i></div>
                    <div class="info-details">
                      <div class="info-label">Priority</div>
                      <div class="info-value"><?php echo htmlspecialchars($ticket['priority'] ?? '--'); ?></div>
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-icon"><i class="fas fa-comment"></i></div>
                    <div class="info-details">
                      <div class="info-label">Channel</div>
                      <div class="info-value"><?php echo htmlspecialchars($ticket['channel'] ?? '--'); ?></div>
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-icon"><i class="fas fa-tag"></i></div>
                    <div class="info-details">
                      <div class="info-label">Product</div>
                      <div class="info-value"><?php echo htmlspecialchars($ticket['products'] ?? '--'); ?></div>
                    </div>
                  </div>
                  <div class="info-item">
                    <div class="info-icon"><i class="fas fa-tag"></i></div>
                    <div class="info-details">
                      <div class="info-label">Type</div>
                      <div class="info-value"><?php echo htmlspecialchars($ticket['request_type'] ?? '--'); ?></div>
                    </div>
                  </div>

                  <div class="info-item">
                    <div class="info-icon"><i class="fas fa-calendar"></i></div>
                    <div class="info-details">
                      <div class="info-label">Due Date</div>
                      <div class="info-value"><?php echo htmlspecialchars($ticket['due_date'] ?? '--'); ?></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      <?php endif; ?>
    </div>
  </div>

  <script src="../dist/js/TextEditor.js"></script>
  <script>
    new TextEditor("wsyigTicket-32ik45", "texteditor-hidden-Ticket-32ik45");
    new TextEditor("wsyigResolution-32ik45", "texteditor-hidden-resolution-32ik45");
  </script>

  <script>
    const sidebar = document.getElementById('sidebar');
    const resizeHandle = document.getElementById('resize-handle');

    let isResizing = false;

    resizeHandle.addEventListener('mousedown', function(e) {
      isResizing = true;
      document.body.style.cursor = 'ew-resize';
    });

    document.addEventListener('mousemove', function(e) {
      if (!isResizing) return;

      const newWidth = e.clientX;
      if (newWidth >= 200 && newWidth <= 600) {
        sidebar.style.width = newWidth + 'px';
      }
    });

    document.addEventListener('mouseup', function() {
      isResizing = false;
      document.body.style.cursor = 'default';
    });
  </script>


  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const timelineItems = document.querySelectorAll('.timeline__item');

      // Toggle individual item
      timelineItems.forEach(item => {
        const button = item.querySelector('.timeline__arrow');
        const content = item.querySelector('.timeline__item-body');

        if (button && content) {
          button.addEventListener('click', () => {
            const isExpanded = button.getAttribute('aria-expanded') === 'true';
            button.setAttribute('aria-expanded', !isExpanded);
            content.setAttribute('aria-hidden', isExpanded);
            content.style.display = isExpanded ? 'none' : 'block';
          });
        }
      });

      // Expand/Collapse all buttons
      const expandAllBtn = document.querySelector('[data-action="expand"]');
      const collapseAllBtn = document.querySelector('[data-action="collapse"]');

      expandAllBtn.addEventListener('click', () => {
        timelineItems.forEach(item => {
          const button = item.querySelector('.timeline__arrow');
          const content = item.querySelector('.timeline__item-body');
          if (button && content) {
            button.setAttribute('aria-expanded', 'true');
            content.setAttribute('aria-hidden', 'false');
            content.style.display = 'block';
          }
        });
      });

      collapseAllBtn.addEventListener('click', () => {
        timelineItems.forEach(item => {
          const button = item.querySelector('.timeline__arrow');
          const content = item.querySelector('.timeline__item-body');
          if (button && content) {
            button.setAttribute('aria-expanded', 'false');
            content.setAttribute('aria-hidden', 'true');
            content.style.display = 'none';
          }
        });
      });
    });
  </script>
  <script>
    // Sidebar toggle
    document.getElementById('sidebar-toggle').addEventListener('click', () => {
      const sidebar = document.getElementById('sidebar');
      sidebar.classList.toggle('collapsed');
    });

    // Tab switching
    document.querySelectorAll('.tab-button').forEach(button => {
      button.addEventListener('click', () => {
        document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));

        button.classList.add('active');
        document.getElementById(button.getAttribute('data-tab')).classList.add('active');
      });
    });

    // Ensure sidebar is open by default when navigating to a ticket
    window.onload = () => {
      const sidebar = document.getElementById('sidebar');
      const urlParams = new URLSearchParams(window.location.search);
      const ticketId = urlParams.get('ticket_id');

      // If a ticket_id is present in the URL, ensure the sidebar is not collapsed
      if (ticketId) {
        sidebar.classList.remove('collapsed');
      } else {
        // Optionally, collapse the sidebar if no ticket is selected (default behavior)
        sidebar.classList.add('collapsed');
      }
      const conversationsDiv = document.querySelector('.conversations');
      if (conversationsDiv) {
        conversationsDiv.scrollTop = conversationsDiv.scrollHeight;
      }
    };
  </script>
</body>

</html>