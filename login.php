<?php
// echo password_hash('password1', PASSWORD_DEFAULT);

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Client Portal - Login</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        @import url(https://fonts.googleapis.com/css?family=Varela+Round);
        @import 'https://fonts.googleapis.com/css?family=Hind+Madurai:600,700';
        body {
            height: 100vh;
            font-family: 'Varela Round', sans-serif;
            background: linear-gradient(135deg, #1a9e9e 0%, #0d5e5e 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .login-box {
            width: 500px;
            background-color: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(5px);
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            background-color: rgba(73, 175, 175, 0.6);
        }

        h1, h2, h3 {
            color: white;
            text-align: center;
            margin-bottom: 30px;
        }

        .input-group {
            position: relative;
            margin-bottom: 20px;
        }

        .input-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #0d5e5e;
        }

        input {
            width: 89%;
            padding: 12px 15px 12px 40px;
            border: none;
            border-radius: 4px;
            background-color: white;
            font-size: 16px;
            outline: none;
        }

        input::placeholder {
            color: #999;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            background-color: #0d7e7e;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background-color 0.3s;
        }

        .login-btn:hover {
            background-color: #0a6e6e;
        }

        .forgot-password {
            text-align: center;
            margin-top: 15px;
        }

        .forgot-password a {
            color: white;
            text-decoration: none;
            font-size: 14px;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        .footer {
            position: absolute;
            bottom: 20px;
            color: white;
            font-size: 14px;
            text-align: center;
        }

        @media (max-width: 480px) {
            .login-box {
                width: 90%;
                padding: 30px;
            }
        }
    </style>
</head>

<body>
    <!-- <div class="login-container">
        <h2>Client Portal Login</h2>
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit">Login</button>
        </form>
        <p id="error-message" class="error"></p>
    </div> -->

    <div class="container">
        <div class="login-box">
            <h1>Celaeno Technology</h1>
            <h2>Login</h2>
            <form id="loginForm">
                <div class="input-group">
                    <i class="fas fa-user"></i>
                    <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
                </div>
                <div class="input-group">
                    <i class="fas fa-key"></i>
                    <input type="password" id="password" name="password" placeholder="*******" required>
                </div>
                <button type="submit" class="login-btn">Login</button>
            </form>
        </div>
        <div class="footer">
            <p>Copyright © 2025, All Right Reserved Celaeno Technology</p>
        </div>
    </div>

    <script src="clientscript.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', function (e) {
            e.preventDefault();
            login();
        });
    </script>
</body>

</html>