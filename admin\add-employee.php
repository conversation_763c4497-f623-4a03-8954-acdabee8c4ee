<?php
session_start();
include('includes/config.php');
error_reporting(0);
if(strlen($_SESSION['login'])==0)
{ 
    header('location:index.php');
}
else{
    if(isset($_POST['submitemployee']))
    {
        $employeeName=$_POST['employee_name'];
        $employeeEmail=$_POST['employee_email'];
        $manager= intval($_POST['manager']);
        $position=$_POST['position'];
        $createdDate = new Date();
        $status=1;

        echo "INSERT INTO `tblemployees`(`employee_name`, `company_id`, `employee_email`, `password`, `status`, `manager_id`, `position`, `created_by`, `created_date`) VALUES ('$employeeName', 8, '$employeeEmail', '123', $status, $manager, '$position', 40, '$createdDate')";

        $query = mysqli_query($con,"INSERT INTO `tblemployees`(`employee_name`, `company_id`, `employee_email`, `password`, `status`, `manager_id`, `position`, `created_by`, `created_date`) VALUES ('$employeeName', 8, '$employeeEmail', '123', $status, $manager, '$position', 40, '$createdDate')");


        if($query)
        {
            $msg = "Employee added successfully ";
        }
        else{
            $error="Something went wrong . Please try again.";    
        }
    }

?>


<!DOCTYPE html>
<html lang="en">

<head>

    <title>Celaeno Technology | Add Employee</title>

    <!-- App css -->
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../plugins/switchery/switchery.min.css">
    <script src="assets/js/modernizr.min.js"></script>

</head>


<body class="fixed-left">

    <!-- Begin page -->
    <div id="wrapper">

        <!-- Top Bar Start -->
        <?php include('includes/topheader.php');?>
        <!-- Top Bar End -->


        <!-- ========== Left Sidebar Start ========== -->
        <?php include('includes/leftsidebar.php');?>
        <!-- Left Sidebar End -->

        <div class="content-page">
            <!-- Start content -->
            <div class="content">
                <div class="container">


                    <div class="row">
                        <div class="col-xs-12">
                            <div class="page-title-box">
                                <h4 class="page-title">Add Employee</h4>
                                <ol class="breadcrumb p-0 m-0">
                                    <li>
                                        <a href="#">Admin</a>
                                    </li>
                                    <li>
                                        <a href="manage-resources.php">Resources</a>
                                    </li>
                                    <li class="active">
                                        Add Employee
                                    </li>
                                </ol>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>
                    <!-- end row -->


                    <div class="row">
                        <div class="col-sm-12">
                            <div class="card-box">
                                <h4 class="m-t-0 header-title"><b>Add Employee </b></h4>
                                <hr />



                                <div class="row">
                                    <div class="col-sm-6">
                                        <!---Success Message--->
                                        <?php if($msg){ ?>
                                        <div class="alert alert-success" role="alert">
                                            <strong>Well done!</strong> <?php echo htmlentities($msg);?>
                                        </div>
                                        <?php } ?>

                                        <!---Error Message--->
                                        <?php if($error){ ?>
                                        <div class="alert alert-danger" role="alert">
                                            <strong>Oh snap!</strong> <?php echo htmlentities($error);?>
                                        </div>
                                        <?php } ?>


                                    </div>
                                </div>


                                <div class="row">
                                    <div class="col-md-6">
                                        <form class="form-horizontal" name="employee" method="post">
                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Employee Name</label>
                                                <div class="col-md-10">
                                                    <input type="text" class="form-control" value="" name="employee_name"
                                                        required>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Email</label>
                                                <div class="col-md-10">
                                                    <input type="text" class="form-control" value="" name="employee_email"
                                                        required>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Position</label>
                                                <div class="col-md-10">
                                                    <input type="text" class="form-control" value="" name="position"
                                                        required>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Manager</label>
                                                <div class="col-md-10">
                                                    <select class="form-control" name="manager" required>
                                                        <option value="">Select Manager </option>
                                                        <?php
                                                            // Feching active categories
                                                            $ret = mysqli_query($con,"SELECT employee_id, employee_name FROM tblemployees WHERE status=1");
                                                            while($result=mysqli_fetch_array($ret))
                                                            {
                                                        ?>
                                                        <option value="<?php echo htmlentities($result['employee_id']);?>">
                                                            <?php echo htmlentities($result['employee_name']);?>
                                                        </option>
                                                        <?php } ?>

                                                    </select>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-2 control-label">&nbsp;</label>
                                                <div class="col-md-10">

                                                    <button type="submit"
                                                        class="btn btn-custom waves-effect waves-light btn-md"
                                                        name="submitemployee">
                                                        Submit
                                                    </button>
                                                </div>
                                            </div>

                                        </form>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <!-- end row -->


                </div> <!-- container -->

            </div> <!-- content -->

            <?php include('includes/footer.php');?>

        </div>


    </div>
    <!-- END wrapper -->



    <script>
    var resizefunc = [];
    </script>

    <!-- jQuery  -->
    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/detect.js"></script>
    <script src="assets/js/fastclick.js"></script>
    <script src="assets/js/jquery.blockUI.js"></script>
    <script src="assets/js/waves.js"></script>
    <script src="assets/js/jquery.slimscroll.js"></script>
    <script src="assets/js/jquery.scrollTo.min.js"></script>
    <script src="../plugins/switchery/switchery.min.js"></script>

    <!-- App js -->
    <script src="assets/js/jquery.core.js"></script>
    <script src="assets/js/jquery.app.js"></script>

</body>

</html>
<?php } ?>