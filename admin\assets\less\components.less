@import "variables.less";
@import "elements.less";

/*
Template Name: Zircos Dashboard
Author: CoderThemes
Email: <EMAIL>
File: Components
*/

/* =============
  == Components List==

   - Popover / Tooltips
   - Buttons
   - Panels
   - Portlets
   - Checkbox and radio
   - Modals
   - Tabs
   - Progressbars
   - Notifications
   - Alerts
   - Carousel
   - Sweet Alert
   - Widgets
   - Nestable
   - Rating
   - Calendar
   - Form
   - Tables
   - Charts

============= */


/* =================
   Popover / Tooltips
==================== */
/* Popover */
.popover {
  font-family: inherit;
  border: none;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  -moz-border-radius: 3px;
  background-clip: padding-box;
  box-shadow: 0 0 28px rgba(0, 0, 0, 0.15);

  .popover-title {
    background-color: transparent;
    color: @custom;
    padding: 12px 15px;
    font-size: 15px;
  }

  .arrow {
    border-color: transparent !important;
  }
}

/* Tooltips */
.tooltip {
  font-family: @font-secondary;

  .tooltip-inner {
    padding: 4px 10px;
    border-radius: 2px;
    background-color: @dark;
  }
}

.tooltip.left .tooltip-arrow {
  border-left-color: @dark;
}

.tooltip.top .tooltip-arrow {
  border-top-color: @dark;
}

.tooltip.bottom .tooltip-arrow {
  border-bottom-color: @dark;
}

.tooltip.right .tooltip-arrow {
  border-right-color: @dark;
}

/* Tooltpster */
.tooltipster-sidetip .tooltipster-box {
  background-color: @custom;
  border: 2px solid @custom;
}

.tooltipster-sidetip.tooltipster-top .tooltipster-arrow-border,
.tooltipster-sidetip.tooltipster-top .tooltipster-arrow-background{
  border-top-color: @custom;
}

.tooltipster-sidetip.tooltipster-bottom .tooltipster-arrow-border,
.tooltipster-sidetip.tooltipster-bottom .tooltipster-arrow-background{
  border-bottom-color: @custom;
}

.tooltipster-sidetip.tooltipster-left .tooltipster-arrow-border,
.tooltipster-sidetip.tooltipster-left .tooltipster-arrow-background{
  border-left-color: @custom;
}

.tooltipster-sidetip.tooltipster-right .tooltipster-arrow-border,
.tooltipster-sidetip.tooltipster-right .tooltipster-arrow-background{
  border-right-color: @custom;
}


/* =============
   Buttons
============= */
.btn {
  border-radius: 2px;
  padding: 6px 14px;
}

.btn-md {
  padding: 8px 18px;
}

.btn-group-lg>.btn, .btn-lg {
  padding: 10px 16px !important;
  font-size: 16px;
}
.btn-group-sm>.btn, .btn-sm {
  padding: 5px 10px !important;
}
.btn-group-xs>.btn, .btn-xs {
  padding: 1px 5px !important;
}
.btn-group .btn+.btn, .btn-group .btn+.btn-group,
.btn-group .btn-group+.btn, .btn-group .btn-group+.btn-group {
  margin-left: 0px;
}
.btn-group.open .dropdown-toggle {
  -webkit-box-shadow: 0 0 0 100px rgba(0,0,0,.1) inset;
  box-shadow: 0 0 0 100px rgba(0,0,0,.1) inset;
}
.btn-custom,.btn-primary, .btn-success, .btn-info, .btn-warning,
.btn-danger, .btn-inverse, .btn-purple, .btn-pink, .btn-orange,
.btn-brown,.btn-teal{
  color: @white !important;
}

.btn-custom {
  background-color: @custom;
  border-color: @custom;
}
.btn-custom:hover, .btn-custom:focus, .btn-custom:active, .btn-custom.active,
.btn-custom.focus, .btn-custom:active, .btn-custom:focus, .btn-custom:hover,
.open > .dropdown-toggle.btn-custom  {
  background-color: darken(@custom, 5%) !important;
  border: 1px solid darken(@custom, 5%) !important;
}

.btn-default {
  background-color: @white;
  border-color: fade(@dark,20%);
}
.btn-default:hover, .btn-default:focus, .btn-default:active, .btn-default.active,
.btn-default.focus, .btn-default:active, .btn-default:focus, .btn-default:hover,
.open > .dropdown-toggle.btn-default  {
  background-color: fade(@dark, 7%) !important;
  border: 1px solid fade(@dark, 20%) !important;
}

.btn-primary{
  background-color: @primary !important;
  border: 1px solid @primary !important;
}
.btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active,
.btn-primary.focus, .btn-primary:active, .btn-primary:focus, .btn-primary:hover,
.open > .dropdown-toggle.btn-primary  {
  background-color: darken(@primary, 5%) !important;
  border: 1px solid darken(@primary, 5%) !important;
}

.btn-success {
  background-color: @success !important;
  border: 1px solid @success !important;
}
.btn-success:hover, .btn-success:focus, .btn-success:active, .btn-success.active, .btn-success.focus, .btn-success:active, .btn-success:focus, .btn-success:hover, .open > .dropdown-toggle.btn-success {
  background-color: darken(@success, 5%) !important;
  border: 1px solid darken(@success, 5%) !important;
}

.btn-info {
  background-color: @info !important;
  border: 1px solid @info !important;
}
.btn-info:hover, .btn-info:focus, .btn-info:active, .btn-info.active, .btn-info.focus, .btn-info:active, .btn-info:focus, .btn-info:hover, .open > .dropdown-toggle.btn-info {
  background-color: darken(@info, 5%) !important;
  border: 1px solid darken(@info, 5%) !important;
}

.btn-warning {
  background-color: @warning !important;
  border: 1px solid @warning !important;
}
.btn-warning:hover, .btn-warning:focus, .btn-warning:active, .btn-warning.active, .btn-warning.focus, .btn-warning:active, .btn-warning:focus, .btn-warning:hover, .open > .dropdown-toggle.btn-warning {
  background-color: darken(@warning, 5%) !important;
  border: 1px solid darken(@warning, 5%) !important;
}

.btn-danger {
  background-color: @danger !important;
  border: 1px solid @danger !important;
}
.btn-danger:active, .btn-danger:focus, .btn-danger:hover, .btn-danger.active, .btn-danger.focus, .btn-danger:active, .btn-danger:focus, .btn-danger:hover, .open > .dropdown-toggle.btn-danger {
  background-color: darken(@danger, 5%) !important;
  border: 1px solid darken(@danger, 5%) !important;
}

.btn-inverse {
  background-color: @inverse !important;
  border: 1px solid @inverse !important;
}
.btn-inverse:hover, .btn-inverse:focus, .btn-inverse:active, .btn-inverse.active, .btn-inverse.focus, .btn-inverse:active, .btn-inverse:focus, .btn-inverse:hover, .open > .dropdown-toggle.btn-inverse {
  background-color: darken(@inverse, 5%) !important;
  border: 1px solid darken(@inverse, 5%) !important;
}

.btn-purple {
  background-color: @purple !important;
  border: 1px solid @purple !important;
}
.btn-purple:hover, .btn-purple:focus, .btn-purple:active {
  background-color: darken(@purple, 5%) !important;
  border: 1px solid darken(@purple, 5%) !important;
}

.btn-pink {
  background-color: @pink !important;
  border: 1px solid @pink !important;
}
.btn-pink:hover, .btn-pink:focus, .btn-pink:active {
  background-color: darken(@pink, 5%) !important;
  border: 1px solid darken(@pink, 5%) !important;
}

.btn-orange {
  background-color: @orange !important;
  border: 1px solid @orange !important;
}
.btn-orange:hover, .btn-orange:focus, .btn-orange:active {
  background-color: darken(@orange, 5%) !important;
  border: 1px solid darken(@orange, 5%) !important;
}

.btn-brown {
  background-color: @brown !important;
  border: 1px solid @brown !important;
}
.btn-brown:hover, .btn-brown:focus, .btn-brown:active {
  background-color: darken(@brown, 5%) !important;
  border: 1px solid darken(@brown, 5%) !important;
}

.btn-teal {
  background-color: @teal !important;
  border: 1px solid @teal !important;
}
.btn-teal:hover, .btn-teal:focus, .btn-teal:active {
  background-color: darken(@teal, 5%) !important;
  border: 1px solid darken(@teal, 5%) !important;
}


.btn-bordered {
  border-bottom: 3px solid transparent;
}

.btn-bordered.btn-default {
  background-color: @white;
  border-bottom: 2px solid fade(@dark,10%) !important;
}
.btn-bordered.btn-custom {
  background-color: @custom;
  border-bottom: 2px solid darken(@custom, 7%) !important;
}

.btn-bordered.btn-primary {
  border-bottom: 2px solid darken(@primary, 7%) !important;
}

.btn-bordered.btn-success {
  border-bottom: 2px solid darken(@success, 7%) !important;
}

.btn-bordered.btn-info {
  border-bottom: 2px solid darken(@info, 7%) !important;
}

.btn-bordered.btn-warning {
  border-bottom: 2px solid darken(@warning, 10%) !important;
}

.btn-bordered.btn-danger {
  border-bottom: 2px solid darken(@danger, 10%) !important;
}

.btn-bordered.btn-inverse {
  border-bottom: 2px solid darken(@inverse, 20%) !important;
}

.btn-bordered.btn-purple {
  border-bottom: 2px solid darken(@purple, 7%) !important;
}

.btn-bordered.btn-pink {
  border-bottom: 2px solid darken(@pink, 7%) !important;
}

.btn-bordered.btn-orange {
  border-bottom: 2px solid darken(@orange, 7%) !important;
}

.btn-bordered.btn-brown {
  border-bottom: 2px solid darken(@brown, 7%) !important;
}

.btn-bordered.btn-teal {
  border-bottom: 2px solid darken(@teal, 7%) !important;
}

.btn-rounded {
  border-radius: 2em;
  padding: 6px 18px;
}

/* Social Buttons */
.btn-facebook {
  color: @white !important;
  background-color: #3b5998 !important;
}
.btn-twitter {
  color: @white !important;
  background-color: #00aced !important;
}
.btn-linkedin {
  color: @white !important;
  background-color: #007bb6 !important;
}
.btn-dribbble {
  color: @white !important;
  background-color: #ea4c89 !important;
}
.btn-googleplus {
  color: @white !important;
  background-color: #dd4b39 !important;
}
.btn-instagram {
  color: @white !important;
  background-color: #517fa4 !important;
}
.btn-pinterest {
  color: @white !important;
  background-color: #cb2027 !important;
}
.btn-dropbox {
  color: @white !important;
  background-color: #007ee5 !important;
}
.btn-flickr {
  color: @white !important;
  background-color: #ff0084 !important;
}
.btn-tumblr {
  color: @white !important;
  background-color: #32506d !important;
}
.btn-skype {
  color: @white !important;
  background-color: #00aff0 !important;
}
.btn-youtube {
  color: @white !important;
  background-color: #bb0000 !important;
}
.btn-github {
  color: @white !important;
  background-color: #171515 !important;
}



/* ===========
   Panels
 =============*/

.panel {
  border: 2px solid @light3;
  box-shadow: none;
  margin-bottom: 20px;
  .panel-body {
    padding: 20px;

    p{
      margin-bottom: 0;
      line-height: 24px;
    }

    p + p {
      padding-top: 10px;
    }
  }
}

.panel-heading {
  border: none !important;
  padding: 15px 20px;
  margin: -2px;
  border-radius: 4px 4px 0 0;
}

.panel-default > .panel-heading {
  background-color: @light3;
  border-bottom: none;
  color: @light7;
}

.panel-title {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 0;
  margin-top: 0;
  text-transform: uppercase;
  letter-spacing: 0.03em;
  font-family: @font-secondary;
}

.panel-sub-title {
  margin-bottom: 0px;
  color: fade(@white,70%) !important;
  margin-top: 3px;
}

.panel-footer {
  background: @light3;
  border-top: 0;
}

.panel-default .panel-sub-title {
  color: inherit !important;
}
.panel-color {
  .panel-title {
    color: @white;
  }
}

.panel-primary > .panel-heading {
  background-color: @primary;
}

.panel-success > .panel-heading {
  background-color: @success;
}

.panel-info > .panel-heading {
  background-color: @info;
}

.panel-warning > .panel-heading {
  background-color: @warning;
}

.panel-danger > .panel-heading {
  background-color: @danger;
}

.panel-purple > .panel-heading {
  background-color: @purple;
}

.panel-pink > .panel-heading {
  background-color: @pink;
}

.panel-orange > .panel-heading {
  background-color: @orange;
}

.panel-brown > .panel-heading {
  background-color: @brown;
}

.panel-teal > .panel-heading {
  background-color: @teal;
}

.panel-inverse > .panel-heading {
  background-color: @inverse;
}

.panel-border {
  border-radius: 3px;
}

.panel-border .panel-heading {
  background-color: @white;
  border-top: 3px solid #ccc !important;
  border-radius: 3px;
  padding: 15px 20px 0;
  margin: -2px 0 0 0 !important;
}

.panel-border .panel-body {
  padding: 15px 20px 20px 20px;
}

.panel-border.panel-primary .panel-heading {
  border-color: @primary !important;
  color: @primary !important;
}

.panel-border.panel-success .panel-heading {
  border-color: @success !important;
  color: @success !important;
}

.panel-border.panel-info .panel-heading {
  border-color: @info !important;
  color: @info !important;
}

.panel-border.panel-warning .panel-heading {
  border-color: @warning !important;
  color: @warning !important;
}

.panel-border.panel-danger .panel-heading {
  border-color: @danger !important;
  color: @danger !important;
}

.panel-border.panel-purple .panel-heading {
  border-color: @purple !important;
  color: @purple !important;
}

.panel-border.panel-pink .panel-heading {
  border-color: @pink !important;
  color: @pink !important;
}

.panel-border.panel-orange .panel-heading {
  border-color: @orange !important;
  color: @orange !important;
}

.panel-border.panel-brown .panel-heading {
  border-color: @brown !important;
  color: @brown !important;
}

.panel-border.panel-teal .panel-heading {
  border-color: @teal !important;
  color: @teal !important;
}

.panel-border.panel-inverse .panel-heading {
  border-color: @inverse !important;
  color: @inverse !important;
}

.panel-default>.panel-heading+.panel-collapse>.panel-body {
  border-top: 0 !important;
}




/* ===========
   Portlets
 =============*/

.portlet {
  background: @white;
  border-radius: 3px;
  margin-bottom: 20px;
  transition: all 0.4s;
  border: 2px solid @light3;

  .portlet-heading {
    -webkit-border-radius: 3px 3px 0 0;
    border-radius: 3px 3px 0 0;
    -moz-border-radius: 3px 3px 0 0;
    color: @white;
    padding: 12px 20px;
    margin: -2px -2px 0 -2px;

    .portlet-title {
      color: @white;
      float: left;
      font-size: 15px;
      font-weight: 600;
      margin-bottom: 0;
      margin-top: 9px;
      text-transform: uppercase;
      letter-spacing: 0.03em;
      font-family: @font-secondary;
    }
    .portlet-widgets {
      display: inline-block;
      float: right;
      font-size: 15px;
      line-height: 30px;
      padding-left: 15px;
      position: relative;
      text-align: right;
      .divider {
        margin: 0 5px;
      }
    }
    a {
      color: #999999;
    }
  }
  .portlet-body {
    -moz-border-radius-bottomleft: 5px;
    -moz-border-radius-bottomright: 5px;
    -webkit-border-bottom-left-radius: 5px;
    -webkit-border-bottom-right-radius: 5px;
    background: @white;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    padding: 15px;
  }
}

.portlet-default {
  .portlet-title {
    color: @light7 !important;
  }
}
.portlet .portlet-heading .portlet-widgets .collapsed .ion-minus-round:before {
    content: "\f217" !important;
}

.portlet .portlet-heading.bg-purple a, .portlet .portlet-heading.bg-info a,
.portlet .portlet-heading.bg-success a, .portlet .portlet-heading.bg-primary a,
.portlet .portlet-heading.bg-danger a, .portlet .portlet-heading.bg-warning a,
.portlet .portlet-heading.bg-inverse a, .portlet .portlet-heading.bg-pink a,
.portlet .portlet-heading.bg-orange a,.portlet .portlet-heading.bg-brown a,
.portlet .portlet-heading.bg-teal a{
  color: @white;
}

.panel-disabled {
  background: fade(@dark,50%);
  cursor: progress;
  bottom: 20px;
  border-radius: 3px;
  left: 10px;
  position: absolute;
  right: 10px;
  top: 0;
}

.portlet-loader {
  width: 30px;
  height: 30px;
  background-color: @dark;
  border-radius: 2px;
  -webkit-animation: sk-rotateplane 1.2s infinite ease-in-out;
  animation: sk-rotateplane 1.2s infinite ease-in-out;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -15px;
  margin-top: -15px;
}

@-webkit-keyframes sk-rotateplane {
  0% {
    -webkit-transform: perspective(120px);
  }
  50% {
    -webkit-transform: perspective(120px) rotateY(180deg);
  }
  100% {
    -webkit-transform: perspective(120px) rotateY(180deg) rotateX(180deg);
  }
}
@keyframes sk-rotateplane {
  0% {
    transform: perspective(120px) rotateX(0deg) rotateY(0deg);
    -webkit-transform: perspective(120px) rotateX(0deg) rotateY(0deg);
  }
  50% {
    transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);
    -webkit-transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);
  }
  100% {
    transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
    -webkit-transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
  }
}


/* ==== Draggable card ==== */
.card-draggable-placeholder {
  border: 2px dashed rgba(67, 89, 102, 0.5);
  margin-bottom: 16px;
  background-color: rgba(67, 89, 102, 0.08);
}


/* =============
   Checkbox and Radios
============= */

.checkbox {
  padding-left: 20px;
  label {
    display: inline-block;
    padding-left: 5px;
    position: relative;
    font-family: @font-primary;
    font-weight: normal;

    &::before {
      -o-transition: 0.3s ease-in-out;
      -webkit-transition: 0.3s ease-in-out;
      background-color: @white;
      border-radius: 2px;
      border: 1px solid darken(@light3,10%);
      content: "";
      display: inline-block;
      height: 17px;
      left: 0;
      margin-left: -20px;
      position: absolute;
      transition: 0.3s ease-in-out;
      width: 17px;
      outline: none !important;
      margin-top: 2px;
    }
    &::after {
      color: @light7;
      display: inline-block;
      font-size: 11px;
      height: 16px;
      left: 0;
      margin-left: -20px;
      padding-left: 3px;
      padding-top: 1px;
      position: absolute;
      top: 2px;
      width: 16px;
    }
  }
  input[type="checkbox"] {
    cursor: pointer;
    opacity: 0;
    z-index: 1;
    outline: none !important;

    &:disabled + label {
      opacity: 0.65;
    }
  }
  input[type="checkbox"]:focus + label {
    &::before {
      outline-offset: -2px;
      outline: none;
    }
  }
  input[type="checkbox"]:checked + label {
    &::after {
      content: "\F12C";
      font-family: 'Material Design Icons';
      font-weight: bold;
    }
  }
  input[type="checkbox"]:disabled + label {
    &::before {
      background-color: @light;
      cursor: not-allowed;
    }
  }
}

.checkbox.checkbox-circle {
  label {
    &::before {
      border-radius: 50%;
    }
  }
}

.checkbox.checkbox-inline {
  margin-top: 0;
}

.checkbox.checkbox-single {
  label {
    height: 17px;
  }
}

.checkbox-custom {
  input[type="checkbox"]:checked + label {
    &::before {
      background-color: @custom;
      border-color: @custom;
    }
    &::after {
      color: @white;
    }
  }
}

.checkbox-primary {
  input[type="checkbox"]:checked + label {
    &::before {
      background-color: @primary;
      border-color: @primary;
    }
    &::after {
      color: @white;
    }
  }
}

.checkbox-danger {
  input[type="checkbox"]:checked + label {
    &::before {
      background-color: @danger;
      border-color: @danger;
    }
    &::after {
      color: @white;
    }
  }
}

.checkbox-info {
  input[type="checkbox"]:checked + label {
    &::before {
      background-color: @info;
      border-color: @info;
    }
    &::after {
      color: @white;
    }
  }
}

.checkbox-warning {
  input[type="checkbox"]:checked + label {
    &::before {
      background-color: @warning;
      border-color: @warning;
    }
    &::after {
      color: @white;
    }
  }
}

.checkbox-success {
  input[type="checkbox"]:checked + label {
    &::before {
      background-color: @success;
      border-color: @success;
    }
    &::after {
      color: @white;
    }
  }
}

.checkbox-purple {
  input[type="checkbox"]:checked + label {
    &::before {
      background-color: @purple;
      border-color: @purple;
    }
    &::after {
      color: @white;
    }
  }
}

.checkbox-pink {
  input[type="checkbox"]:checked + label {
    &::before {
      background-color: @pink;
      border-color: @pink;
    }
    &::after {
      color: @white;
    }
  }
}

.checkbox-inverse {
  input[type="checkbox"]:checked + label {
    &::before {
      background-color: @inverse;
      border-color: @inverse;
    }
    &::after {
      color: @white;
    }
  }
}

/* Radios */

.radio {
  padding-left: 20px;
  label {
    display: inline-block;
    padding-left: 5px;
    position: relative;
    font-family: @font-primary;
    font-weight: normal;

    &::before {
      -o-transition: border 0.5s ease-in-out;
      -webkit-transition: border 0.5s ease-in-out;
      background-color: @white;
      border-radius: 50%;
      border: 1px solid darken(@light3,10%);
      content: "";
      display: inline-block;
      height: 17px;
      left: 0;
      margin-left: -20px;
      outline: none !important;
      position: absolute;
      transition: border 0.5s ease-in-out;
      width: 17px;
    }
    &::after {
      -moz-transition: -moz-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
      -ms-transform: scale(0, 0);
      -o-transform: scale(0, 0);
      -o-transition: -o-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
      -webkit-transform: scale(0, 0);
      -webkit-transition: -webkit-transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
      background-color: @light7;
      border-radius: 50%;
      content: " ";
      display: inline-block;
      height: 11px;
      left: 3px;
      margin-left: -20px;
      position: absolute;
      top: 3px;
      transform: scale(0, 0);
      transition: transform 0.1s cubic-bezier(0.8, -0.33, 0.2, 1.33);
      width: 11px;
    }
  }
  input[type="radio"] {
    cursor: pointer;
    opacity: 0;
    z-index: 1;
    outline: none !important;
    &:disabled + label {
      opacity: 0.65;
    }
  }
  input[type="radio"]:focus + label {
    &::before {
      outline-offset: -2px;
    }
  }
  input[type="radio"]:checked + label {
    &::after {
      -ms-transform: scale(1, 1);
      -o-transform: scale(1, 1);
      -webkit-transform: scale(1, 1);
      transform: scale(1, 1);
    }
  }
  input[type="radio"]:disabled + label {
    &::before {
      cursor: not-allowed;
    }
  }
}

.radio.radio-inline {
  margin-top: 0;
}

.radio.radio-single {
  label {
    height: 17px;
  }
}


.radio-custom {
  input[type="radio"] + label {
    &::after {
      background-color: @custom;
    }
  }
  input[type="radio"]:checked + label {
    &::before {
      border-color: @custom;
    }
    &::after {
      background-color: @custom;
    }
  }
}

.radio-primary {
  input[type="radio"] + label {
    &::after {
      background-color: @primary;
    }
  }
  input[type="radio"]:checked + label {
    &::before {
      border-color: @primary;
    }
    &::after {
      background-color: @primary;
    }
  }
}

.radio-danger {
  input[type="radio"] + label {
    &::after {
      background-color: @danger;
    }
  }
  input[type="radio"]:checked + label {
    &::before {
      border-color: @danger;
    }
    &::after {
      background-color: @danger;
    }
  }
}

.radio-info {
  input[type="radio"] + label {
    &::after {
      background-color: @info;
    }
  }
  input[type="radio"]:checked + label {
    &::before {
      border-color: @info;
    }
    &::after {
      background-color: @info;
    }
  }
}

.radio-warning {
  input[type="radio"] + label {
    &::after {
      background-color: @warning;
    }
  }
  input[type="radio"]:checked + label {
    &::before {
      border-color: @warning;
    }
    &::after {
      background-color: @warning;
    }
  }
}

.radio-success {
  input[type="radio"] + label {
    &::after {
      background-color: @success;
    }
  }
  input[type="radio"]:checked + label {
    &::before {
      border-color: @success;
    }
    &::after {
      background-color: @success;
    }
  }
}

.radio-purple {
  input[type="radio"] + label {
    &::after {
      background-color: @purple;
    }
  }
  input[type="radio"]:checked + label {
    &::before {
      border-color: @purple;
    }
    &::after {
      background-color: @purple;
    }
  }
}

.radio-pink {
  input[type="radio"] + label {
    &::after {
      background-color: @pink;
    }
  }
  input[type="radio"]:checked + label {
    &::before {
      border-color: @pink;
    }
    &::after {
      background-color: @pink;
    }
  }
}

/* =============
   Modals
============= */

.modal {
  .modal-dialog {
    .modal-content {
      -moz-box-shadow: none;
      -webkit-box-shadow: none;
      border-color: #DDDDDD;
      border-radius: 2px;
      box-shadow: none;
      padding: 25px;
      .modal-header {
        border-bottom-width: 2px;
        margin: 0;
        padding: 0;
        padding-bottom: 15px;
      }
      .modal-body {
        padding: 20px 0;
      }
      .modal-footer {
        padding: 0;
        padding-top: 15px;
      }
    }
  }
}

.modal-full {
  width: 98%;
}

.modal-content {
  .nav.nav-tabs + .tab-content {
    margin-bottom: 0;
  }
  .panel-group {
    margin-bottom: 0;
  }
  .panel {
    border-top: none;
  }
}

/* Custom-modal */

.modal-demo {
  background-color: @white;
  width: 600px;
  .border-radius(4px);
  display: none;

  .close {
    position: absolute;
    top: 15px;
    right: 25px;
    color: @light;
  }
}

.custom-modal-title {
  padding: 15px 25px 15px 25px;
  line-height: 22px;
  font-size: 18px;
  background-color: @dark;
  color: @white;
  text-align: left;
  margin: 0;
}

.custom-modal-text {
  padding: 20px;
}

.custombox-modal-flash, .custombox-modal-rotatedown {
  .close {
    top: 20px;
    z-index: 9999;
  }
}




/* =============
   Tabs
============= */

.tab-content {
  padding: 20px 0 0 0;
}

.nav-tabs>li>a {
  color: @dark;
  text-transform: uppercase;
  font-weight: 600;
  font-family: @font-secondary;

  &:hover {
    background-color: fade(@muted,10%);
  }
}

.nav-tabs>li.active>a, .nav-tabs>li.active>a:focus, .nav-tabs>li.active>a:hover {
  color: @custom;
}

/* Vertial tab */
.tabs-vertical-env {

  .tab-content {
    background: @white;
    display: table-cell;
    padding: 0 0 0 20px;
    vertical-align: top;
  }
  .nav.tabs-vertical {
    display: table-cell;
    min-width: 120px;
    vertical-align: top;
    width: 150px;

    li > a {
      color: @dark;
      text-align: center;
      white-space: nowrap;
      font-weight: 600;
      font-family: @font-secondary;
      text-transform: uppercase;
    }

    li.active > a {
      background-color: fade(@muted,10%);
      border: 0;
      color: @custom;
    }

  }
}

.tabs-vertical-env-right {
  .tab-content {
    padding: 0 20px 0 0;
  }
}

.tabs-bordered {
  border-bottom: 2px solid fade(@muted,20%) !important;
}

.tabs-bordered li a, .tabs-bordered li a:hover, .tabs-bordered li a:focus {
  border: 0 !important;
  background-color: @white !important;
  padding: 10px 20px !important;
}
.tabs-bordered li.active a, .tabs-bordered li.active a:hover, .tabs-bordered li.active a:focus {
    border-bottom: 2px solid @custom !important;
    margin-bottom: -1px;
    color: @custom;
}

/* Navpills */
.nav-pills>li>a {
  color: @dark;
}
.nav-pills>li.active>a, .nav-pills>li.active>a:focus, .nav-pills>li.active>a:hover {
  background-color: @custom;
}


/* =============
   Progressbars
============= */


.progress {
  -webkit-box-shadow: none !important;
  background-color: @light3;
  box-shadow: none !important;
  height: 10px;
  margin-bottom: 18px;
  overflow: hidden;
}

.progress-bar {
  box-shadow: none;
  font-size: 8px;
  font-weight: 600;
  line-height: 12px;
}

.progress.progress-sm {
  height: 5px !important;
  .progress-bar {
    font-size: 8px;
    line-height: 5px;
  }
}

.progress.progress-md {
  height: 15px !important;
  .progress-bar {
    font-size: 10.8px;
    line-height: 14.4px;
  }
}

.progress.progress-lg {
  height: 20px !important;
  .progress-bar {
    font-size: 12px;
    line-height: 20px;
  }
}

.progress-bar-primary {
  background-color: @primary;
}

.progress-bar-success {
  background-color: @success;
}

.progress-bar-info {
  background-color: @info;
}

.progress-bar-warning {
  background-color: @warning;
}

.progress-bar-danger {
  background-color: @danger;
}

.progress-bar-inverse {
  background-color: @inverse;
}

.progress-bar-purple {
  background-color: @purple;
}

.progress-bar-pink {
  background-color: @pink;
}

.progress-bar-custom {
  background-color: @custom;
}

.progress-bar-orange {
  background-color: @orange;
}

.progress-bar-brown {
  background-color: @brown;
}

.progress-bar-teal {
  background-color: @teal;
}


/* Progressbar Vertical */
.progress-vertical {
    min-height: 250px;
    height: 250px;
    width: 10px;
    position: relative;
    display: inline-block;
    margin-bottom: 0;
    margin-right: 20px;

    .progress-bar {
      width: 100%;
   }
}
.progress-vertical-bottom{
    min-height: 250px;
    height: 250px;
    position: relative;
    width: 10px;
    display: inline-block;
    margin-bottom: 0;
    margin-right: 20px;

    .progress-bar {
      width: 100%;
      position: absolute;
      bottom: 0;
   }
}

.progress-vertical.progress-sm,.progress-vertical-bottom.progress-sm {
  width: 5px !important;
  .progress-bar {
    font-size: 8px;
    line-height: 5px;
  }
}

.progress-vertical.progress-md,.progress-vertical-bottom.progress-md {
  width: 15px !important;
  .progress-bar {
    font-size: 10.8px;
    line-height: 14.4px;
  }
}

.progress-vertical.progress-lg,.progress-vertical-bottom.progress-lg {
  width: 20px !important;
  .progress-bar {
    font-size: 12px;
    line-height: 20px;
  }
}



/* =============
   Notification
============= */
#toast-container > div {
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
  opacity: 1;
}
#toast-container > :hover {
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
  opacity: 0.98;
}
.toast {
  background-color: @custom;
}
.toast-success {
  background-color: @success;
  border: 2px solid @success;
}
.toast-error {
  background-color: @danger;
  border: 2px solid @danger;
}
.toast-info {
  background-color: @info;
  border: 2px solid @info;
}
.toast-warning {
  background-color: @warning;
  border: 2px solid @warning;
}

/* =============
   Alerts
============= */
.alert {
  position: relative;

  .alert-link {
    font-weight: 600;
    font-family: @font-secondary;
  }
}

.alert-icon {
  padding-left: 50px;

  i {
    position: absolute;
    left: 0;
    height: 50px;
    width: 50px;
    text-align: center;
    top: 0;
    line-height: 50px;
    font-size: 22px;
  }
}

.alert-success {
  color: @success;
  background-color: lighten(@success,35%);
  border-color: lighten(@success,20%);
  .alert-link {
    color: darken(@success,10%);
  }
  hr {
    border-top-color: darken(@success,10%);
  }
}

.alert-info {
  color: @info;
  background-color: lighten(@info,35%);
  border-color: lighten(@info,20%);
  .alert-link {
    color: darken(@info,10%);
  }
  hr {
    border-top-color: darken(@info,10%);
  }
}

.alert-warning {
  color: @warning;
  background-color: lighten(@warning, 25%);
  border-color: lighten(@warning, 15%);
  .alert-link {
    color: darken(@warning, 10%);
  }
  hr {
    border-top-color: darken(@warning, 10%);
  }
}

.alert-danger {
  color: @danger;
  background-color: lighten(@danger,25%);
  border-color: lighten(@danger,15%);
  .alert-link {
    color: darken(@danger,10%);
  }
  hr {
    border-top-color: darken(@danger,10%);
  }
}

/* =============
   Carousel
============= */

.carousel-control {
  width: 10%;

  span {
    position: absolute;
    top: 50%;
    z-index: 5;
    display: inline-block;
    font-size: 30px;
  }
}



/* =========== */
/* Sweet Alert */
/* =========== */

.sweet-alert {
  background: url("../images/bg-pattern.png");

  h2 {
    font-size: 24px;
    position: relative;
  }

  p {
    font-size: 14px;
    line-height: 22px;
  }
  .icon.success  {
    .placeholder {
      border: 4px solid fade(@success,30%);
    }
    .line {
      background-color: @success;
    }
  }
  .icon.warning {
    border-color: @warning;
  }
  .icon.info {
    border-color: @info;
  }
  .btn-warning:focus,.btn-info:focus,.btn-success:focus,.btn-danger:focus,.btn-default:focus {
    box-shadow: none;
  }
  .btn-lg {
    font-size: 15px !important;
    padding: 6px 14px !important;
  }
}



/* =============
   Widgets
============= */
.widget-box-one {
  .widget-one-icon {
    position: absolute;
    right: 30px;
    font-size: 72px !important;
    top: 0;
    color: @light3;
    overflow: hidden;
    vertical-align: middle;
    line-height: 2 !important;
  }

  .wigdet-one-content {
    position: relative;
  }
}

.widget-box-two {
  .widget-two-icon {
    position: absolute;
    right: 30px;
    font-size: 42px !important;
    top: 30px;
    overflow: hidden;
    vertical-align: middle;
    height: 80px;
    width: 80px;
    text-align: center;
    line-height: 80px;
    border-radius: 50%;
  }
  .wigdet-two-content {
    position: relative;
  }
}


.widget-two-primary {
  background-color: fade(@primary,20%);
  border-color: fade(@primary,50%);

  .widget-two-icon {
    color: fade(@primary,50%);
    border: 2px solid fade(@primary,50%);
  }
}

.widget-two-success {
  background-color: fade(@success,20%);
  border-color: fade(@success,50%);

  .widget-two-icon {
    color: fade(@success,50%);
    border: 2px solid fade(@success,50%);
  }
}

.widget-two-warning {
  background-color: fade(@warning,20%);
  border-color: fade(@warning,50%);

  .widget-two-icon {
    color: fade(@warning,50%);
    border: 2px solid fade(@warning,50%);
  }
}

.widget-two-info {
  background-color: fade(@info,20%);
  border-color: fade(@info,50%);

  .widget-two-icon {
    color: fade(@info,50%);
    border: 2px solid fade(@info,50%);
  }
}

.widget-two-danger {
  background-color: fade(@danger,20%);
  border-color: fade(@danger,50%);

  .widget-two-icon {
    color: fade(@danger,50%);
    border: 2px solid fade(@danger,50%);
  }
}

.widget-two-inverse {
  background-color: fade(@inverse,20%);
  border-color: fade(@inverse,50%);

  .widget-two-icon {
    color: fade(@inverse,50%);
    border: 2px solid fade(@inverse,50%);
  }
}

.widget-two-purple {
  background-color: fade(@purple,20%);
  border-color: fade(@purple,50%);

  .widget-two-icon {
    color: fade(@purple,50%);
    border: 2px solid fade(@purple,50%);
  }
}

.widget-two-pink {
  background-color: fade(@pink,20%);
  border-color: fade(@pink,50%);

  .widget-two-icon {
    color: fade(@pink,50%);
    border: 2px solid fade(@pink,50%);
  }
}

.widget-two-orange {
  background-color: fade(@orange,20%);
  border-color: fade(@orange,50%);

  .widget-two-icon {
    color: fade(@orange,50%);
    border: 2px solid fade(@orange,50%);
  }
}

.widget-two-brown {
  background-color: fade(@brown,20%);
  border-color: fade(@brown,50%);

  .widget-two-icon {
    color: fade(@brown,50%);
    border: 2px solid fade(@brown,50%);
  }
}

.widget-two-teal {
  background-color: fade(@teal,20%);
  border-color: fade(@teal,50%);

  .widget-two-icon {
    color: fade(@teal,50%);
    border: 2px solid fade(@teal,50%);
  }
}

.widget-two-default {
  background-color: fade(@light3,50%);
  border-color: darken(@light3,5%);

  .widget-two-icon {
    color: darken(@light3,20%);
    border: 2px solid darken(@light3,20%);
  }
}

.widget-box-three .bg-icon {
  height: 80px;
  width: 80px;
  text-align: center;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  -moz-border-radius: 50%;
  background-clip: padding-box;
  border: 1px dashed @muted;
  background-color: @light3;
  margin-right: 20px;

  i {
    line-height: 80px;
    font-size: 36px;
    color: @muted;
  }
}

.tilebox-two {
  border-left: 4px solid @light3;
  background-color: @light3;
  i {
    font-size: 54px;
    opacity: 0.3;
    margin-top: 15px;
  }
}

.tilebox-custom {
  border-left-color: @custom;
}
.tilebox-primary {
  border-left-color: @primary;
}
.tilebox-success {
  border-left-color: @success;
}
.tilebox-info {
  border-left-color: @info;
}
.tilebox-warning {
  border-left-color: @warning;
}
.tilebox-danger {
  border-left-color: @danger;
}
.tilebox-inverse {
  border-left-color: @inverse;
}
.tilebox-pink {
  border-left-color: @pink;
}
.tilebox-purple {
  border-left-color: @purple;
}
.tilebox-dark {
  border-left-color: @dark;
}
.tilebox-brown {
  border-left-color: @brown;
}
.tilebox-orange {
  border-left-color: @orange;
}
.tilebox-teal {
  border-left-color: @teal;
}


/* Inbox-widget */

.inbox-widget {
  .inbox-item {
    border-bottom: 1px solid fade(@light3,90%);
    overflow: hidden;
    padding: 10px 0;
    position: relative;
    .inbox-item-img {
      display: block;
      float: left;
      margin-right: 15px;
      width: 40px;
    }
    img {
      width: 40px;
    }
    .inbox-item-author {
      color: @dark;
      display: block;
      margin: 0;
    }
    .inbox-item-text {
      color: #a0a0a0;
      display: block;
      font-size: 12px;
      margin: 0;
    }
    .inbox-item-date {
      color: #a9a9a9;
      font-size: 11px;
      position: absolute;
      right: 7px;
      top: 2px;
    }
  }
}

/* Chat widget */
.conversation-list {
  list-style: none;
  padding: 0 10px;
  li {
    margin-bottom: 24px;
  }
  .chat-avatar {
    display: inline-block;
    float: left;
    text-align: center;
    width: 42px;
    img {
      border-radius: 100%;
      width: @width;
    }
    i {
      font-size: 12px;
      font-style: normal;
    }
  }
  .ctext-wrap {
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    background: fade(@light3,60%);
    border-radius: 3px;
    display: inline-block;
    padding: 12px;
    position: relative;

    i {
      color: @dark;
      display: block;
      font-size: 12px;
      font-style: normal;
      font-weight: bold;
      font-family: @font-secondary;
      position: relative;
    }
    p {
      margin: 0;
      padding-top: 3px;
    }
    &:after {
      right: 100%;
      top: 0;
      border: solid transparent;
      content: " ";
      height: 0;
      width: 0;
      position: absolute;
      pointer-events: none;
      border-top-color: fade(@light3,60%);
      border-width: 8px;
      margin-left: -1px;
      border-right-color: fade(@light3,60%);
    }
  }
  .conversation-text {
    display: inline-block;
    float: left;
    font-size: 12px;
    margin-left: 12px;
    width: 70%;
  }
  .odd {
    .chat-avatar {
      float: right !important;
    }
    .conversation-text {
      float: right !important;
      margin-right: 12px;
      text-align: right;
      width: 70% !important;
    }
    .ctext-wrap {
      background-color: @danger;
      color: @white;

      i {
        color: @white;
      }
      &:after {
        border-color: rgba(238, 238, 242, 0) !important;
        border-left-color: @danger !important;
        border-top-color: @danger !important;
        left: 100% !important;
        margin-right: -1px;
      }
    }
  }
}

.chat-send {
  padding-left: 0;
  padding-right: 30px;

  button {
    width: @width;
  }
}

.chat-inputbar {
  padding-left: 30px;
}

/* Todos widget */

#todo-message {
  font-size: 16px;
}

.todo-list {
  li {
    border-radius: 0;
    border: 0;
    margin: 0;
    padding: 1px;
    color: @muted;

    &:last-of-type {
      border-bottom: none;
    }
  }
  label {
    font-family: @font-primary;
  }
}

.todo-send {
  padding-left: 0;
}


/* Avatar box */
.avatar-sm-box {
  height: 32px;
  width: 32px;
  color: @white;
  display: block;
  line-height: 32px;
  text-align: center;
  border-radius: 50%;
  font-family: @font-secondary;
  font-size: 16px;
}


/* =============
   Nestable
============= */


.custom-dd {
  .dd-list {
    .dd-item {
      .dd-handle {
        background: @light3;
        border: none;
        padding: 8px 16px;
        height: auto;
        font-weight: 600;
        font-family: @font-secondary;
        .border-radius(3px);

        &:hover {
          color: @custom;
        }
      }

      button {
        height: auto;
        font-size: 17px;
        margin: 8px auto;
        color: @light7;
        width: 30px;
      }
    }
  }
}

.custom-dd-empty {
  .dd-list {
    .dd3-handle {
      border: none;
      background: @light3;
      height: 36px !important;
      width: 36px !important;

      &:before {
        color: inherit;
        top: 7px;
      }

      &:hover {
        color: @custom;
      }
    }
    .dd3-content {
      height: auto;
      border: none;
      padding: 8px 16px 8px 46px;
      background: @light3;
      font-weight: 600;
      font-family: @font-secondary;

      &:hover {
        color: @custom;
      }
    }
    button {
      width: 26px;
      height: 26px;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.dd-dragel > .dd-item .dd-handle {
  padding: 8px 16px !important;
  background: @light3;
  height: auto;
}

.dd-placeholder, .dd-empty {
  background: @light3;
  border: 1px dashed @muted;
}
.dd-dragel > .dd3-item > .dd3-handle {
  border: none;
  background: @light3;
  height: 36px !important;
  width: 36px !important;

  &:before {
    color: inherit;
    top: 7px;
  }
}
.dd-dragel > .dd3-item > .dd3-content {
  padding: 8px 16px 8px 46px;
  background: @light3;
  height: auto;
}



/* =============
   Rating
============= */

.rating-md {
  i {
    font-size: 18px;
  }
}

.rating-lg {
  i {
    font-size: 24px;
  }
}



/* =============
   Calendar
============= */


.calendar {
  float: left;
  margin-bottom: 0;
}

.fc-view {
  margin-top: 30px;
}
.none-border {
  .modal-footer {
    border-top: none;
  }
}

.fc-toolbar {
  margin-bottom: 5px;
  margin-top: 15px;
  h2 {
    font-size: 18px;
    font-weight: 600;
    font-family: @font-secondary;
    line-height: 30px;
    text-transform: uppercase;
  }
}
.fc-day-grid-event .fc-time {
  font-family: @font-secondary;
}

.fc-day {
  background: @white;
}

.fc-toolbar .fc-state-active, .fc-toolbar .ui-state-active,
.fc-toolbar button:focus, .fc-toolbar button:hover,
.fc-toolbar .ui-state-hover {
  z-index: 0;
}


.fc {
  th.fc-widget-header {
    background: @light;
    font-size: 14px;
    line-height: 20px;
    padding: 10px 0;
    text-transform: uppercase;
  }
}
.fc-unthemed th, .fc-unthemed td, .fc-unthemed thead, .fc-unthemed tbody, .fc-unthemed .fc-divider, .fc-unthemed .fc-row, .fc-unthemed .fc-popover {
    border-color: @light3;
}

.fc-button {
  background: #f1f1f1;
  border: none;
  color: @light7;
  text-transform: capitalize;
  box-shadow: none !important;
  border-radius: 3px !important;
  margin: 0 3px !important;
  padding: 6px 12px !important;
  height: auto !important;
}

.fc-text-arrow {
  font-family: inherit;
  font-size: 16px;
}

.fc-state-hover {
  background: @light3;
}

.fc-state-highlight {
  background: #f0f0f0;
}

.fc-state-down, .fc-state-active,.fc-state-disabled {
  background-color: @custom !important;
  color: @white !important;
  text-shadow: none !important;
}

.fc-cell-overlay {
  background: #f0f0f0;
}

.fc-unthemed {
  .fc-today {
    background: @white;
  }
}

.fc-event {
  border-radius: 2px;
  border: none;
  cursor: move;
  font-size: 13px;
  margin: 5px 7px;
  padding: 5px 5px;
  text-align: center;
}

.external-event {
  cursor: move;
  margin: 10px 0;
  padding: 6px 10px;
}
.external-event.bg-primary {
  background-color: fade(@primary,30%) !important;
  color: @primary;
}

.external-event.bg-success {
  background-color: fade(@success,30%) !important;
  color: @success;
}

.external-event.bg-info {
  background-color: fade(@info,30%) !important;
  color: @info;
}

.external-event.bg-warning {
  background-color: fade(@warning,30%) !important;
  color: @warning;
}

.external-event.bg-danger {
  background-color: fade(@danger,30%) !important;
  color: @danger;
}

.external-event.bg-pink {
  background-color: fade(@pink,30%) !important;
  color: @pink;
}

.external-event.bg-purple {
  background-color: fade(@purple,30%) !important;
  color: @purple;
}

.external-event.bg-inverse {
  background-color: fade(@inverse,30%) !important;
  color: @inverse;
}

.external-event.bg-orange {
  background-color: fade(@orange,30%) !important;
  color: @orange;
}

.external-event.bg-brown {
  background-color: fade(@brown,30%) !important;
  color: @brown;
}

.external-event.bg-teal {
  background-color: fade(@teal,30%) !important;
  color: @teal;
}

.fc-basic-view {
  td.fc-week-number {
    span {
      padding-right: 8px;
      font-weight: 700;
      font-family: @font-secondary;
    }
  }
  td.fc-day-number {
    padding-right: 8px;
    font-weight: 700;
    font-family: @font-secondary;
  }
}



/* =============
   Form
============= */

/* Form components */

label {
  font-weight: 600;
  font-family: @font-secondary;
}

textarea.form-control {
  min-height: 90px;
}
.form-control {
  border: 1px solid #e3e3e3;
  border-radius: 4px;
  padding: 7px 12px;
  height: 38px;
  max-width: 100%;
  -webkit-box-shadow: none;
  box-shadow: none;
  -webkit-transition: all 300ms linear;
  -moz-transition: all 300ms linear;
  -o-transition: all 300ms linear;
  -ms-transition: all 300ms linear;
  transition: all 300ms linear;
}
.form-control:focus {
  border: 1px solid #aaaaaa;
  -webkit-box-shadow: none;
  box-shadow: none;
  outline: 0 !important;
}
.input-lg {
  height: 46px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px;
}
.input-sm {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
.form-horizontal .form-group {
  margin-left: -10px;
  margin-right: -10px;
}
.form-control-feedback {
  line-height: 38px !important;
}
.input-group-btn .btn {
  padding: 8px 12px;
}
.input-group-btn .btn-sm {
  padding: 5px 10px;
}
.input-group-btn .btn-lg {
  padding: 10px 17px;
}

.has-success .checkbox, .has-success .checkbox-inline,
.has-success .control-label, .has-success .help-block,
.has-success .radio, .has-success .radio-inline,
.has-success.checkbox label, .has-success.checkbox-inline label,
.has-success.radio label, .has-success.radio-inline label,
.has-success .form-control-feedback{
  color: @success;
}

.has-warning .checkbox, .has-warning .checkbox-inline,
.has-warning .control-label, .has-warning .help-block,
.has-warning .radio, .has-warning .radio-inline,
.has-warning.checkbox label, .has-warning.checkbox-inline label,
.has-warning.radio label, .has-warning.radio-inline label,
.has-warning .form-control-feedback{
  color: @warning;
}
.has-error .checkbox, .has-error .checkbox-inline,
.has-error .control-label, .has-error .help-block,
.has-error .radio, .has-error .radio-inline,
.has-error.checkbox label, .has-error.checkbox-inline label,
.has-error.radio label, .has-error.radio-inline label,
.has-error .form-control-feedback{
  color: @danger;
}
.has-success .form-control {
  border-color: @success;
  box-shadow: none !important;
}
.has-warning .form-control {
  border-color: @warning;
  box-shadow: none !important;
}
.has-error .form-control {
  border-color: @danger;
  box-shadow: none !important;
}
.input-group-addon {
  border-radius: 2px;
  border: 1px solid #eeeeee;
}


/* == Form Advanced */

/* Bootstrap tagsinput */
.bootstrap-tagsinput {
  box-shadow: none;
  padding: 5px 7px 5px;
  width: 100%;
  border: 1px solid #e3e3e3;

  .label-info {
    background-color: @custom !important;
    display: inline-block;
    padding: 5px;
    margin: 3px 1px;
  }
}

/* CSS Switch */
input[data-switch] {
  display: none;
}
input[data-switch] + label {
  font-size: 1em;
  line-height: 1;
  width: 56px;
  height: 24px;
  background-color: #ddd;
  background-image: none;
  border-radius: 2rem;
  padding: 0.16667rem;
  cursor: pointer;
  display: inline-block;
  text-align: center;
  position: relative;
  font-family: inherit;
  -webkit-transition: all 0.1s ease-in-out;
  transition: all 0.1s ease-in-out;
}
input[data-switch] + label:before {
  color: @dark;
  content: attr(data-off-label);
  display: block;
  font-family: inherit;
  font-weight: 500;
  font-size: 12px;
  line-height: 21px;
  position: absolute;
  right: 6px;
  margin: 0.21667rem;
  top: 0;
  text-align: center;
  min-width: 1.66667rem;
  overflow: hidden;
  -webkit-transition: all 0.1s ease-in-out;
  transition: all 0.1s ease-in-out;
}
input[data-switch] + label:after {
  content: '';
  position: absolute;
  left: 3px;
  background-color: #f7f7f7;
  box-shadow: none;
  border-radius: 2rem;
  height: 20px;
  width: 20px;
  -webkit-transition: all 0.1s ease-in-out;
  transition: all 0.1s ease-in-out;
}
input[data-switch]:checked + label {
  background-color: @custom;
}
input[data-switch]:checked + label:before {
  color: @white;
  content: attr(data-on-label);
  right: auto;
  left: 6px;
}
input[data-switch]:checked + label:after {
  left: 33px;
  background-color: #f7f7f7;
}

input[data-switch="bool"] + label {
  background-color: @danger;
}
input[data-switch="bool"] + label:before,input[data-switch="bool"]:checked + label:before,
input[data-switch="default"]:checked + label:before{
  color: @white !important;
}

input[data-switch="bool"]:checked + label {
  background-color: @success;
}

input[data-switch="default"]:checked + label {
  background-color: #a2a2a2;
}

input[data-switch="primary"]:checked + label {
  background-color: @primary;
}

input[data-switch="success"]:checked + label {
  background-color: @success;
}

input[data-switch="info"]:checked + label {
  background-color: @info;
}

input[data-switch="warning"]:checked + label {
  background-color: @warning;
}

input[data-switch="inverse"]:checked + label {
  background-color: @inverse;
}

input[data-switch="pink"]:checked + label {
  background-color: @pink;
}

input[data-switch="purple"]:checked + label {
  background-color: @purple;
}

input[data-switch="orange"]:checked + label {
  background-color: @orange;
}

input[data-switch="brown"]:checked + label {
  background-color: @brown;
}

input[data-switch="teal"]:checked + label {
  background-color: @teal;
}


/* Button Switch css */

.btn-switch {
  position: relative;
  display: inline-block;
  cursor: pointer;
}
.btn-switch > input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  visibility: hidden;
  cursor: pointer;
  left: -100%;
  top: -100%;
}
.btn-switch > input[type="checkbox"] + .btn {
  background-color: transparent !important;
  border-color: @custom;
  color: @custom !important;
}
.btn-switch > input[type="checkbox"] + .btn > em {
  display: inline-block;
  border: 1px solid @custom;
  border-radius: 50%;
  padding: 2px;
  margin: 0 4px 0 0;
  top: 1px;
  font-size: 10px;
  text-align: center;
}
.btn-switch > input[type="checkbox"] + .btn > em:before {
  opacity: 0;
}
.btn-switch > input[type="checkbox"]:checked + .btn {
  background-color: @custom !important;
  color: @white !important;
}
.btn-switch > input[type="checkbox"]:checked + .btn > em {
  background-color: @white;
  color: @custom !important;
}
.btn-switch > input[type="checkbox"]:checked + .btn > em:before {
  opacity: 1;
}

.btn-switch-primary > input[type="checkbox"] + .btn,
.btn-switch-primary > input[type="checkbox"]:checked + .btn > em{
  color: @primary !important;
}
.btn-switch-primary > input[type="checkbox"] + .btn,
.btn-switch-primary > input[type="checkbox"] + .btn > em{
  border-color: @primary !important;
}
.btn-switch-primary > input[type="checkbox"]:checked + .btn {
  background-color: @primary !important;
}

.btn-switch-success > input[type="checkbox"] + .btn,
.btn-switch-success > input[type="checkbox"]:checked + .btn > em{
  color: @success !important;
}
.btn-switch-success > input[type="checkbox"] + .btn,
.btn-switch-success > input[type="checkbox"] + .btn > em{
  border-color: @success !important;
}
.btn-switch-success > input[type="checkbox"]:checked + .btn {
  background-color: @success !important;
}

.btn-switch-info > input[type="checkbox"] + .btn,
.btn-switch-info > input[type="checkbox"]:checked + .btn > em{
  color: @info !important;
}
.btn-switch-info > input[type="checkbox"] + .btn,
.btn-switch-info > input[type="checkbox"] + .btn > em{
  border-color: @info !important;
}
.btn-switch-info > input[type="checkbox"]:checked + .btn {
  background-color: @info !important;
}

.btn-switch-warning > input[type="checkbox"] + .btn,
.btn-switch-warning > input[type="checkbox"]:checked + .btn > em{
  color: @warning !important;
}
.btn-switch-warning > input[type="checkbox"] + .btn,
.btn-switch-warning > input[type="checkbox"] + .btn > em{
  border-color: @warning !important;
}
.btn-switch-warning > input[type="checkbox"]:checked + .btn {
  background-color: @warning !important;
}

.btn-switch-danger > input[type="checkbox"] + .btn,
.btn-switch-danger > input[type="checkbox"]:checked + .btn > em{
  color: @danger !important;
}
.btn-switch-danger > input[type="checkbox"] + .btn,
.btn-switch-danger > input[type="checkbox"] + .btn > em{
  border-color: @danger !important;
}
.btn-switch-danger > input[type="checkbox"]:checked + .btn {
  background-color: @danger !important;
}

.btn-switch-inverse > input[type="checkbox"] + .btn,
.btn-switch-inverse > input[type="checkbox"]:checked + .btn > em{
  color: @inverse !important;
}
.btn-switch-inverse > input[type="checkbox"] + .btn,
.btn-switch-inverse > input[type="checkbox"] + .btn > em{
  border-color: @inverse !important;
}
.btn-switch-inverse > input[type="checkbox"]:checked + .btn {
  background-color: @inverse !important;
}

.btn-switch-pink > input[type="checkbox"] + .btn,
.btn-switch-pink > input[type="checkbox"]:checked + .btn > em{
  color: @pink !important;
}
.btn-switch-pink > input[type="checkbox"] + .btn,
.btn-switch-pink > input[type="checkbox"] + .btn > em{
  border-color: @pink !important;
}
.btn-switch-pink > input[type="checkbox"]:checked + .btn {
  background-color: @pink !important;
}

.btn-switch-purple > input[type="checkbox"] + .btn,
.btn-switch-purple > input[type="checkbox"]:checked + .btn > em{
  color: @purple !important;
}
.btn-switch-purple > input[type="checkbox"] + .btn,
.btn-switch-purple > input[type="checkbox"] + .btn > em{
  border-color: @purple !important;
}
.btn-switch-purple > input[type="checkbox"]:checked + .btn {
  background-color: @purple !important;
}

.btn-switch-orange > input[type="checkbox"] + .btn,
.btn-switch-orange > input[type="checkbox"]:checked + .btn > em{
  color: @orange !important;
}
.btn-switch-orange > input[type="checkbox"] + .btn,
.btn-switch-orange > input[type="checkbox"] + .btn > em{
  border-color: @orange !important;
}
.btn-switch-orange > input[type="checkbox"]:checked + .btn {
  background-color: @orange !important;
}

.btn-switch-brown > input[type="checkbox"] + .btn,
.btn-switch-brown > input[type="checkbox"]:checked + .btn > em{
  color: @brown !important;
}
.btn-switch-brown > input[type="checkbox"] + .btn,
.btn-switch-brown > input[type="checkbox"] + .btn > em{
  border-color: @brown !important;
}
.btn-switch-brown > input[type="checkbox"]:checked + .btn {
  background-color: @brown !important;
}

.btn-switch-teal > input[type="checkbox"] + .btn,
.btn-switch-teal > input[type="checkbox"]:checked + .btn > em{
  color: @teal !important;
}
.btn-switch-teal > input[type="checkbox"] + .btn,
.btn-switch-teal > input[type="checkbox"] + .btn > em{
  border-color: @teal !important;
}
.btn-switch-teal > input[type="checkbox"]:checked + .btn {
  background-color: @teal !important;
}


/* Multiple select */
.search-input {
  margin-bottom: 10px;
}

.ms-container {
  background: transparent url('../plugins/multiselect/img/multiple-arrow.png') no-repeat 50% 50%;
  width: 100% !important;
  max-width: 360px !important;

  .ms-list {
    box-shadow: none;
    border: 1px solid fade(@dark,20%);
  }
  .ms-list.ms-focus {
    box-shadow: none;
    border: 1px solid fade(@dark,40%);
  }
  .ms-selectable {
    box-shadow: none;
    outline: none !important;

    li.ms-elem-selectable{
      border: none;
      padding: 5px 10px;
    }
    li.ms-hover {
      background-color: @custom;
    }
  }
  .ms-selection {
    li.ms-elem-selection{
      border: none;
      padding: 5px 10px;
    }
    li.ms-hover {
      background-color: @custom;
    }
  }
}


/* Select 2 */
.select2-container {
  width: 100% !important;

  .select2-selection--single {
    border: 1px solid #E3E3E3 !important;
    height: 38px !important;

    .select2-selection__rendered {
      line-height: 36px !important;
      padding-left: 12px !important;
    }

    .select2-selection__arrow {
      height: 34px;
      width: 34px;
      right: 3px;

      b{
        border-color: #999 transparent transparent transparent;
        border-width: 6px 6px 0 6px;
      }
    }
  }
}

.select2-container--open {
  .select2-selection--single {

    .select2-selection__arrow {

      b{
        border-color: transparent transparent #999 transparent !important;
        border-width: 0 6px 6px 6px !important;
      }
    }
  }
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: @custom;
}
.select2-results__option {
  padding: 6px 12px;
}

.select2-dropdown {
  border: 1px solid #e3e3e3 !important;
  padding-top: 5px;
  .box-shadow( 0 2px 2px rgba(0, 0, 0, .15));
}
.select2-search {
  input{
    border: 1px solid #e3e3e3 !important;
  }
}

.select2-container .select2-selection--multiple {
  min-height: 38px !important;
  border: 1px solid #e3e3e3 !important;

  .select2-selection__rendered {
    padding: 2px 10px;
  }
  .select2-search__field {
    margin-top: 7px;
    border: 0 !important;
  }
  .select2-selection__choice {
    background-color: #f5f5f5;
    border: 1px solid #e3e3e3;
    border-radius: 1px;
    padding: 0 7px;
  }
}


/* AUTOCOMPLETE */
.autocomplete-suggestions {
  border: 1px solid #f9f9f9;
  background: @white;
  cursor: default;
  overflow: auto;
  -webkit-box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15);
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15);
}

.autocomplete-suggestion {
  padding: 2px 5px;
  white-space: nowrap;
  overflow: hidden;
}

.autocomplete-no-suggestion {
  padding: 2px 5px;
}

.autocomplete-selected {
  background: fade(@muted,30%);
  cursor: pointer;
}

.autocomplete-suggestions strong {
  font-weight: bold;
  color: @dark;
}

.autocomplete-group {
  padding: 2px 5px;
}

.autocomplete-group strong {
  font-weight: bold;
  font-size: 16px;
  color: @dark;
  display: block;
}

/* Bootstrap-select */
.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: 100% !important;
}
.bootstrap-select  {
  .dropdown-toggle {
    &:focus {
      outline: none !important;
    }
  }
  .glyphicon {
    padding-right: 6px;
  }
}

/* Bootstrap filestyle */
.icon-span-filestyle {
  padding-right: 5px;
}


/* Bootstrap-touchSpin */
.bootstrap-touchspin {
  .input-group-btn-vertical {
    .btn {
      padding: 9px 12px;
    }
    i {
      top: 4px;
      left: 8px;
    }
  }
}



/* Form validation */
.parsley-error {
  border-color: @danger !important;
}
.parsley-errors-list {
  display: none;
  margin: 0;
  padding: 0;
}
.parsley-errors-list.filled {
  display: block;
}
.parsley-errors-list > li {
  font-size: 12px;
  list-style: none;
  color: @danger;
  margin-top: 5px;
}



// Timepicker
.bootstrap-timepicker-widget table td input {
  border: 1px solid fade(@dark,30%);
  width: 35px;
}


// Datepicker
.datepicker-dropdown {
  padding: 10px !important;
}

.datepicker td, .datepicker th {
    width: 30px;
    height: 30px;
}

.datepicker table tr td.active:hover, .datepicker table tr td.active:hover:hover,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active.disabled:hover:hover,
.datepicker table tr td.active:active, .datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active, .datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active.disabled:hover.active,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active:hover.disabled,
.datepicker table tr td.active.disabled.disabled,
.datepicker table tr td.active.disabled:hover.disabled,
.datepicker table tr td.active[disabled], .datepicker table tr td.active:hover[disabled],
.datepicker table tr td.active.disabled[disabled],
.datepicker table tr td.active.disabled:hover[disabled],
.datepicker table tr td.selected, .datepicker table tr td.selected:hover,
.datepicker table tr td.selected.disabled, .datepicker table tr td.selected.disabled:hover{
  background-color: @custom !important;
  color: @white !important;
  background-image: none !important;
  text-shadow: none !important;
}

.datepicker table tr td.today, .datepicker table tr td.today:hover, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover {
  background-color: @success !important;
  color: @white !important;
  background-image: none !important;
}

.datepicker-inline {
    border: 2px solid fade(@dark,10%);
}

//Daterange Picker

.daterangepicker td.active, .daterangepicker td.active:hover {
    background-color: @custom;
}
.daterangepicker .input-mini.active {
    border: 1px solid fade(@dark,30%);
}
.daterangepicker .ranges li {
  border-radius: 2px;
  color: @dark;
  font-weight: 600;
  font-size: 12px;
  font-family: @font-secondary;
}
.daterangepicker select.hourselect, .daterangepicker select.minuteselect,
.daterangepicker select.secondselect, .daterangepicker select.ampmselect{
  border: 1px solid fade(@dark,30%);
  padding: 2px;
  width: 60px;
}
.daterangepicker .ranges li.active, .daterangepicker .ranges li:hover {
  background-color: @custom;
  border: 1px solid @custom;
  color: @white;
}





/* Wizard */
.wizard > .content {
  background: @white;
  min-height: 240px;
  padding: 20px !important;
  border: 1px solid @light3;
  margin-top: 10px !important;
}

.wizard > .content > .body {
  padding: 0px;
  position: relative;
  width: 100%;

  input {
    border: 1px solid darken(@light3,4%);
  }

  ul > li {
    display: block;
    line-height: 30px;
  }
  label.error {
    color: @danger;
    margin-left: 0;
    margin-top: 5px;
    font-size: 12px;
  }
  label {
    display: inline-block;
    margin-top: 10px;
  }
}

.wizard > .steps {

  a{
    font-size: 16px;
    text-align: center;
    font-family: @font-secondary;
  }

  .number {
    font-size: 86px;
    line-height: 86px;
    font-family: @font-secondary;
    position: absolute;
    left: 10px;
    top: -14px;
    opacity: 0.1;
  }

  .disabled {
    a {
      background: @light3;
      color: lighten(@dark,5%) !important;
      cursor: default;
      border: 1px solid darken(@light3,5%);
      &:hover {
        background: darken(@light3,5%);
        border: 1px solid darken(@light3,10%);
      }
      &:active {
        background: darken(@light3,5%);
        border: 1px solid darken(@light3,10%);
      }
    }
  }
  .current {
    a {
      background: @custom;
      &:hover {
        background: @custom;
        .number {
          color: @white;
          opacity: 0.4;
        }
      }
      &:active {
        background: @custom;
        .number {
          color: @white;
          opacity: 0.4;
        }
      }
      .number {
        color: @white;
        opacity: 0.4;
      }
    }
  }
  .done {
    a {
      background: @light3 !important;
      color: lighten(@dark,5%) !important;
    }
  }
}

.wizard > .steps a, .wizard > .steps a:hover, .wizard > .steps a:active, .wizard > .content {
  border-radius: 2px;
  position: relative;
}

.wizard > .actions {
  margin-top: 15px !important;

  a {
    background: @custom;
    border-radius: 2px !important;
    color: @white;

    &:hover {
      background: darken(@custom,15%);
      color: @white;
    }
    &:active {
      background: darken(@custom,15%);
      color: @white;
    }
  }
  .disabled {
    a {
      background: @white;
      color: @dark;
      cursor: default;
      border: 1px solid #eaeaea;
      &:hover {
        background: @light3;
        color: @dark;
        cursor: default;
        border: 1px solid #eaeaea;
      }
      &:active {
        background: @light3;
        color: @dark;
        cursor: default;
        border: 1px solid #eaeaea;
      }
    }
  }
}

.wizard.vertical > .content {
  margin-top: 0px !important;
}

@media (max-width: 560px) {
  .wizard.vertical > .steps,.wizard.vertical > .content {
    width: 100%;
  }
}


/* Summernote */
.note-editor {
  position: relative;

  .btn-default {
    background-color: transparent;
    border-color: transparent !important;
  }
  .btn-group-sm > .btn, .btn-sm {
    padding: 8px 12px !important;
  }
  .note-toolbar {
    background-color: @light3;
    border-bottom: 1px solid @light;
    margin: 0;
  }
  .note-statusbar {
    background-color: @white;
    .note-resizebar {
      border-top: none;
      height: 15px;
      padding-top: 3px;
    }
  }
}
.note-editor.note-frame {
  border: 1px solid @light !important;
}

.note-popover {
  .popover {
    .popover-content {
      padding: 5px 0 10px 5px;
    }
  }

  .btn-default {
    background-color: transparent;
    border-color: transparent !important;
  }
  .btn-group-sm > .btn, .btn-sm {
    padding: 8px 12px !important;
  }
}

.note-toolbar {
  padding: 5px 0 10px 5px;
}



/* Wysiwig css */

.mce-panel {
  border: 1px solid @light3 !important;
  background-color: @light3 !important;
}
.mce-menu {
  background-color: @white !important;
  box-shadow: 0 0px 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0px 0 rgba(0, 0, 0, 0.02);
}
.mce-menubar .mce-menubtn:hover, .mce-menubar .mce-menubtn.mce-active, .mce-menubar .mce-menubtn:focus {
  border-color: @light3 !important;
}
.mce-menu-item:hover, .mce-menu-item.mce-selected, .mce-menu-item:focus,.mce-menu-item-normal.mce-active,
.mce-primary{
  background-color: @custom !important;
}
.mce-window-head .mce-title {
  font-family: @font-secondary;
}
.mce-window {
  padding: 20px !important;
}
.mce-window-head {
  padding-bottom: 20px !important;
  border-bottom: 0 !important;
}


/* == Form Uploads == */

.jFiler-input-dragDrop {
  width: 100%;
  background-color: #fafafa;
}

.jFiler-theme-default .jFiler-input {
  width: 100%;
}
.jFiler-theme-default .jFiler-input-button {
  background-color: @custom;
  background-image: none !important;
  color: @white;
  border: 1px solid @custom !important;
}





/* =============
   Tables
============= */
th {
  font-family: @font-secondary;
  font-weight: 600;
}
.table-centered {
  td {
    vertical-align: middle !important;
  }
}

/* Data table */

table.dataTable {
  margin-top: 10px !important;
  margin-bottom: 18px !important;
}
.table-bordered.dataTable>thead>tr>td, .table-bordered.dataTable>thead>tr>th {
  border-bottom-width: 1px !important;
}

/* Fixed Header table */
.fixedHeader-floating {
  top: 70px !important;
}
/* Key Table border */
table.dataTable th.focus, table.dataTable td.focus {
  outline: 2px solid @custom !important;
  outline-offset: -1px;
  background-color: @custom;
  color: @white;
}

/* Responsive data table */
table.dataTable.dtr-inline.collapsed > tbody > tr > td:first-child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th:first-child:before {
  box-shadow: 0 0 3px rgba(67, 89, 102, 0.2);
  background-color: @success;
}
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before {
  background-color: @danger;
}
table.dataTable>tbody>tr.child span.dtr-title {
  font-family: @font-secondary;
}

/* ColVid Tables */
div.ColVis {
  float: none;
  margin-right: 30px;
}
button.ColVis_Button,
.ColVis_Button:hover {
  float: none;
  border-radius: 3px;
  outline: none !important;
  background: none;
  box-shadow: none;
  color: @white !important;
  background-color: @custom !important;
  border: 1px solid @custom !important;
}
.dataTables_length {
  float: left;
}
div.ColVis_collectionBackground {
  background-color: transparent;
}
ul.ColVis_collection {
  padding: 10px 0 0 0;
  background-color: @white;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26);
  border: none;
}
ul.ColVis_collection li {
  background: transparent !important;
  padding: 3px 10px !important;
  border: none !important;
  box-shadow: none !important;
}
#datatable-colvid_info {
  float: left;
}

/* Responsive Table */
.table-rep-plugin {
  .dropdown-menu li.checkbox-row {
    padding: 2px 15px !important;
  }

  .table-responsive {
    border: none !important;
  }
  tbody {
    th {
      font-size: 14px;
      font-weight: normal;
    }
  }
  .checkbox-row {
    padding-left: 40px;

    label {
      display: inline-block;
      padding-left: 5px;
      position: relative;
      &::before {
        -o-transition: 0.3s ease-in-out;
        -webkit-transition: 0.3s ease-in-out;
        background-color: @white;
        border-radius: 3px;
        border: 1px solid @muted;
        content: "";
        display: inline-block;
        height: 17px;
        left: 0;
        margin-left: -20px;
        position: absolute;
        transition: 0.3s ease-in-out;
        width: 17px;
        outline: none !important;
      }
      &::after {
        color: @light3;
        display: inline-block;
        font-size: 11px;
        height: 16px;
        left: 0;
        margin-left: -20px;
        padding-left: 3px;
        padding-top: 1px;
        position: absolute;
        top: -1px;
        width: 16px;
      }
    }
    input[type="checkbox"] {
      cursor: pointer;
      opacity: 0;
      z-index: 1;
      outline: none !important;

      &:disabled + label {
        opacity: 0.65;
      }
    }
    input[type="checkbox"]:focus + label {
      &::before {
        outline-offset: -2px;
        outline: none;
      }
    }
    input[type="checkbox"]:checked + label {
      &::after {
        content: "\f00c";
        font-family: 'FontAwesome';
      }
    }
    input[type="checkbox"]:disabled + label {
      &::before {
        background-color: @light;
        cursor: not-allowed;
      }
    }
    input[type="checkbox"]:checked + label {
      &::before {
        background-color: @white;
        border-color: @success;
      }
      &::after {
        color: @success;
      }
    }
  }
  table.focus-on tbody tr.focused th, table.focus-on tbody tr.focused td,
  .sticky-table-header{
    background-color: @success;
    color: @white;
    border-color: @success;
  }
  .sticky-table-header.fixed-solution {
    top: 70px !important;
  }
}



/* Data table editable */
.add-edit-table {
  td,th {
    vertical-align: middle !important;
  }
  td {
    border: 0 !important;
  }
}
#datatable-editable .actions a {
  padding: 5px;
}
#datatable-editable .form-control {
  background-color: #ffffff;
  width: 100%;
}
#datatable-editable .fa-trash-o {
  color: #f05050;
}
#datatable-editable .fa-times {
  color: #f05050;
}
#datatable-editable .fa-pencil {
  color: #29b6f6;
}
#datatable-editable .fa-save {
  color: #33b86c;
}
#datatable td {
  font-weight: normal;
}
.modal-block {
  background: transparent;
  margin: 40px auto;
  max-width: 600px;
  padding: 0;
  position: relative;
  text-align: left;
}


.dt-buttons {
  float: left;
}
div#datatable-buttons_info {
  float: left;
}
table.dataTable thead th {
  position: relative;
  background-image: none !important;
}
table.dataTable thead th.sorting:after,
table.dataTable thead th.sorting_asc:after,
table.dataTable thead th.sorting_desc:after {
  position: absolute;
  top: 7px;
  right: 8px;
  display: block;
  font-family: FontAwesome;
}
table.dataTable thead th.sorting:after {
  content: "\f0dc";
  padding-top: 0.12em;
}
table.dataTable thead th.sorting_asc:after {
  content: "\f0de";
}
table.dataTable thead th.sorting_desc:after {
  content: "\f0dd";
}
.DTFC_LeftBodyWrapper table thead th.sorting:after,
.dataTables_scrollBody table thead th.sorting:after,
.DTFC_RightBodyLiner table thead th.sorting:after,
.DTFC_LeftBodyWrapper table thead th.sorting_asc:after,
.dataTables_scrollBody table thead th.sorting_asc:after,
.DTFC_RightBodyLiner table thead th.sorting_asc:after,
.DTFC_LeftBodyWrapper table thead th.sorting_desc:after,
.dataTables_scrollBody table thead th.sorting_desc:after,
.DTFC_RightBodyLiner table thead th.sorting_desc:after {
  display: none !important;
}

/* Tablesaw Tables */
.tablesaw {
  thead {
    background: fade(@light3,70%);
    background-image: none;
    border: none;

    th {
      text-shadow: none;
      letter-spacing: 0.06em;
    }

    tr:first-child th {
      font-weight: 600;
      border: none;
      font-family: @font-secondary;
    }
  }
  td {
    border-top: 1px solid fade(@light3,70%) !important;
  }
}

.tablesaw td, .tablesaw tbody th {
  font-size: inherit;
  line-height: inherit;
  padding: 10px !important;
}
.tablesaw-stack tbody tr ,.tablesaw tbody tr{
  border-bottom: none;
}
.tablesaw-sortable .tablesaw-sortable-head.tablesaw-sortable-ascending button:after, .tablesaw-sortable .tablesaw-sortable-head.tablesaw-sortable-descending button:after {
  font-family: FontAwesome;
  font-size: 10px;
}
.tablesaw-sortable .tablesaw-sortable-head.tablesaw-sortable-ascending button:after {
  content: "\f176";
}
.tablesaw-sortable .tablesaw-sortable-head.tablesaw-sortable-descending button:after {
  content: "\f175";
}
.tablesaw-bar .btn-select.btn-small:after, .tablesaw-bar .btn-select.btn-micro:after {
  font-size: 8px;
  padding-right: 10px;
}
.tablesaw-swipe .tablesaw-cell-persist {
  box-shadow: none;
}
.tablesaw-enhanced .tablesaw-bar .btn {
  text-shadow: none;
  background-image: none;
}
.tablesaw-enhanced .tablesaw-bar .btn.btn-select {
  &:hover {
    background: @white;
  }
}
.tablesaw-enhanced .tablesaw-bar .btn:hover, .tablesaw-enhanced .tablesaw-bar .btn:focus,.tablesaw-enhanced .tablesaw-bar .btn:active {
  color: @custom !important;
  background-color: @light3;
  outline: none !important;
  box-shadow: none !important;
  background-image: none;
}


/* Table colored */
.table-colored {
  thead {
    th {
      color: @white;
    }
  }
}
.table-colored.table-custom {
  thead {
    th {
      background-color: @custom;
    }
  }
}
.table-colored.table-primary {
  thead {
    th {
      background-color: @primary;
    }
  }
}
.table-colored.table-success {
  thead {
    th {
      background-color: @success;
    }
  }
}

.table-colored.table-info {
  thead {
    th {
      background-color: @info;
    }
  }
}

.table-colored.table-warning {
  thead {
    th {
      background-color: @warning;
    }
  }
}

.table-colored.table-danger {
  thead {
    th {
      background-color: @danger;
    }
  }
}

.table-colored.table-inverse {
  thead {
    th {
      background-color: @inverse;
    }
  }
}

.table-colored.table-pink {
  thead {
    th {
      background-color: @pink;
    }
  }
}

.table-colored.table-purple {
  thead {
    th {
      background-color: @purple;
    }
  }
}

.table-colored.table-brown {
  thead {
    th {
      background-color: @brown;
    }
  }
}

.table-colored.table-orange {
  thead {
    th {
      background-color: @orange;
    }
  }
}

.table-colored.table-teal {
  thead {
    th {
      background-color: @teal;
    }
  }
}

/* Table colored-bordered */

.table-colored-bordered {
  thead {
    th {
      border-bottom: 0 !important;
      color: @white;
    }
  }
}

.table-colored-bordered.table-bordered-primary {
  border: 2px solid @primary;

  thead {
    th {
      background-color: @primary;
    }
  }
}

.table-colored-bordered.table-bordered-success {
  border: 2px solid @success;

  thead {
    th {
      background-color: @success;
    }
  }
}

.table-colored-bordered.table-bordered-info {
  border: 2px solid @info;

  thead {
    th {
      background-color: @info;
    }
  }
}

.table-colored-bordered.table-bordered-custom {
  border: 2px solid @custom;

  thead {
    th {
      background-color: @custom;
    }
  }
}

.table-colored-bordered.table-bordered-warning {
  border: 2px solid @warning;

  thead {
    th {
      background-color: @warning;
    }
  }
}

.table-colored-bordered.table-bordered-danger {
  border: 2px solid @danger;

  thead {
    th {
      background-color: @danger;
    }
  }
}

.table-colored-bordered.table-bordered-inverse {
  border: 2px solid @inverse;

  thead {
    th {
      background-color: @inverse;
    }
  }
}

.table-colored-bordered.table-bordered-pink {
  border: 2px solid @pink;

  thead {
    th {
      background-color: @pink;
    }
  }
}

.table-colored-bordered.table-bordered-purple {
  border: 2px solid @purple;

  thead {
    th {
      background-color: @purple;
    }
  }
}

.table-colored-bordered.table-bordered-orange {
  border: 2px solid @orange;

  thead {
    th {
      background-color: @orange;
    }
  }
}

.table-colored-bordered.table-bordered-brown {
  border: 2px solid @brown;

  thead {
    th {
      background-color: @brown;
    }
  }
}

.table-colored-bordered.table-bordered-teal {
  border: 2px solid @teal;

  thead {
    th {
      background-color: @teal;
    }
  }
}


/* Table full colored */
.table-colored-full {
  color: @white;

  thead {
    th {
      border-bottom: 0 !important;
      padding: 10px !important;
    }
  }
  tbody {
    td,th {
      border: 0 !important;
    }
  }
}

.table-colored-full.table-full-primary {
  background-color: fade(@primary,80%);

  thead {
    th {
      background-color: @primary;
    }
  }
  tbody {
    tr{
      &:hover {
        background-color: @primary;
      }
    }
  }
}

.table-colored-full.table-full-success {
  background-color: fade(@success,80%);

  thead {
    th {
      background-color: @success;
    }
  }
  tbody {
    tr{
      &:hover {
        background-color: @success;
      }
    }
  }
}

.table-colored-full.table-full-info {
  background-color: fade(@info,80%);

  thead {
    th {
      background-color: @info;
    }
  }
  tbody {
    tr{
      &:hover {
        background-color: @info;
      }
    }
  }
}

.table-colored-full.table-full-custom {
  background-color: fade(@custom,80%);

  thead {
    th {
      background-color: @custom;
    }
  }
  tbody {
    tr{
      &:hover {
        background-color: @custom;
      }
    }
  }
}

.table-colored-full.table-full-warning {
  background-color: fade(@warning,80%);

  thead {
    th {
      background-color: @warning;
    }
  }
  tbody {
    tr{
      &:hover {
        background-color: @warning;
      }
    }
  }
}

.table-colored-full.table-full-danger {
  background-color: fade(@danger,80%);

  thead {
    th {
      background-color: @danger;
    }
  }
  tbody {
    tr{
      &:hover {
        background-color: @danger;
      }
    }
  }
}

.table-colored-full.table-full-inverse {
  background-color: fade(@inverse,80%);

  thead {
    th {
      background-color: @inverse;
    }
  }
  tbody {
    tr{
      &:hover {
        background-color: @inverse;
      }
    }
  }
}


.table-colored-full.table-full-pink {
  background-color: fade(@pink,80%);

  thead {
    th {
      background-color: @pink;
    }
  }
  tbody {
    tr{
      &:hover {
        background-color: @pink;
      }
    }
  }
}

.table-colored-full.table-full-purple {
  background-color: fade(@purple,80%);

  thead {
    th {
      background-color: @purple;
    }
  }
  tbody {
    tr{
      &:hover {
        background-color: @purple;
      }
    }
  }
}

.table-colored-full.table-full-brown {
  background-color: fade(@brown,80%);

  thead {
    th {
      background-color: @brown;
    }
  }
  tbody {
    tr{
      &:hover {
        background-color: @brown;
      }
    }
  }
}

.table-colored-full.table-full-orange {
  background-color: fade(@orange,80%);

  thead {
    th {
      background-color: @orange;
    }
  }
  tbody {
    tr{
      &:hover {
        background-color: @orange;
      }
    }
  }
}


.table-colored-full.table-full-teal {
  background-color: fade(@teal,80%);

  thead {
    th {
      background-color: @teal;
    }
  }
  tbody {
    tr{
      &:hover {
        background-color: @teal;
      }
    }
  }
}


/* =====================================
   CHARTS
   ===================================== */
// Chart List
.chart-detail-list {
  li {
    margin: 0 10px;
  }
}

/* Flot chart */
#flotTip {
  padding: 8px 12px;
  background-color: @dark !important;
  z-index: 100;
  color: @white;
  opacity: 0.9;
  font-size: 13px;
  -webkit-border-radius: 3px !important;
  -moz-border-radius: 3px !important;;
  border-radius: 3px !important;;
}
.legend tr {
  height: 20px;
  font-family: @font-secondary;
}
.legendLabel {
  padding-left: 5px !important;
  line-height: 10px;
  padding-right: 10px;
}

/* Morris chart */
.morris-hover.morris-default-style {
    border-radius: 5px;
    padding: 10px 12px;
}

/* Chartist chart */
.ct-golden-section:before {
    float: none;
}
.ct-chart {

  max-height: 300px;

  .ct-label {
    fill: #a3afb7;
    color: #a3afb7;
    font-size: 12px;
    line-height: 1;
  }
}
.ct-grid {
    stroke: fade(@dark,10%);
}
.ct-chart.simple-pie-chart-chartist {
  .ct-label {
    color: @white;
    fill: @white;
    font-size: 16px;
  }
}
.ct-chart .ct-series.ct-series-a .ct-bar, .ct-chart .ct-series.ct-series-a .ct-line, .ct-chart .ct-series.ct-series-a .ct-point, .ct-chart .ct-series.ct-series-a .ct-slice-donut {
    stroke: @info;
}
.ct-chart .ct-series.ct-series-b .ct-bar, .ct-chart .ct-series.ct-series-b .ct-line, .ct-chart .ct-series.ct-series-b .ct-point, .ct-chart .ct-series.ct-series-b .ct-slice-donut {
    stroke: @danger;
}
.ct-chart .ct-series.ct-series-c .ct-bar, .ct-chart .ct-series.ct-series-c .ct-line, .ct-chart .ct-series.ct-series-c .ct-point, .ct-chart .ct-series.ct-series-c .ct-slice-donut {
    stroke: @success;
}
.ct-chart .ct-series.ct-series-d .ct-bar, .ct-chart .ct-series.ct-series-d .ct-line, .ct-chart .ct-series.ct-series-d .ct-point, .ct-chart .ct-series.ct-series-d .ct-slice-donut {
    stroke: @orange;
}
.ct-chart .ct-series.ct-series-e .ct-bar, .ct-chart .ct-series.ct-series-e .ct-line, .ct-chart .ct-series.ct-series-e .ct-point, .ct-chart .ct-series.ct-series-e .ct-slice-donut {
    stroke: @dark;
}
.ct-chart .ct-series.ct-series-f .ct-bar, .ct-chart .ct-series.ct-series-f .ct-line, .ct-chart .ct-series.ct-series-f .ct-point, .ct-chart .ct-series.ct-series-f .ct-slice-donut {
    stroke: @primary;
}
.ct-chart .ct-series.ct-series-g .ct-bar, .ct-chart .ct-series.ct-series-g .ct-line, .ct-chart .ct-series.ct-series-g .ct-point, .ct-chart .ct-series.ct-series-g .ct-slice-donut {
    stroke: @brown;
}
.ct-series-a .ct-area, .ct-series-a .ct-slice-pie {
    fill: @info;
}
.ct-series-b .ct-area, .ct-series-b .ct-slice-pie {
    fill: @danger;
}
.ct-series-c .ct-area, .ct-series-c .ct-slice-pie {
    fill: @success;
}
.ct-series-d .ct-area, .ct-series-d .ct-slice-pie {
    fill: @orange;
}
.chartist-tooltip {
  position: absolute;
  display: inline-block;
  opacity: 0;
  min-width: 10px;
  padding: 2px 10px;
  border-radius: 3px;
  background: @dark;
  color: @white;
  text-align: center;
  pointer-events: none;
  z-index: 1;
  -webkit-transition: opacity .2s linear;
  -moz-transition: opacity .2s linear;
  -o-transition: opacity .2s linear;
  transition: opacity .2s linear;
}

.chartist-tooltip.tooltip-show {
  opacity: 1;
}


/* C3 chart */
.c3 svg {
    max-width: 100%;
}
.c3-tooltip td>span {
  background: @dark;
}
.c3-tooltip td {
    border-left: none;
}
.c3-tooltip {
    box-shadow: 0 8px 40px 0 rgba(0, 0, 0, 0.12);
    opacity: 1;
}
.c3-chart-arcs-title {
  font-size: 18px;
  font-weight: 600;
  font-family: @font-secondary;
}
.c3-tooltip tr {
    border: none !important;
}
.c3-tooltip th {
  background-color: @dark;
}

.c3-tooltip {
  .value {
    font-weight: 600;
    font-family: @font-secondary;
  }
}

/* Sparkline chart */
.jqstooltip {
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  width: auto !important;
  height: auto !important;
  background-color: @dark !important;
  padding: 5px 10px !important;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  -moz-border-radius: 3px;
  border-color: @dark !important;
}
.jqsfield {
  font-size: 12px !important;
  line-height: 18px !important;
}



/* Google Chart*/
.google-chart {
  .chart {
    display: inline-block;
  }
}