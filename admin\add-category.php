<?php
session_start();
include('includes/config.php');
error_reporting(0);
if (strlen($_SESSION['login']) == 0) {
    header('location:index.php');
} else {
    if (isset($_POST['submitcategory'])) {
        $areaid = $_POST['area'];
        $categoryname = $_POST['category'];
        $categorydescription = $_POST['categorydescription'];
        $accessibility = $_POST['accessibility'];
        $status = $_POST['status'];
        $subcategoryname = $_POST['subcategory']; // Get the subcategory name
        $subcategorydescription = $_POST['subcategorydescription']; // Get subcategory description

        $query = mysqli_query($con, "INSERT INTO tblcategory(AreaId, Category, CategoryDescription, Accessibility, Status, Is_Active) VALUES('$areaid','$categoryname','$categorydescription','$accessibility','$status',1)");

        if ($query) {
            $lastCategoryId = mysqli_insert_id($con); // Get the ID of the newly inserted category

            // Insert the subcategory if a name is provided
            if (!empty($subcategoryname)) {
                $subquery = mysqli_query($con, "INSERT INTO tblsubcategory(CategoryId, SubCategory, SubCategoryDescription, Is_Active) VALUES('$lastCategoryId','$subcategoryname','$subcategorydescription',1)");

                if ($subquery) {
                    $msg = "Category and Subcategory created ";
                } else {
                    $error = "Category created, but something went wrong adding the Subcategory. Please try again.";
                }
            } else {
                $msg = "Category created ";
            }
        } else {
            $error = "Something went wrong. Please try again.";
        }
    }
?>


    <!DOCTYPE html>
    <html lang="en">

    <head>

        <title>Celaeno Technology | Add Category</title>

        <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
        <link rel="stylesheet" href="../plugins/switchery/switchery.min.css">
        <script src="assets/js/modernizr.min.js"></script>

    </head>


    <body class="fixed-left">

        <div id="wrapper">

            <?php include('includes/topheader.php'); ?>
            <?php include('includes/leftsidebar.php'); ?>
            <div class="content-page">
                <div class="content">
                    <div class="container">


                        <div class="row">
                            <div class="col-xs-12">
                                <div class="page-title-box">
                                   a -->
                                    <ol class="breadcrumb p-0 m-0">
                                        <li>
                                            <a href="./admin-page.php">Admin</a>
                                        </li>
                                        <li>
                                            <a href="./manage-categories.php">Categories</a>
                                        </li>

                                        <li class="active">
                                            Add Categories
                                        </li>
                                    </ol>
                                    <div class="clearfix"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="card-box">
                                    <h4 class="m-t-0 header-title"><b>Add Category </b></h4>
                                    <hr />



                                    <div class="row">
                                        <div class="col-sm-6">
                                            <?php if ($msg) { ?>
                                                <div class="alert alert-success" role="alert">
                                                    <strong>Well done!</strong> <?php echo htmlentities($msg); ?>
                                                </div>
                                            <?php } ?>

                                            <?php if ($error) { ?>
                                                <div class="alert alert-danger" role="alert">
                                                    <strong>Oh snap!</strong> <?php echo htmlentities($error); ?>
                                                </div>
                                            <?php } ?>


                                        </div>
                                    </div>





                                    <div class="row">
                                        <div class="col-md-6">
                                            <form class="form-horizontal" name="category" method="post">
                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Product</label>
                                                    <div class="col-md-10">
                                                        <select class="form-control" name="area" required>
                                                            <option value="">Select Product</option>
                                                            <?php
                                                            // Fetching active areas
                                                            $ret = mysqli_query($con, "SELECT id, ProductName FROM tblarea WHERE Is_Active=1");
                                                            while ($result = mysqli_fetch_array($ret)) {
                                                            ?>
                                                                <option value="<?php echo htmlentities($result['id']); ?>">
                                                                    <?php echo htmlentities($result['ProductName']); ?>
                                                                </option>
                                                            <?php } ?>
                                                        </select>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Category</label>
                                                    <div class="col-md-10">
                                                        <input type="text" class="form-control" value="" name="category" required>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Category Description</label>
                                                    <div class="col-md-10">
                                                        <textarea class="form-control" rows="5" name="categorydescription" required></textarea>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Subcategory</label>
                                                    <div class="col-md-10">
                                                        <input type="text" class="form-control" value="" name="subcategory">
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Subcategory Description (Optional)</label>
                                                    <div class="col-md-10">
                                                        <textarea class="form-control" rows="3" name="subcategorydescription"></textarea>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Accessibility</label>
                                                    <div class="col-md-10">
                                                        <select class="form-control" name="accessibility" required>
                                                            <option value="">Select Accessibility</option>
                                                            <option value="Public">Public</option>
                                                            <option value="Internal">Internal</option>
                                                            <option value="Private">Private</option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Status</label>
                                                    <div class="col-md-10">
                                                        <select class="form-control" name="status" required>
                                                            <option value="">Select Status</option>
                                                            <option value="Draft">Draft</option>
                                                            <option value="Published">Published</option>
                                                            <option value="Retired">Retired</option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">&nbsp;</label>
                                                    <div class="col-md-10">
                                                        <button type="submit" class="btn btn-custom waves-effect waves-light btn-md" name="submitcategory">
                                                            Submit
                                                        </button>
                                                        <a href="manage-categories.php" class="btn btn-custom waves-effect waves-light btn-md">
                                                            Cancel
                                                        </a>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div> <?php include('includes/footer.php'); ?>

                        </div>




                    </div>
                    <script>
                        var resizefunc = [];
                    </script>

                    <script src="assets/js/jquery.min.js"></script>
                    <script src="assets/js/bootstrap.min.js"></script>
                    <script src="assets/js/detect.js"></script>
                    <script src="assets/js/fastclick.js"></script>
                    <script src="assets/js/jquery.blockUI.js"></script>
                    <script src="assets/js/waves.js"></script>
                    <script src="assets/js/jquery.slimscroll.js"></script>
                    <script src="assets/js/jquery.scrollTo.min.js"></script>
                    <script src="../plugins/switchery/switchery.min.js"></script>

                    <script src="assets/js/jquery.core.js"></script>
                    <script src="assets/js/jquery.app.js"></script>

    </body>

    </html>
<?php } ?>