/* General container for the title search wrapper */

.Header__titleSearchWrapper {
  max-width: 100rem;
  background: rgb(0, 116, 116);
  background: linear-gradient(
    142deg,
    rgba(0, 116, 116, 1) 45%,
    rgba(255, 255, 255, 1) 150%,
    rgba(0, 116, 116, 1) 200%
  );
  background-size: cover;
  background-position: center;
  padding: 200px;
  margin: 0 auto;
  text-align: center;
  color: white;
  box-sizing: border-box; /* Ensure padding doesn't affect width */
  margin-top: 0px; /* Add margin-top to start below the header */
}

.Header__titleSearchBox {
  max-width: 1400px; /* Limit the width of the search box */
  margin: 0 auto;
}

/* Title and description styling */
.Header__titleDescription {
  margin-bottom: 20px;
}

.Header__welcomeTitle {
  font-size: 36px;
  margin: 0;
  font-weight: 700;
}

.Header__welcomeDescription {
  font-size: 16px;
  margin: 10px 0;
  font-weight: 400;
}

/* Container for the search input */
.Header__searchContainer {
  margin-top: 20px;
}

/* Styling for the search box and input field */
.SearchBox__searchpart {
  display: inline-block;
  position: relative;
}

input {
  padding: 20px;
  font-size: 16px;
  width: 500px;
  border-radius: 50px;
  border: 1px solid #ccc;
  outline: none;
  transition: border-color 0.3s ease;
}

/* Focus effect for the input field */
input:focus {
  border-color: #007474;
}

/* Styling for the filter popup and the search icon */
.SearchBox__filterPopup {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
}

.SearchBox__searchicon {
  display: inline-block;
  margin-left: 10px;
}

.SearchBox__searchicon svg {
  width: 16px;
  height: 16px;
}

/* Section styling for suites */
.sp_suites_cont {
  width: 100rpm;
  padding: 20px;
  background-color: #f4f4f4;
  border: 2px solid #ccc;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.sp_suites_heading {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
  padding-bottom: 10px;
  text-align: center;
}

/* Support page */
/* body {
  background-color: rgb(37, 150, 190);
  font-family: "Poppins", sans-serif;
  margin: 0;
  padding: 0;
} */

.container-1 {
  margin-top: 1px;
  text-align: center;
  padding: 40px;
  border-radius: 12px;
  background-color: #fdf4ef;
}

.zsp_heading {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333; /* Adjusted text color to contrast with the gradient */
}

.zsp_description {
  font-size: 18px;
  color: #333; /* Adjusted text color for better contrast */
  margin-bottom: 40px;
}

.zsp_container {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.zsp_item_1 {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
  padding: 30px;
  text-align: center;
  flex: 1;
  max-width: 420px;
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
  background: rgb(0, 116, 116);
  background: linear-gradient(
    157deg,
    rgba(0, 116, 116, 1) 68%,
    rgba(255, 255, 247, 1) 73%,
    rgba(0, 116, 116, 1) 79%
  );
}

.zsp_item_2 {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
  padding: 30px;
  text-align: center;
  flex: 1;
  max-width: 420px;
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
  background: rgb(255, 140, 43);
  background: linear-gradient(
    157deg,
    rgba(255, 140, 43, 1) 68%,
    rgba(255, 255, 247, 1) 73%,
    rgba(255, 140, 43, 1) 79%
  );
}

.zsp_item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.zsp_badge {
  background: #ff8c2b;
  color: #ffffff;
  padding: 6px 10px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
  position: absolute;
  top: 15px;
  right: 15px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.zsp_title {
  font-size: 24px;
  font-weight: 600;
  color: white;
  margin-top: 20px;
}

.zsp_boxDescription {
  font-size: 16px;
  color: white;
  margin: 15px 0;
  line-height: 1.6;
}

/* General Styles */
body {
  font-family: Arial, sans-serif;
  background-color: #f8f8f8;
  margin: 0;
  padding: 0;
}

/* List Container */
ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

/* List Items */
.WidgetsContainer__lftWd {
  width: 90%;
  max-width: 600px;
  background: white;
  margin: 20px;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease-in-out;
}

.WidgetsContainer__lftWd:hover {
  transform: translateY(-5px);
}

/* Wrapper */
.hz_wrapper {
  padding: 10px;
}

/* Heading */
.hz_mainHeading {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 15px;
}

/* Row Styling */
.hz_row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border-radius: 8px;
}

.hz_rowOdd {
  background-color: #f4f4f4;
}

/* Column 1 - Text Content */
.hz_col1 {
  width: 70%;
}

/* Column 2 - Empty Space for Future Use */
.hz_col2 {
  width: 30%;
}

/* Headings */
.hz_heading {
  font-size: 18px;
  font-weight: bold;
  color: #222;
  margin-bottom: 10px;
}

/* Description */
.hz_description {
  font-size: 14px;
  color: #555;
  line-height: 1.6;
}

/* Buttons */
.hz_button {
  display: inline-block;
  background-color: #007474;
  color: white;
  padding: 8px 15px;
  text-decoration: none;
  font-size: 14px;
  font-weight: bold;
  border-radius: 5px;
  transition: background 0.3s ease;
}

.hz_button:hover {
  background-color: #007474;
}

/* General styles */
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f9f9f9;
}

/* Section Styling */

/*ajhs*/

/* General Styles */
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f4f4f4;
}

/* Contact Us Widget */ /* Apply background only to the contact page container */
.contact-page-container {
  /* background-image: url("images/image3.jpg"); Replace with your image URL */
  background-size: cover; /* Ensures the image covers the entire container */
  background-position: center; /* Ensures the image is centered */
  background-repeat: no-repeat; /* Prevents repeating the background image */
  height: 70vh; /* Container's height remains 90vh (unchanged) */
  display: flex;
  justify-content: center;
  align-items: center;
  color: white; /* Adjust text color for visibility on the background */
  font-family: Arial, sans-serif; /* Optional: change font */
  /* margin-top: 5rem; */
  padding-bottom: 2rem; /* Add bottom padding for balance */
  text-align: center; /* Center-align the content inside */
  padding-left: 20px; /* Optional: increase left padding */
  padding-right: 20px; /* Optional: increase right padding */
  width: 100%; /* Ensures it spans the full width of the viewport */
  position: relative; /* Allows for positioning of other elements inside */
  z-index: 1; /* Ensures this container stays on top of other content */
  overflow: hidden; /* Ensures the background image does not overflow */
}

/* Optional: Styling for any content you add inside */
.content {
  text-align: center;
  padding: 20px; /* Add some padding for better spacing */
  font-size: 16px;
  max-width: 1200px; /* Max width to ensure content doesn't stretch too wide */
  width: 100%; /* Full width */
  margin: 0 auto; /* Center the content horizontally */
}

/* Support Container in One Line */
.sp_support_container1 {
  display: flex;
  flex-wrap: wrap; /* Allows wrapping when the screen is smaller */
  justify-content: space-between;
  gap: 60px;
  max-width: 1200px; /* Max width for the container */
  width: 100%; /* Full width */
  margin: 0 auto; /* Center the container */
}

/* Support Cells */
.sp_support_cell {
  flex: 1;
  background-color: rgba(
    255,
    247,
    240,
    0.9
  ); /* Add some transparency to the background */
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  min-width: 300px; /* Ensures proper width */
  text-align: center;
}

.sp_support_cell:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

/* Titles */
.sp_support_title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

/* Description */
.sp_support_description {
  font-size: 16px;
  color: #555;
  line-height: 1.5;
  margin-bottom: 15px;
}

/* Links */
.sp_support_link {
  display: inline-block;
  padding: 10px 15px;
  background-color: #007474;
  color: white;
  text-decoration: none;
  font-weight: bold;
  border-radius: 5px;
  transition: background 0.3s ease;
}

.sp_support_link:hover {
  background-color: #005b5b; /* Slightly darker shade on hover */
}

/* Additional Styling for the Upper and Lower Arcs */
.c-arc {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 2rem; /* Adds space above the arcs */
}

.c-arc__lower {
  background-color: #f2f4f4; /* Aubergine color */
  width: 100%;
  height: 150px;
  border-bottom-left-radius: 100% 142px;
  border-bottom-right-radius: 100% 142px;
  position: absolute;
}

/* Responsive Design */
@media (max-width: 992px) {
  .sp_support_container1 {
    overflow-x: auto; /* Enables horizontal scrolling on smaller screens */
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .hz_row {
    flex-direction: column;
  }

  .hz_col1,
  .hz_col2 {
    width: 100%;
    text-align: center;
  }
}

/* Media Queries for responsiveness */
@media (max-width: 768px) {
  .zsp_row {
    flex-direction: column;
  }

  .zsp_col {
    width: 100%;
  }

  .sp_tiers {
    flex-direction: column;
  }

  .tier {
    width: 100%;
    margin-bottom: 20px;
  }
}

/* Responsive Design */

/* For Tablets (Max width: 768px) */
@media (max-width: 768px) {
  .Header__titleSearchWrapper {
    padding: 100px 20px; /* Adjust padding for smaller screens */
  }

  .Header__welcomeTitle {
    font-size: 28px; /* Smaller font size for title */
  }

  .Header__welcomeDescription {
    font-size: 14px; /* Smaller font size for description */
  }

  .Header__searchContainer {
    margin-top: 15px;
  }

  input {
    width: 100%; /* Make the search box full width */
    padding: 15px; /* Adjust padding */
    font-size: 14px; /* Smaller font size for input */
  }

  .SearchBox__searchpart {
    width: 100%; /* Ensure search part takes full width */
  }

  .sp_suites_cont {
    padding: 15px;
  }

  .sp_suites_heading {
    font-size: 20px; /* Adjust heading size */
  }
}

/* For Mobile Devices (Max width: 480px) */
@media (max-width: 480px) {
  .Header__titleSearchWrapper {
    padding: 80px 10px; /* Smaller padding for mobile screens */
  }

  .Header__welcomeTitle {
    font-size: 24px; /* Further reduce title font size */
  }

  .Header__welcomeDescription {
    font-size: 14px; /* Adjust description font size */
  }

  .Header__searchContainer {
    margin-top: 15px;
  }

  input {
    width: 100%; /* Full width for input */
    padding: 12px; /* Adjust padding for mobile */
    font-size: 14px; /* Smaller font size */
  }

  .SearchBox__searchpart {
    width: 100%;
  }

  .sp_suites_cont {
    padding: 12px;
  }

  .sp_suites_heading {
    font-size: 18px; /* Adjust heading size for mobile */
  }
}

/* For Large Screens (Min width: 1024px) */
@media (min-width: 1024px) {
  .Header__titleSearchWrapper {
    padding: 250px; /* Larger padding for bigger screens */
  }

  .Header__welcomeTitle {
    font-size: 48px; /* Larger font size for title */
  }

  .Header__welcomeDescription {
    font-size: 18px; /* Larger font size for description */
  }

  .Header__searchContainer {
    margin-top: 30px;
  }

  input {
    width: 600px; /* Larger input width for big screens */
    padding: 25px; /* Adjust padding */
    font-size: 18px; /* Larger font size for input */
  }

  .sp_suites_cont {
    padding: 30px; /* Larger padding for suites section */
  }

  .sp_suites_heading {
    font-size: 30px; /* Larger heading size */
  }
}


/* Pop up CSS */

/* Popup Overlay */
.popup-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}
.popup-form {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  z-index: 1000; /* Adjust width */
}
.contact_section {
  background-color: #fff;
  border-radius: 10px;
  padding: 10px;
  border-color: #007474;
  text-align: center;
  position: relative;
}

.contact_section .section_title {
  font-size: 24px;
  font-weight: bold;
  color: #007474;
  margin-bottom: 20px;
}

.hc__rowMail {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  font-size: 17px;
  justify-content: space-between;
}

.hc__rowMail>div {
  width: calc(50% - 15px);
  box-sizing: border-box;
}

.hc__mail_id {
  font-weight: bold;
  color: #007474;
  text-decoration: none;
  font-size: 1.1em;
  transition: color 0.3s ease;
}

.hc__mail_id:hover {
  color: #005f5f;
}

.hc__mail_id_description {
  font-size: 0.95em;
  color: #555;
  margin-top: 8px;
  line-height: 1.5;
}

.hc__mail_id,
.hc__mail_id_description {
  margin-bottom: 15px;
}

@media (max-width: 768px) {
  .hc__rowMail>div {
      width: 100%;
  }

  .contact_section {
      padding: 15px;
  }
}


.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 20px;
  font-weight: bold;
  color: #aaa;
  cursor: pointer;
}

.close-btn:hover {
  color: #333;
}

/* End Popup */
