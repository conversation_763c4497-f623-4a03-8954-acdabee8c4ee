async function login() {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const errorMessage = document.getElementById('error-message');

    try {
        const response = await fetch('./clientapi.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password })
        });
        
        const result = await response.json();
        
        if (result.status === 'success') {
            window.location.href = './knowledgebase.php';
        } else {
            errorMessage.textContent = result.message;
        }
    } catch (error) {
        errorMessage.textContent = 'An error occurred. Please try again.';
    }
}

async function loadTickets(userId, product) {
    const tickets = [
        { id: 1, title: 'Issue #1', status: 'Open' },
        { id: 2, title: 'Issue #2', status: 'Closed' }
    ];

    const ticketList = document.getElementById('ticket-list');
    ticketList.innerHTML = '';
    
    tickets.forEach(ticket => {
        const div = document.createElement('div');
        div.className = 'ticket-item';
        div.textContent = `${ticket.title} (${ticket.status})`;
        div.onclick = () => loadConversation(ticket.id);
        ticketList.appendChild(div);
    });
}

async function loadConversation(ticketId) {
    const messages = [
        { sender: 'client', text: 'I have an issue', timestamp: '2025-04-01 10:00' },
        { sender: 'admin', text: 'We\'re looking into it', timestamp: '2025-04-01 10:05' }
    ];

    const conversation = document.getElementById('conversation');
    conversation.innerHTML = '';
    
    messages.forEach(msg => {
        const div = document.createElement('div');
        div.className = `message ${msg.sender}`;
        div.innerHTML = `<strong>${msg.sender}:</strong> ${msg.text}<br><small>${msg.timestamp}</small>`;
        conversation.appendChild(div);
    });

    document.getElementById('ticketId').value = ticketId;
}

document.getElementById('messageForm')?.addEventListener('submit', async function(e) {
    e.preventDefault();
    const message = document.getElementById('message').value;
    const ticketId = document.getElementById('ticketId').value;

    const response = { status: 'success', message: 'Message sent' };
    
    if (response.status === 'success') {
        loadConversation(ticketId);
        document.getElementById('message').value = '';
    }
});

function logout() {
    if (confirm('Are you sure you want to logout?')) {
        window.location.href = 'logout.php';
    }
}