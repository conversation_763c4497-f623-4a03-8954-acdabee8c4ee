<?php
session_start();
include('../dist/include/config.php');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id = intval($_POST['id']);
    $action = $_POST['action'];

    // Check if the record exists
    $checkQuery = "SELECT * FROM tblreviews WHERE id = ?";
    $stmt = $con->prepare($checkQuery);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Record exists, update it
        if ($action == 'like') {
            $query = "UPDATE tblreviews SET likes = likes + 1 WHERE id = ?";
        } elseif ($action == 'dislike') {
            $query = "UPDATE tblreviews SET dislikes = dislikes + 1 WHERE id = ?";
        }
    } else {
        // Record does not exist, insert a new row
        if ($action == 'like') {
            $query = "INSERT INTO tblreviews (id, likes, dislikes) VALUES (?, 1, 0)";
        } elseif ($action == 'dislike') {
            $query = "INSERT INTO tblreviews (id, likes, dislikes) VALUES (?, 0, 1)";
        }
    }

    // Execute the query
    $stmt = $con->prepare($query);
    $stmt->bind_param("i", $id);
    $stmt->execute();

    // Fetch updated data
    $result = $con->query("SELECT likes, dislikes FROM tblreviews WHERE id = $id");
    $data = $result->fetch_assoc();

    echo json_encode($data);
}
