<?php
session_start();
include('includes/config.php');
error_reporting(0);

if (strlen($_SESSION['login']) == 0) {
    header('location:index.php');
} else {
    $subcatid = intval($_GET['scid']);

    // Update subcategory
    if (isset($_POST['submit'])) {
        $categoryid = $_POST['category'];
        $subcategory = $_POST['subcategory'];
        $description = $_POST['description'];

        $query = mysqli_query($con, "UPDATE tblsubcategory SET CategoryId='$categoryid', SubCategory='$subcategory', SubCategoryDescription='$description' WHERE SubCategoryId='$subcatid'");

        if ($query) {
            $msg = "Subcategory updated successfully";
        } else {
            $error = "Something went wrong. Please try again.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <title>Celaeno Technology | Edit Subcategory</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../plugins/switchery/switchery.min.css">
    <script src="assets/js/modernizr.min.js"></script>
</head>

<body class="fixed-left">
    <div id="wrapper">
        <?php include('includes/topheader.php'); ?>
        <?php include('includes/leftsidebar.php'); ?>

        <div class="content-page">
            <div class="content">
                <div class="container">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="page-title-box">
                                <h4 class="page-title">Edit Subcategory</h4>
                                <ol class="breadcrumb p-0 m-0">
                                    <li><a href="#">Admin</a></li>
                                    <li><a href="manage-subcategories.php">Subcategory</a></li>
                                    <li class="active">Edit Subcategory</li>
                                </ol>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-12">
                            <div class="card-box">
                                <h4 class="m-t-0 header-title"><b>Edit Subcategory</b></h4>
                                <hr />

                                <div class="row">
                                    <div class="col-sm-6">
                                        <?php if ($msg) { ?>
                                            <div class="alert alert-success" role="alert">
                                                <strong>Well done!</strong> <?php echo htmlentities($msg); ?>
                                            </div>
                                        <?php } ?>

                                        <?php if ($error) { ?>
                                            <div class="alert alert-danger" role="alert">
                                                <strong>Oh snap!</strong> <?php echo htmlentities($error); ?>
                                            </div>
                                        <?php } ?>
                                    </div>
                                </div>

                                <?php
                                $query = mysqli_query($con, "SELECT tblsubcategory.SubCategoryId, tblsubcategory.SubCategory, tblsubcategory.SubCategoryDescription, tblsubcategory.CategoryId, tblcategory.Category, tblarea.ProductName, tblarea.id as AreaId 
                                                            FROM tblsubcategory 
                                                            JOIN tblcategory ON tblsubcategory.CategoryId = tblcategory.CategoryId 
                                                            JOIN tblarea ON tblcategory.AreaId = tblarea.id 
                                                            WHERE tblsubcategory.Is_Active = 1 AND tblsubcategory.SubCategoryId='$subcatid'");
                                while ($row = mysqli_fetch_array($query)) {
                                ?>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <form class="form-horizontal" name="editsubcategory" method="post">
                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Product</label>
                                                    <div class="col-md-10">
                                                        <select class="form-control" name="product" id="product" onchange="loadCategories()" required>
                                                            <option value="<?php echo htmlentities($row['AreaId']); ?>">
                                                                <?php echo htmlentities($row['ProductName']); ?>
                                                            </option>
                                                            <?php
                                                            $product_query = mysqli_query($con, "SELECT id, ProductName FROM tblarea WHERE Is_Active=1 AND id != '" . $row['AreaId'] . "'");
                                                            while ($product_row = mysqli_fetch_array($product_query)) {
                                                            ?>
                                                                <option value="<?php echo htmlentities($product_row['id']); ?>">
                                                                    <?php echo htmlentities($product_row['ProductName']); ?>
                                                                </option>
                                                            <?php } ?>
                                                        </select>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Category</label>
                                                    <div class="col-md-10">
                                                        <select class="form-control" name="category" id="category" required>
                                                            <option value="<?php echo htmlentities($row['CategoryId']); ?>">
                                                                <?php echo htmlentities($row['Category']); ?>
                                                            </option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Subcategory</label>
                                                    <div class="col-md-10">
                                                        <input type="text" class="form-control"
                                                            value="<?php echo htmlentities($row['SubCategory']); ?>"
                                                            name="subcategory" required>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Subcategory Description</label>
                                                    <div class="col-md-10">
                                                        <textarea class="form-control" rows="5"
                                                            name="description"
                                                            required><?php echo htmlentities($row['SubCategoryDescription']); ?></textarea>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <div class="col-md-offset-2 col-md-10">
                                                        <button type="submit"
                                                            class="btn btn-custom waves-effect waves-light btn-md"
                                                            name="submit">
                                                            Update
                                                        </button>
                                                        <a href="manage-categories.php"
                                                            class="btn btn-custom waves-effect waves-light btn-md">
                                                            Cancel
                                                        </a>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php include('includes/footer.php'); ?>
        </div>
    </div>

    <script>
        var resizefunc = [];

        function loadCategories() {
            var productId = document.getElementById('product').value;
            var categorySelect = document.getElementById('category');

            // Clear existing options
            categorySelect.innerHTML = '<option value="">Loading...</option>';

            // AJAX request to fetch categories for selected product
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'get_categories.php?product_id=' + productId, true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState == 4 && xhr.status == 200) {
                    categorySelect.innerHTML = xhr.responseText;
                }
            };
            xhr.send();
        }
    </script>

    <!-- jQuery -->
    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/detect.js"></script>
    <script src="assets/js/fastclick.js"></script>
    <script src="assets/js/jquery.blockUI.js"></script>
    <script src="assets/js/waves.js"></script>
    <script src="assets/js/jquery.slimscroll.js"></script>
    <script src="assets/js/jquery.scrollTo.min.js"></script>
    <script src="../plugins/switchery/switchery.min.js"></script>

    <!-- App js -->
    <script src="assets/js/jquery.core.js"></script>
    <script src="assets/js/jquery.app.js"></script>
</body>

</html>