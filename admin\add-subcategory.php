<?php
session_start();
include('includes/config.php');
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if the user is logged in.  Redirect and exit if not.
if (strlen($_SESSION['login']) == 0) {
    header('location:index.php');
    exit;
}

// Function to safely get and sanitize POST data.
function get_post_data($key, $default = '')
{
    if (!isset($_POST[$key])) {
        return $default;
    }
    $value = $_POST[$key];
    global $con; // Make the database connection available.
    return mysqli_real_escape_string($con, trim($value));
}

// Function to handle the insertion and provide error message
function execute_query($con, $query, $success_message, &$error_message)
{
    $result = mysqli_query($con, $query);
    if ($result) {
        $msg = $success_message;
        return true;
    } else {
        $error_message = "Database error: " . mysqli_error($con);
        return false;
    }
}

$msg = ""; // Initialize message variable
$error = ""; // Initialize error variable

if (isset($_POST['add_subcategory'])) {
    $product_id = get_post_data('product_id');
    $category_id = get_post_data('category_id');
    $subcategory_name = get_post_data('subcategory_name');
    $subcategory_description = get_post_data('subcategory_description');

    // Validate the input
    $errors = [];
    if (empty($product_id)) {
        $errors[] = "Please select a Product.";
    }
    if (empty($category_id)) {
        $errors[] = "Please select a Category.";
    }
    if (empty($subcategory_name)) {
        $errors[] = "Subcategory Name is required.";
    }

    if (!empty($errors)) {
        $error = implode("<br>", $errors);
    } else {
        // Insert the subcategory into the database
        $insert_subcategory_query = "INSERT INTO tblsubcategory(CategoryId, SubCategory, SubCategoryDescription, Is_Active) 
                                    VALUES('$category_id','$subcategory_name','$subcategory_description',1)";
        if (execute_query($con, $insert_subcategory_query, "Subcategory added successfully!", $error)) {
            $msg = "Subcategory added successfully!";
        }
    }
}


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <title>Celaeno Technology | Add Subcategory</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../plugins/switchery/switchery.min.css">
    <script src="assets/js/modernizr.min.js"></script>
    <script src="assets/js/jquery.min.js"></script>


</head>

<body class="fixed-left">
    <div id="wrapper">
        <?php include('includes/topheader.php'); ?>
        <?php include('includes/leftsidebar.php'); ?>
        <div class="content-page">
            <div class="content">
                <div class="container">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="page-title-box">
                                <!-- <h4 class="page-title">Add Subcategory</h4> -->
                                <ol class="breadcrumb p-0 m-0">
                                    <li><a href="./admin-page.php">Admin</a></li>
                                    <li><a href="./manage-categories.php">Categories</a></li>
                                    <li class="active">Add Subcategory</li>
                                </ol>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="card-box">
                                <h4 class="m-t-0 header-title"><b>Add Subcategory</b></h4>
                                <hr />
                                <div class="row">
                                    <div class="col-sm-6">
                                        <?php if ($msg) { ?>
                                            <div class="alert alert-success" role="alert">
                                                <strong>Well done!</strong> <?php echo htmlentities($msg); ?>
                                            </div>
                                        <?php } ?>
                                        <?php if ($error) { ?>
                                            <div class="alert alert-danger" role="alert">
                                                <strong>Oh snap!</strong> <?php echo htmlentities($error); ?>
                                            </div>
                                        <?php } ?>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <form class="form-horizontal" name="add_subcategory_form" method="post">
                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Product</label>
                                                <div class="col-md-10">
                                                    <select class="form-control" name="product_id" required onchange="fetchCategories(this.value)">
                                                        <option value="">Select Product</option>
                                                        <?php
                                                        $products = mysqli_query($con, "SELECT id, ProductName FROM tblarea WHERE Is_Active=1");
                                                        if ($products) {
                                                            while ($product = mysqli_fetch_assoc($products)) {
                                                                echo '<option value="' . htmlentities($product['id']) . '">' . htmlentities($product['ProductName']) . '</option>';
                                                            }
                                                        } else {
                                                            echo '<option value="">No Products Available</option>';
                                                        }
                                                        ?>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Category</label>
                                                <div class="col-md-10">
                                                    <select class="form-control" name="category_id" id="category_dropdown" required>
                                                        <option value="">Select Category</option>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Subcategory Name</label>
                                                <div class="col-md-10">
                                                    <input type="text" class="form-control" name="subcategory_name" required placeholder="Enter Subcategory Name">
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-2 control-label">Subcategory Description</label>
                                                <div class="col-md-10">
                                                    <textarea class="form-control" rows="5" name="subcategory_description" placeholder="Enter Subcategory Description"></textarea>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-2 control-label">&nbsp;</label>
                                                <div class="col-md-10">
                                                    <button type="submit" class="btn btn-custom waves-effect waves-light btn-md" name="add_subcategory">
                                                        Add Subcategory
                                                    </button>
                                                    <a href="manage-categories.php" class="btn btn-custom waves-effect waves-light btn-md">
                                                        Cancel
                                                    </a>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php include('includes/footer.php'); ?>
        </div>
    </div>
    <script>
        var resizefunc = [];
    </script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/detect.js"></script>
    <script src="assets/js/fastclick.js"></script>
    <script src="assets/js/jquery.blockUI.js"></script>
    <script src="assets/js/waves.js"></script>
    <script src="assets/js/jquery.slimscroll.js"></script>
    <script src="assets/js/jquery.scrollTo.min.js"></script>
    <script src="../plugins/switchery/switchery.min.js"></script>
    <script src="assets/js/jquery.core.js"></script>
    <script src="assets/js/jquery.app.js"></script>
    <script>
        // Function to fetch categories based on the selected product.
        function fetchCategories(productId) {
            if (!productId) {
                $('#category_dropdown').html('<option value="">Select Category</option>');
                return;
            }

            $.ajax({
                url: 'get_categories.php',
                type: 'POST',
                dataType: 'json',
                data: {
                    product_id: productId
                },
                success: function(data) {
                    var categoryDropdown = $('#category_dropdown');
                    categoryDropdown.empty();

                    if (data && data.error) {
                        alert(data.error);
                        categoryDropdown.append('<option value="">Select Category</option>');
                    } else if (data && data.length > 0) {
                        categoryDropdown.append('<option value="">Select Category</option>');
                        $.each(data, function(index, category) {
                            categoryDropdown.append('<option value="' + category.CategoryId + '">' + category.Category + '</option>');
                        });
                    } else {
                        categoryDropdown.append('<option value="">No Categories Found</option>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error fetching categories:", xhr, status, error);
                    alert('Error fetching categories. Please check the console for details.');
                    $('#category_dropdown').html('<option value="">Select Category</option>');
                }
            });
        }

        $(document).ready(function() {
            $('select[name="product_id"]').change(function() {
                var productId = $(this).val();
                fetchCategories(productId);
            });
        });
    </script>
</body>

</html>