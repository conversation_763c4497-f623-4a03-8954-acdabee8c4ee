<?php
session_start();
include('includes/config.php');

// Enable error reporting for debugging (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (empty($_SESSION['login'])) {
    header('Location: index.php');
    exit();
}

// Handle article deletion
if (isset($_GET['action']) && $_GET['action'] == 'del') {
    $articleid = filter_input(INPUT_GET, 'pid', FILTER_VALIDATE_INT) ?? 0;
    if ($articleid > 0) {
        $query = mysqli_query($con, "UPDATE tblarticles SET Is_Active=0 WHERE id='$articleid'");
        if ($query) {
            $msg = "Article deleted successfully";
        } else {
            $error = "Delete failed: " . mysqli_error($con);
        }
    } else {
        $error = "Invalid article ID";
    }
}

// Handle filter inputs
$selectedArea = isset($_POST['areas']) && $_POST['areas'] != '-1' ? intval($_POST['areas']) : null;
$selectedCategory = isset($_POST['category']) && $_POST['category'] != '-1' ? mysqli_real_escape_string($con, $_POST['category']) : null;
$selectedStatus = isset($_POST['status']) && $_POST['status'] != '-1' ? mysqli_real_escape_string($con, $_POST['status']) : null;

// Dynamic query for cards
$query = "
    SELECT 
        a.id AS areaId,
        a.ProductName,
        COUNT(art.id) AS articlesCount
    FROM 
        tblarea a
    LEFT JOIN 
        tblarticles art ON a.id = art.areaId
    WHERE 
        1=1";

if ($selectedArea) {
    $query .= " AND a.id = '$selectedArea'";
}
if ($selectedCategory) {
    $query .= " AND art.Accessibility = '$selectedCategory'";
}
if ($selectedStatus) {
    $statusValue = ($selectedStatus == 'active') ? 1 : 0;
    $query .= " AND art.Is_Active = '$statusValue'";
}

$query .= " GROUP BY a.id, a.ProductName";

// Debug: Output the query
echo "<!-- Debug Query: $query -->";

$cardResult = mysqli_query($con, $query);
if (!$cardResult) {
    die("Query failed: " . mysqli_error($con));
}
// $products = array();
// while ($row = mysqli_fetch_assoc($cardResult)) {
//     $products[] = $row;
// }

?>

<!DOCTYPE html>
<html>

<head>
    <title>Celaeno Technology | Manage Articles</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/knowledge-base.css" rel="stylesheet" type="text/css" />

</head>

<body class="fixed-left">
    <div id="wrapper">
        <?php include('includes/topheader.php'); ?>
        <?php include('includes/leftsidebar.php'); ?>

        <div class="content-page">
            <div class="content">
                <div class="container">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="page-title-box">
                                <ol class="breadcrumb p-0 m-0">
                                    <li class="active">
                                        Knowledge Base
                                    </li>
                                </ol>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>

                    <?php if (isset($msg)) { ?>
                        <div class="alert alert-success"><?php echo $msg; ?></div>
                    <?php } ?>
                    <?php if (isset($error)) { ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php } ?>

                    <div class="row">
                        <div class="col-sm-12">
                            <a href="add-article.php">
                                <button id="addToTable" class="btn btn-success waves-effect waves-light"
                                    style="margin-bottom: 1rem; margin-left: auto; display: block;">
                                    Add Article <i class="mdi mdi-plus-circle-outline"></i>
                                </button>
                            </a>

                            <form method="post" class="filter-articles">
                                <input type="hidden" name="view_mode" id="view_mode"
                                    value="<?php echo isset($_POST['view_mode']) ? htmlspecialchars($_POST['view_mode']) : 'card'; ?>">

                                <select class="form-control" name="areas" id="areas" onchange="this.form.submit()">
                                    <option value="-1">Select Product</option>
                                    <?php
                                    $area_query = mysqli_query($con, "SELECT id, ProductName FROM tblarea ORDER BY ProductName");
                                    if ($area_query) {
                                        while ($row = mysqli_fetch_array($area_query)) {
                                            $selected = $selectedArea == $row['id'] ? 'selected' : '';
                                            echo "<option value='" . $row['id'] . "' $selected>" . htmlspecialchars($row['ProductName']) . "</option>";
                                        }
                                    } else {
                                        echo "<option value=''>Error loading products: " . mysqli_error($con) . "</option>";
                                    }
                                    ?>
                                </select>

                                <select class="form-control" name="category" id="categories"
                                    onchange="this.form.submit()">
                                    <option value="-1">Select Category</option>
                                    <option value="Public" <?php echo $selectedCategory == 'Public' ? 'selected' : ''; ?>>
                                        Public</option>
                                    <option value="Internal" <?php echo $selectedCategory == 'Internal' ? 'selected' : ''; ?>>Internal</option>
                                    <option value="Private" <?php echo $selectedCategory == 'Private' ? 'selected' : ''; ?>>Private</option>

                                </select>

                                <select class="form-control" name="status" id="status" onchange="this.form.submit()">
                                    <option value="-1">Select Status</option>
                                    <option value="active" <?php echo $selectedStatus == 'active' ? 'selected' : ''; ?>>
                                        Active</option>
                                    <option value="inactive" <?php echo $selectedStatus == 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                    <?php
                                    // Existing code to fetch categories from the database
                                    $cat_query = mysqli_query($con, "SELECT Accessibility FROM tblcategory ORDER BY Accessibility");
                                    if ($cat_query) {
                                        while ($row = mysqli_fetch_array($cat_query)) {
                                            $selected = $selectedCategory == $row['Accessibility'] ? 'selected' : '';
                                            echo "<option value='" . htmlspecialchars($row['Accessibility']) . "' $selected>" . htmlspecialchars($row['Accessibility']) . "</option>";
                                        }
                                    } else {
                                        echo "<option value=''>Error loading categories: " . mysqli_error($con) . "</option>";
                                    }
                                    ?>
                                </select>

                                <div style="display: inline-block; margin-left: 20px;">
                                    <button type="button" class="btn btn-outline-secondary" id="list-view-btn"
                                        style="margin-right: 10px;">
                                        <i class="mdi mdi-format-list-bulleted"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-first" id="card-view-btn">
                                        <i class="mdi mdi-view-grid"></i>
                                    </button>
                                </div>

                            </form>
                        </div>
                    </div>

                    <div class="cards-container">
                        <?php
                        if ($cardResult && mysqli_num_rows($cardResult) > 0) {
                            while ($row = mysqli_fetch_array($cardResult)) {
                                if (!empty($row['areaId'])) { // Ensure areaId is present
                                    $areaId = $row['areaId'];

                                    echo "<a href='product_details.php?id=" . urlencode($areaId) . "' class='product-link'>";
                                    echo "<div class='product-card'>";
                                    echo "<h2 class='product-header'>" . htmlspecialchars($row['ProductName']) . "</h2>";
                                    echo "<div class='articles-total'>Total Articles: " . $row['articlesCount'] . "</div>";
                                    echo "</div>";
                                    echo "</a>";
                                } else {
                                    echo "<p>Error: Product ID is missing.</p>";
                                }
                            }
                        } else {
                            echo "<p>No products found.</p>";
                        }
                        ?>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/detect.js"></script>
    <script src="assets/js/fastclick.js"></script>
    <script src="assets/js/jquery.blockUI.js"></script>
    <script src="assets/js/waves.js"></script>
    <script src="assets/js/jquery.slimscroll.js"></script>
    <script src="assets/js/jquery.scrollTo.min.js"></script>
    <script src="assets/js/jquery.core.js"></script>
    <script src="assets/js/jquery.app.js"></script>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- <script>
        $(document).ready(function() {
            $(".btn-outline-secondary").click(function() {
                $(".cards-container").toggle(); // Toggles visibility
            });
        });
        document.addEventListener("DOMContentLoaded", function() {
            document.querySelector(".btn-outline-first").addEventListener("click", function() {
                document.querySelector(".cards-container").classList.toggle("list-view");
            });
        });
    </script> -->

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function () {
            // List view button
            $("#list-view-btn").click(function () {
                $("#view_mode").val("list"); // Update hidden input
                $(".cards-container").addClass("list-view"); // Apply list view
            });

            // Card view button
            $("#card-view-btn").click(function () {
                $("#view_mode").val("card"); // Update hidden input
                $(".cards-container").removeClass("list-view"); // Apply card view
            });

            // Apply the initial view based on the hidden input value
            if ($("#view_mode").val() === "list") {
                $(".cards-container").addClass("list-view");
            } else {
                $(".cards-container").removeClass("list-view");
            }
        });
    </script>



</body>

</html>