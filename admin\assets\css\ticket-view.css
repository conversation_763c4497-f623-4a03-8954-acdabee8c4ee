#resize-handle {
    width: 5px;
    cursor: ew-resize;
    height: 100%;
    background-color: transparent;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
}

.timeline {
    position: relative;
    padding: 20px 0;
}

.timeline__item {
    position: relative;
    margin-bottom: 20px;
}

.timeline__item-header {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.timeline__arrow {
    background: none;
    border: none;
    padding: 0;
    margin-right: 10px;
    cursor: pointer;
}

.timeline__arrow-icon {
    transition: transform 0.3s;
}

.timeline__arrow[aria-expanded="true"] .timeline__arrow-icon {
    transform: rotate(180deg);
}

.timeline__dot {
    width: 10px;
    height: 10px;
    background: #007474;
    border-radius: 50%;
    display: inline-block;
    margin-right: 10px;
}

.timeline__meta {
    flex: 1;
}

.timeline__date {
    font-size: 0.9em;
    color: #666;
}

.timeline__title {
    font-size: 1.1em;
}

.timeline__item-body {
    display: none;
    margin-left: 34px;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 4px;
}

.timeline__item-body[aria-hidden="false"] {
    display: block;
}

.btn-group {
    margin-bottom: 20px;
}

.btn {
    padding: 5px 10px;
    margin-right: 10px;
    background: #007474;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn:hover {
    background: #0056b3;
}



/* Conversion */

.send-message-form {
    margin-top: 20px;

    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
}

.send-message-form h3 {
    margin-bottom: 10px;
    color: #333;
}

.send-message-form textarea {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
    resize: vertical;
    min-height: 80px;
}

.send-message-form button {
    padding: 10px 15px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1em;
}

.send-message-form button:hover {
    background-color: #0056b3;
}

.error {
    color: red;
    margin-bottom: 10px;
}

.conversations {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
    overflow-y: auto;
}

.message {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 8px;
    clear: both;
    /* Prevent floating issues */
}

.client-message {
    background-color: #e6f7ff;
    border: 1px solid #ccebff;
    float: left;
    text-align: left;
}

.admin-message {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    float: right;
    text-align: right;
}

.system-message {
    background-color: #f9e6ff;
    border: 1px solid #f3cbf7;

    font-style: italic;
    color: #777;
}

.sender-info {
    font-size: 0.9em;
    color: #555;
    margin-bottom: 5px;
}

.sender-info strong {
    font-weight: bold;
}

.timestamp {
    font-size: 0.8em;
    color: #888;
    margin-left: 10px;
}

.message-content {
    white-space: pre-line;
    /* Preserve line breaks */
}

/* end */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

:root {
    --primary-color: #007474;
    --primary-hover: #007474;
    --background-color: #f9fafb;
    --foreground-color: #111827;
    --muted-color: #6b7280;
    --border-color: #e5e7eb;
    --card-background: #ffffff;
    --sidebar-width: 300px;
    --sidebar-collapsed-width: 0px;
    --header-height: 64px;
    --status-open: #3b82f6;
    --status-in-progress: #f59e0b;
    --status-resolved: #10b981;
    --radius: 6px;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

body {
    background-color: var(--background-color);
    color: var(--foreground-color);
    line-height: 1.5;
    height: 100vh;
    overflow: hidden;
}

.app-container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* Sidebar Styles */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--card-background);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;

    overflow: hidden;
    position: relative;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
    overflow: hidden;
}

.sidebar-header {
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-toggle {
    position: fixed;
    top: 10px;
    left: 10px;
    z-index: 1000;
    background: #333;
    color: white;
    border: none;
    padding: 8px 10px;
    cursor: pointer;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: left 0.3s ease;
    /* Smooth transition for left movement */
}

.sidebar-toggle:hover {
    background-color: var(--background-color);
    color: var(--foreground-color);
}

.sidebar:not(.collapsed)~.sidebar-toggle {
    left: calc(var(--sidebar-width) + 10px);
    /* Move toggle button to the right of the sidebar when expanded */
}

.sidebar-search {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.search-container {
    position: relative;
    margin-bottom: 8px;
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--muted-color);
}

.search-input {
    width: 100%;
    padding: 8px 8px 8px 32px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    font-size: 14px;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.filter-select {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    font-size: 14px;
    background-color: var(--card-background);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.ticket-list {
    list-style: none;
}

.ticket-item {
    padding: 12px;
    border-radius: var(--radius);
    margin-bottom: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.ticket-item:hover {
    background-color: var(--background-color);
}

.ticket-item.active {
    background-color: rgba(79, 70, 229, 0.1);
    border-left: 3px solid var(--primary-color);
}

.ticket-item-header {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
}

.ticket-item-icon {
    margin-right: 8px;
    color: var(--muted-color);
}

.ticket-item-id {
    font-weight: 600;
    font-size: 14px;
}

.ticket-item-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--muted-color);
}

.sidebar-footer {
    padding: 16px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ticket-count {
    font-size: 14px;
    color: var(--muted-color);
}

.advanced-filter-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.advanced-filter-btn i {
    margin-right: 4px;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    /* Space for toggle button when sidebar is collapsed */
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--muted-color);
    text-align: center;
}

.empty-state h2 {
    margin-bottom: 8px;
    font-size: 24px;
}

.ticket-details {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.ticket-header {
    padding: 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ticket-title {
    display: flex;
    align-items: center;
}

.ticket-title h1 {
    font-size: 24px;
    font-weight: 700;
}

.edit-btn {
    background: none;
    border: none;
    color: var(--muted-color);
    margin-left: 8px;
    cursor: pointer;
}

.edit-btn:hover {
    color: var(--foreground-color);
}

.ticket-status {
    padding: 6px 12px;
    border-radius: 9999px;
    font-size: 14px;
    font-weight: 600;
    color: white;
}

.status-open {
    background-color: var(--status-open);
}

.status-in-progress {
    background-color: var(--status-in-progress);
}

.status-resolved {
    background-color: var(--status-resolved);
}

.ticket-body {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.ticket-content {
    flex: 1;
    overflow: auto;
}

.tabs {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.tab-list {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    padding: 0 24px;
}

.tab-button {
    padding: 12px 16px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    font-size: 14px;
    font-weight: 500;
    color: var(--muted-color);
    cursor: pointer;
    display: flex;
    align-items: center;
}

.tab-button i {
    margin-right: 8px;
}

.tab-button:hover {
    color: var(--foreground-color);
}

.tab-button.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    padding: 24px;
    flex: 1;
    overflow: auto;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.ticket-description {
    background-color: var(--background-color);
    padding: 16px;
    border-radius: var(--radius);
    margin-bottom: 24px;
    font-size: 14px;
}

.empty-content {
    background-color: var(--background-color);
    padding: 16px;
    border-radius: var(--radius);
    color: var(--muted-color);
    font-size: 14px;
}

.ticket-info {
    width: 320px;
    border-left: 1px solid var(--border-color);
    padding: 24px;
    overflow-y: auto;
}

.ticket-info h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
}

.info-card {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    margin-bottom: 24px;
    box-shadow: var(--shadow);
}

.info-content {
    padding: 16px;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--muted-color);
}

.info-details {
    flex: 1;
}

.info-email {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
}

.info-label {
    font-size: 12px;
    color: var(--muted-color);
    margin-bottom: 4px;
}

.info-value {
    font-size: 14px;
    font-weight: 500;
}

.assignee {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.assignee .avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.edit-ticket {
    text-decoration: none;
    padding: 2px;
    color: #007474;
    font-size: 1rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        z-index: 10;
        height: 100%;
        transform: translateX(0);
        transition: transform 0.3s ease;
    }

    .sidebar.collapsed {
        transform: translateX(-100%);
        width: var(--sidebar-width);
    }

    .sidebar:not(.collapsed)~.sidebar-toggle {
        left: calc(var(--sidebar-width) + 10px);
    }

    .ticket-body {
        flex-direction: column;
    }

    .ticket-info {
        width: 100%;
        border-left: none;
        border-top: 1px solid var(--border-color);
    }
}