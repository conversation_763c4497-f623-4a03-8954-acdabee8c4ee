/* Container for cards */
.cards-container {
  display: flex;
  gap: 3.5rem;
  /* Increased gap for better spacing */
  justify-content: center;
  padding: 3rem;
  /* More padding around container */
  flex-wrap: wrap;

  /* Subtle background for contrast */
}

/* Individual card styling */
.product-card {
  width: 350px;
  /* Larger card width */
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  /* Smoother corners */
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  /* Deeper shadow */
  padding: 2rem;
  /* More internal padding */
  transition: all 0.4s ease;
  /* Smoother transition */
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(79, 172, 254, 0.1);
  /* Subtle border */
}

/* Hover effect */
.product-card:hover {
  transform: translateY(-15px) scale(1.02);
  /* Added slight scale effect */
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.18);
  /* Enhanced shadow */
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
  /* Lighter gradient */
}

/* Header styling */
.product-header {
  font-size: 2rem;
  /* Larger header text */
  font-family: "Hind Madurai", sans-serif;
  color: #007474;
  margin: 0 0 1.5rem 0;
  /* More spacing below */
  font-weight: 500;
  /* Bolder text */
  text-transform: uppercase;
  letter-spacing: 1.5px;
  /* More letter spacing */
  background: #007474;
  /* Gradient text */
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  /* Makes gradient visible */
}

/* Article total styling */
.article-total {
  position: absolute;
  bottom: 2rem;
  /* Adjusted positioning */
  left: 2rem;
  right: 2rem;
  padding-top: 1.5rem;
  /* More padding */
  border-top: 2px dashed #e9ecef;
  /* Dashed line for style */
  font-size: 1.4rem;
  /* Larger text */
  color: #495057;
  /* Darker gray */
  font-family: "Hind Madurai", sans-serif;
  font-weight: 300;
  /* Slightly bolder */
  letter-spacing: 0.5px;
}

/* Decorative element */
.product-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 8px;
  /* Thicker decorative line */
  background: linear-gradient(to right, #007474, #ff8c2b);
  border-radius: 20px 20px 0 0;
  /* Matches card corners */
}

/* Additional decorative element */
.product-card::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  width: 50px;
  height: 50px;
  background: radial-gradient(circle, #007474 0%, transparent 40%);
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.product-card:hover::after {
  opacity: 0.8;
  /* Fades in on hover */
}

/* List View */

/* List View Styling
.list-view .product-card {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 10px;
}

.list-view .product-header {
  flex: 1;
  text-align: left;
}

.list-view .articles-total {
  flex: 1;
  text-align: right;
} */
.list-view {
  display: block; /* Change to block layout */
}

.list-view .product-card {
  width: 100%; /* Full width for list view */
  border: 1px solid #ddd;
  padding: 10px;
  text-align: left;
  justify-content: space-between;
}

#categoryFilter {
  padding: 10px;
  font-size: 16px;
  border-radius: 10px;
  border: 1px solid #ccc;
  transition: border-color 0.3s, box-shadow 0.3s;
  margin-bottom: 10%;
}

#categoryFilter:focus {
  border-color: #007474;
  box-shadow: 0 0 10px rgba(0, 86, 179, 0.2);
  outline: none;
}

#categoryFilter::placeholder {
  color: #007474;
  font-style: italic;
}

/* Optional: Add a hover effect */
#categoryFilter:hover {
  border-color: #0056b3;
}
/*css for the inner part of cotainer artical*/

/* General Styling */

.mt-3 {
  margin-top: 3rem;
}

/* Article Container */
div.mt-3 {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
  margin-top: 0%;
}

/* Individual Article Block */
div.border-bottom {
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 2px solid #e0e0e0;
}

/* Article Title */
h4.text-success a {
  font-size: 1.4rem;
  /* font-weight: bold; */
  color: #007474;
  text-decoration: none;
  transition: color 0.3s ease;
}

/* Article Meta (Date and Rating) */

/* Article Description */
p.text-muted {
  font-size: 5rem;
  color: #555;
  line-height: 1.4;
  margin-top: 10px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  div.mt-3 {
    width: 95%;
  }

  h5.text-success a {
    font-size: 1.4rem;
  }

  small {
    font-size: 0.8rem;
  }

  p.text-muted {
    font-size: 0.9rem;
  }
}

/* Reset and General Styling */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Hind Madurai", "sans-serif";
  color: #333;
  line-height: 1.6;
}

/* Article Container */
.article-container {
  max-width: 1390px;
  margin: 10px auto;
  padding: 40px;
  background: #fff;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.8s ease-in-out;
}

/* Article Title */
.article-title {
  font-size: 2rem;
  color: #2c3e50;
  margin-bottom: 15px;
  text-align: center;
  font-weight: 700;
  letter-spacing: 1px;
  text-transform: capitalize;
}

/* Tab Navigation */
.tab-nav {
  display: flex;
  justify-content: center;
  border-bottom: 2px solid #ccc;
  margin-bottom: 20px;
}

.tab-nav a {
  text-decoration: none;
  padding: 10px 20px;
  margin: 0 15px;
  font-size: 1.2rem;
  color: #2c3e50;
  font-weight: 600;
  transition: color 0.3s;
}

.tab-nav a:hover {
  color: #007474;
}

.tab-nav .active {
  color: #007474;
  border-bottom: 3px solid #007474;
}

/* Tab Content */
.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Meta Information */
.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1rem;
  color: #7f8c8d;
  margin-bottom: 30px;
  padding: 0 10px;
}

.article-meta .author {
  font-weight: 600;
  color: #3498db;
}

.article-meta .date {
  font-style: italic;
  background: #ecf0f1;
  padding: 5px 10px;
  border-radius: 20px;
}

/* Article Content */
.article-content {
  font-size: 1.08rem;
  line-height: 1.9;
  color: #555;
}

.article-content p {
  text-align: justify;
}

/* Image Styling */
.article-content img {
  max-width: 100%;
  height: auto;
  border-radius: 10px;
  margin: 25px 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.article-content img:hover {
  transform: scale(1.03);
}

/* Video Styling */
.article-content iframe {
  width: 100%;
  height: 450px;
  border-radius: 10px;
  margin: 25px 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Error Message */
.error-message {
  color: #e74c3c;
  text-align: center;
  font-size: 1.5rem;
  margin: 50px;
  padding: 20px;
  background: #fceae9;
  border-radius: 10px;
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Style for the comment section */
#comment {
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 30px;
}

/* Title style */
#comment h3 {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}

/* Styling for each comment */
.comment {
  background-color: #fff;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Author and date styling */
.comment p {
  font-size: 14px;
  color: #555;
  line-height: 1.5;
  margin-bottom: 10px;
}

/* Bold the comment author's name */
.comment p strong {
  font-weight: bold;
  color: #007474;
}

/* Style for the comment text */
.comment p:last-of-type {
  font-size: 16px;
  color: #333;
}

/* To make the no comments message stand out */
.comment p.no-comments {
  color: #999;
  font-style: italic;
}

/* Style for comment container when no comments are present */
.comment p.no-comments {
  text-align: center;
  font-size: 16px;
}

.custom-button {
  display: inline-block;
  padding: 5px 30px;
  background-color: #007474;
  color: white;
  text-decoration: none;
  border-radius: 5px;
  text-align: center;
}

/* Responsive styling for smaller screens */
@media (max-width: 768px) {
  #comment {
    padding: 15px;
  }

  #comment h3 {
    font-size: 20px;
  }

  .comment {
    padding: 12px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .article-container {
    margin: 20px;
    padding: 20px;
  }

  .article-title {
    font-size: 2rem;
  }

  .article-meta {
    flex-direction: column;
    gap: 10px;
  }

  .article-content {
    font-size: 1rem;
  }

  .article-content iframe {
    height: 300px;
  }
}

/*responsive css*/

.filter-articles select,
.filter-articles button {
  width: 100%;
  margin-bottom: 10px;
}

/* For small screens */
@media (max-width: 768px) {
  .filter-articles {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-articles select {
    width: 100%;
  }

  .filter-articles button {
    width: 100%;
    margin-bottom: 10px;
  }

  .filter-articles div {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
  }

  /* Style the individual buttons */
  #list-view-btn,
  #card-view-btn {
    flex: 1; /* Ensures the buttons take up equal width */
  }

  #list-view-btn {
    margin-right: 5px;
  }
}

/* For medium screens (tablets and above) */
@media (min-width: 769px) and (max-width: 1024px) {
  .filter-articles {
    flex-direction: row;
    justify-content: space-between;
  }

  .filter-articles select {
    width: 48%;
    margin-bottom: 0;
  }

  .filter-articles div {
    display: inline-block;
  }

  /* Ensure buttons take 50% of available width */
  #list-view-btn,
  #card-view-btn {
    width: 48%;
  }
}

/* For large screens (desktops) */
@media (min-width: 1025px) {
  .filter-articles select {
    width: 23%; /* Adjust width of select fields */
  }

  .filter-articles button {
    width: auto;
  }

  .filter-articles div {
    margin-left: 20px;
  }

  /* Ensure buttons take normal size */
  #list-view-btn,
  #card-view-btn {
    width: auto;
  }
}

/* Styling for the comments section container */
.comments-section {
  margin-top: 20px;
  padding: 20px;

  border-radius: 5px;

  margin-left: auto;
  margin-right: auto;
}

/* Heading style */
.comments-section h3 {
  font-size: 1.5em;
  color: #333;
  margin-bottom: 15px;
  font-family: Arial, sans-serif;
}

/* Label styling */
.comments-section label {
  font-size: 1em;
  color: #555;
  font-weight: bold;
  margin-bottom: 5px;
  display: block;
}

/* Textarea styling */
.comments-section textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1em;
  font-family: Arial, sans-serif;
  resize: vertical; /* Allows vertical resizing only */
  box-sizing: border-box; /* Ensures padding doesn't affect width */
  background-color: #fff;
  transition: border-color 0.3s ease;
}

/* Textarea focus state */
.comments-section textarea:focus {
  border-color: #007474;
  outline: none;
}

/* Submit button styling */
.comments-section input {
  background-color: #007474;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 1em;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

/* Submit button hover state */
.comments-section input[type="submit"]:hover {
  background-color: #007474;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .comments-section {
    padding: 15px;
  }

  .comments-section textarea {
    width: 100%;
  }
}

/* Style for the comment section container */
.tab-content {
  padding: 20px;

  border-radius: 8px;
  margin-top: 20px;
}

/* Heading style */
.tab-content h3 {
  color: #333;
  font-size: 24px;
  margin-bottom: 20px;

  padding-bottom: 10px;
}
/* Individual comment box */
.comment-box {
  background: linear-gradient(135deg, #ffffff, #f9f9f9); /* Subtle gradient */
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); /* Softer shadow */
  transition: transform 0.2s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.comment-box:hover {
  transform: translateY(-5px); /* Slight lift effect */
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15); /* Enhanced shadow on hover */
}

/* Adding a subtle accent line */
.comment-box::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(
    to bottom,
    #007474,
    #ff8c2b
  ); /* Colorful accent */
}

/* Name styling */
.comment-box strong {
  color: #007474;
  font-size: 18px;
  font-weight: 600;
  margin-right: 8px;
  letter-spacing: 0.5px;
  transition: color 0.3s ease;
}

/* Comment text */
.comment-box p {
  margin: 12px 0;
  color: #444;
  line-height: 1.8;
  font-size: 15px;
  word-wrap: break-word;
  font-family: "Arial", sans-serif; /* Cleaner typography */
}

/* Timestamp */
.comment-box small {
  color: #999;
  font-size: 13px;
  display: block;
  font-style: italic;
  margin-top: 8px;
  transition: color 0.3s ease;
}

.comment-box small:hover {
  color: #666; /* Slight darken on hover */
}

/* Horizontal rule */
.comment-box hr {
  border: none;
  border-top: 1px dashed #ddd; /* Dashed for a playful touch */
  margin: 20px 0 0 0;
  opacity: 0.7;
}

/* No comments message */
.tab-content > p {
  color: #777;
  font-style: italic;
  font-size: 16px;
  padding: 20px;
  background: #fdfdfd;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  text-align: center;
}

/* Tab content container */
.tab-content {
  background: #fafafa;
  border-radius: 15px;
  padding: 25px;
}

/* Tab content heading */
.tab-content h3 {
  font-size: 24px;
  color: #333;
  font-weight: 700;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Responsive design */
@media (max-width: 768px) {
  .tab-content {
    padding: 15px;
    border-radius: 10px;
  }

  .comment-box {
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 8px;
  }

  .comment-box::before {
    width: 3px; /* Slimmer accent line */
  }

  .comment-box strong {
    font-size: 16px;
  }

  .comment-box p {
    font-size: 14px;
    line-height: 1.6;
  }

  .tab-content h3 {
    font-size: 20px;
    margin-bottom: 15px;
  }

  .tab-content > p {
    font-size: 14px;
    padding: 15px;
  }
}

/* Animation for comment box entrance */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.comment-box {
  animation: fadeInUp 0.5s ease-out forwards;
}

/* Custom CSS for History Section */
.history-section {
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.history-item {
  background: white;
  padding: 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.history-item:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.history-title {
  color: #198754 !important;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.history-title a {
  text-decoration: none !important;
  color: inherit;
}

.history-title a:hover {
  color: #155724 !important;
  text-decoration: underline !important;
}

.history-meta {
  color: #6c757d;
  font-size: 0.9rem;
  margin-left: 1rem;
}

.no-history {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .history-section {
    padding: 1rem;
  }

  .history-item {
    padding: 1rem;
  }

  .history-title {
    font-size: 1.25rem;
  }
}
