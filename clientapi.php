<?php
session_start();
header("Content-Type: application/json");
header("Access-Control-Allow-Origin: *"); // Adjust CORS as needed
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type");

// Database Connection Class
class Database
{
    private $conn;

    public function connect($host, $db_name, $username, $password)
    {
        try {
            $this->conn = new PDO(
                "mysql:host=$host;dbname=$db_name",
                $username,
                $password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            return $this->conn;
        } catch (PDOException $e) {
            throw new Exception("Connection failed: " . $e->getMessage());
        }
    }
}

// User API Class
class UserAPI
{
    private $allUsers = [
        [
            'id' => 1,
            'email' => '<EMAIL>',
            'name' => '<PERSON>',
            'password' => '$2y$10$Zvz/AidiKp6fefoKB.xCXejBgX1l.fOySKT68HUYp0EWZ.OzNjF3G',
            'product' => 'product1'
        ],
        [
            'id' => 2,
            'email' => '<EMAIL>',
            'name' => 'Jane Smith',
            'password' => '$2y$10$Zvz/AidiKp6fefoKB.xCXejBgX1l.fOySKT68HUYp0EWZ.OzNjF3G',
            'product' => 'product2'
        ],
        [
            'id' => 3,
            'email' => '<EMAIL>',
            'name' => 'Bob Jones',
            'password' => '$2y$10$YOUR_HASHED_PASSWORD_HERE', // Use password_hash('password3', PASSWORD_DEFAULT)
            'product' => 'product3'
        ]
    ];

    public function __construct()
    {
        // $this->loadAllUsers();
    }

    private function loadAllUsers()
    {
        $databases = [
            [
                'host' => 'localhost',
                'db_name' => 'product1_db',
                'username' => 'user1',
                'password' => 'pass1',
                'table' => 'users'
            ],
            [
                'host' => 'localhost',
                'db_name' => 'product2_db',
                'username' => 'user2',
                'password' => 'pass2',
                'table' => 'users'
            ],
            [
                'host' => 'localhost',
                'db_name' => 'product3_db',
                'username' => 'user3',
                'password' => 'pass3',
                'table' => 'users'
            ]
        ];

        $db = new Database();

        foreach ($databases as $index => $config) {
            try {
                $conn = $db->connect(
                    $config['host'],
                    $config['db_name'],
                    $config['username'],
                    $config['password']
                );

                $query = "SELECT id, email, name, password, 'product" . ($index + 1) . "' as product 
                         FROM " . $config['table'];
                $stmt = $conn->prepare($query);
                $stmt->execute();

                $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $this->allUsers = array_merge($this->allUsers, $users);

                $conn = null; // Close connection
            } catch (Exception $e) {
                // Log error but continue with other databases
                error_log("Database connection error for product " . ($index + 1) . ": " . $e->getMessage());
            }
        }
    }

    public function login($email, $password)
    {
        if (empty($email) || empty($password)) {
            return [
                "status" => "error",
                "message" => "Email and password are required"
            ];
        }

        foreach ($this->allUsers as $user) {
            if ($user['email'] === $email) {
                if (password_verify($password, $user['password'])) {
                    // Remove password from response
                    $userData = $user;
                    unset($userData['password']);
                    $_SESSION['user'] = $userData;
                    return [
                        "status" => "success",
                        "message" => "Login successful",
                        "data" => $user
                    ];
                } else {
                    return [
                        "status" => "error",
                        "message" => "Invalid password"
                    ];
                }
            }
        }

        return [
            "status" => "error",
            "message" => "User not found"
        ];
    }
}

// Handle API request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents("php://input"), true);

    $email = isset($input['email']) ? trim($input['email']) : '';
    $password = isset($input['password']) ? trim($input['password']) : '';

    $api = new UserAPI();
    $response = $api->login($email, $password);

    echo json_encode($response);
} else {
    echo json_encode([
        "status" => "error",
        "message" => "Method not allowed"
    ]);
}
?>