<?php
include "./includes/config.php";

// Get the userId from the URL
$userId = isset($_GET['userId']) ? (int) $_GET['userId'] : null;

if (!$userId) {
    // Redirect or show an error if userId is invalid
    // header("Location: some_page.php?error=" . urlencode("Invalid user ID."));
    exit;
}

try {
    $userId = mysqli_real_escape_string($con, $userId); // Sanitize!

    $sql = "SELECT * FROM tblusers WHERE user_id = ?"; // Use parameterized queries!
    $stmt = $con->prepare($sql);

    if ($stmt === false) {
        throw new Exception("Error preparing statement: " . $con->error);
    }

    $stmt->bind_param("i", $userId); // "s" indicates a string. Adapt to your data type.

    if (!$stmt->execute()) {
        throw new Exception("Error executing query: " . $stmt->error);
    }

    $result = $stmt->get_result();

    // Check if user exists
    if ($result->num_rows === 0) {
        header("Location: some_page.php?error=" . urlencode("User not found."));
        exit;
    }

    $user = $result->fetch_assoc();

    // Now $user contains the fetched data as an associative array.

    $stmt->close();
    $con->close();

} catch (Exception $e) {
    // Log the error and show a message
    error_log("Error fetching user: " . $e->getMessage());
    header("Location: some_page.php?error=" . urlencode("Error fetching user data."));
    exit;
}

?>



<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Profile</title>
    <link rel="stylesheet" href="./public/css/main.css">
    <link rel="stylesheet" href="./public/css/sidebar.css">
    <link rel="stylesheet" href="./public/css/common/popup.css" />
    <link rel="stylesheet" href="./public/css/common/input.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
        integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        .profile-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .profile-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 15px;
            background-color: #ddd;
            background-image: url("https://via.placeholder.com/120?text=User");
            background-size: cover;
            background-position: center;
        }

        .profile-header h1 {
            color: #333;
            margin: 0;
            font-size: 24px;
        }

        .edit-icon {
            position: absolute;
            top: 20px;
            right: 20px;
            cursor: pointer;
            font-size: 20px;
            color: #666;
        }

        .edit-icon:hover {
            color: #333;
        }

        .profile-field {
            margin-bottom: 20px;
        }

        .profile-field label {
            display: block;
            font-weight: bold;
            color: #555;
            margin-bottom: 5px;
        }

        .profile-field span {
            display: block;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
            color: #333;
        }

        .profile-field input {
            display: none;
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .profile-field select {
            display: none;
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .profile-field.editing span {
            display: none;
        }

        .profile-field.editing input {
            display: block;
        }

        .profile-field.editing select {
            display: block;
        }

        .currency-field span:before {
            content: "$";
            margin-right: 5px;
            color: #666;
        }

        .capacity-field span:after {
            content: " hrs/week";
            color: #666;
        }

        .buttons {
            display: none;
            text-align: center;
            margin-top: 30px;
        }

        .buttons.show {
            display: block;
        }

        .btn {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .btn-save {
            background-color: #2ecc71;
            color: white;
        }

        .btn-cancel {
            background-color: #e74c3c;
            color: white;
        }
    </style>
</head>

<body>
    <section class="wrapper">
        <div class="inner-wrapper" id="inner-project-div-998978">

            <div class="profile-container">
                <div class="profile-header">
                    <div class="profile-image"></div>
                    <h1>User Profile</h1>
                </div>
                <span class="edit-icon" onclick="toggleEditMode()">✎</span>

                <div class="profile-field" id="name">
                    <label>Name</label>
                    <span><?php echo $user['name']; ?></span>
                    <input type="text" value="<?php echo $user['name']; ?>" />
                </div>

                <div class="profile-field" id="email">
                    <label>Email</label>
                    <span><?php echo $user['email']; ?></span>
                    <input type="email" value="<?php echo $user['email']; ?>" />
                </div>

                <div class="profile-field" id="manager">
                    <label>Title</label>
                    <span><?php echo $user['title'] ?></span>
                </div>

                <div class="profile-field" id="role">
                    <label>Role</label>
                    <span><?php echo $user['role'] ?></span>
                </div>

                <div class="buttons" id="editButtons">
                    <button class="btn btn-save" onclick="saveChanges()">Save</button>
                    <button class="btn btn-cancel" onclick="cancelChanges()">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </section>

    <script>
        let isEditing = false;
        let originalValues = {};
        const userId = <?php echo json_encode($userId); ?>; // Pass userId from PHP

        function toggleEditMode() {
            isEditing = !isEditing;
            const fields = document.querySelectorAll(".profile-field");
            const buttons = document.getElementById("editButtons");

            fields.forEach((field) => {
                if (isEditing) {
                    field.classList.add("editing");
                    const span = field.querySelector("span");
                    originalValues[field.id] = span.textContent;
                } else {
                    field.classList.remove("editing");
                }
            });

            buttons.classList.toggle("show", isEditing);
        }

        function saveChanges() {
            const fields = document.querySelectorAll(".profile-field");
            let updatedData = { userId: userId };

            // Collect updated values
            fields.forEach((field) => {
                const input = field.querySelector("input, select"); // Include select for role
                const span = field.querySelector("span");
                const fieldName = field.id; // e.g., "nameField" -> "name"

                if (input) {
                    updatedData[fieldName] = input.value; // Store the new value
                    span.textContent = input.value; // Update the UI
                }
            });

            // Send the updated data to the backend
            fetch("update_profile.php", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(updatedData),
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert("Profile updated successfully!");
                        toggleEditMode(); // Exit edit mode
                    } else {
                        alert("Error updating profile: " + data.error);
                        cancelChanges(); // Revert changes on error
                    }
                })
                .catch(error => {
                    console.error("Error:", error);
                    alert("An error occurred while saving changes.");
                    cancelChanges(); // Revert changes on error
                });
        }

        function cancelChanges() {
            const fields = document.querySelectorAll(".profile-field");
            fields.forEach((field) => {
                const input = field.querySelector("input, select");
                if (input) {
                    input.value = originalValues[field.id];
                }
            });
            toggleEditMode();
        }

        function toggleSidebar() {
            const sidebar = document.getElementById("sidebar");
            const toggleBtn = document.getElementById("toggle-btn");
            const projectTable = document.getElementById("inner-project-div-998978");

            sidebar.classList.toggle("collapsed");
            toggleBtn.classList.toggle("collapsed");
            projectTable.classList.toggle("expanded");
        }
    </script>
</body>

</html>