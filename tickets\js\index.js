let filter = new SlimSelect({
    select: '#filter',
    settings: {
        placeholderText: "Select Filter",
    },
    allowDeselect: true,
    events: {
        afterChange: (newVal) => {
        }
    },
});

let tickets = document.querySelectorAll(".item");
tickets.forEach((ele) => {
    let status = new SlimSelect({
        select: ` #${ele.id} .status`,
        settings: {
            placeholderText: "Select Status",
        },
        data: [
            { html: '<option style="--bg-color: #3dcbf8; color:#3dcbf8; padding: .3rem 1rem; border-radius: 20px;">Open</option>', text: 'Open', value: '1' },
            { html: '<option style="--bg-color: #0beded; color:#0beded; padding: .3rem 1rem; border-radius: 20px;">In Progress</option>', text: 'In Progress', value: '2' },
            { html: '<option style="--bg-color: #ffc000; color:#ffc000; padding: .3rem 1rem; border-radius: 20px;">On Hold</option>', text: 'On Hold', value: '3' },
            { html: '<option style="--bg-color: #b23b00; color:#b23b00; padding: .3rem 1rem; border-radius: 20px;">Overdue</option>', text: 'Overdue', value: '4' },
            { html: '<option style="--bg-color: gray; color:black; padding: .3rem 1rem; border-radius: 20px;">Closed</option>', text: 'Closed', value: '5' }
        ],

        allowDeselect: true,
        events: {
            afterChange: (newVal) => {
            }
        },
    });


    let priority = new SlimSelect({
        select: `#${ele.id} .priority`,
        settings: {
            placeholderText: "Select Priority",
        },
        allowDeselect: true,
        events: {
            afterChange: (newVal) => {
            }
        },
    });
})

// Get all tab buttons
const tabButtons = document.querySelectorAll('.tab-button');

// Get all tab contents
const tabContents = document.querySelectorAll('.tab-content');

// Add click event listener to each tab button
tabButtons.forEach(button => {
    button.addEventListener('click', () => {
        // Remove 'active' class from all tab buttons
        tabButtons.forEach(btn => btn.classList.remove('active'));
        // Add 'active' class to the clicked tab button
        button.classList.add('active');

        // Hide all tab contents
        tabContents.forEach(content => content.classList.remove('active'));

        // Get the target div id from the button's data attribute
        const targetId = button.getAttribute('data-target');

        // Show the corresponding tab content
        const targetContent = document.getElementById(targetId);
        if (targetContent) {
            targetContent.classList.add('active');
        }
    });
});

const commentBox = new Quill('#comment-box', {
    modules: {
        toolbar: [
            [{ header: [1, 2, false] }],
            ['bold', 'italic', 'link', 'underline'], [{ list: 'ordered' }, { list: 'bullet' }],
            ['image', 'code-block'],
        ],
    },
    placeholder: 'Comment',
    theme: 'snow', // or 'bubble'
});

const resolustionbox = new Quill('#resolustion-box', {
    modules: {
        toolbar: [
            [{ header: [1, 2, false] }],
            ['bold', 'italic', 'link', 'underline'], [{ list: 'ordered' }, { list: 'bullet' }],
            ['image', 'code-block'],
        ],
    },
    placeholder: 'Resolution',
    theme: 'snow', // or 'bubble'
});

window.addEventListener("DOMContentLoaded", () => {
    const ctl = new CollapsibleTimeline("#timeline");
});

class CollapsibleTimeline {
    constructor(el) {
        this.el = document.querySelector(el);

        this.init();
    }
    init() {
        this.el?.addEventListener("click", this.itemAction.bind(this));
    }
    animateItemAction(button, ctrld, contentHeight, shouldCollapse) {
        const expandedClass = "timeline__item-body--expanded";
        const animOptions = {
            duration: 300,
            easing: "cubic-bezier(0.65,0,0.35,1)"
        };

        if (shouldCollapse) {
            button.ariaExpanded = "false";
            ctrld.ariaHidden = "true";
            ctrld.classList.remove(expandedClass);
            animOptions.duration *= 2;
            this.animation = ctrld.animate([
                { height: `${contentHeight}px` },
                { height: `${contentHeight}px` },
                { height: "0px" }
            ], animOptions);
        } else {
            button.ariaExpanded = "true";
            ctrld.ariaHidden = "false";
            ctrld.classList.add(expandedClass);
            this.animation = ctrld.animate([
                { height: "0px" },
                { height: `${contentHeight}px` }
            ], animOptions);
        }
    }
    itemAction(e) {
        const { target } = e;
        const action = target?.getAttribute("data-action");
        const item = target?.getAttribute("data-item");

        if (action) {
            const targetExpanded = action === "expand" ? "false" : "true";
            const buttons = Array.from(this.el?.querySelectorAll(`[aria-expanded="${targetExpanded}"]`));
            const wasExpanded = action === "collapse";

            for (let button of buttons) {
                const buttonID = button.getAttribute("data-item");
                const ctrld = this.el?.querySelector(`#item${buttonID}-ctrld`);
                const contentHeight = ctrld.firstElementChild?.offsetHeight;

                this.animateItemAction(button, ctrld, contentHeight, wasExpanded);
            }

        } else if (item) {
            const button = this.el?.querySelector(`[data-item="${item}"]`);
            const expanded = button?.getAttribute("aria-expanded");

            if (!expanded) return;

            const wasExpanded = expanded === "true";
            const ctrld = this.el?.querySelector(`#item${item}-ctrld`);
            const contentHeight = ctrld.firstElementChild?.offsetHeight;

            this.animateItemAction(button, ctrld, contentHeight, wasExpanded);
        }
    }
}