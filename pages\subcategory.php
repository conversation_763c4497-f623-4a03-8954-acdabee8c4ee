<?php
session_start();
include('../dist/include/config.php');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Celaeno HelpDesk - Article</title>
    <link rel="shortcut icon" href="/dist/images/favicon.ico">
    <script src="https://kit.fontawesome.com/1b5fdf4bb2.js" crossorigin="anonymous"></script>
    <link href="../dist/css/main.css" rel="stylesheet">
    <style>
        .col-md-6 {
            text-align: center;
        }

        .content {
            padding: 20px 0;
        }

        .flex-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .left-sidebar {
            flex: 1;
            min-width: 200px;
            max-width: 250px;
        }

        .main-content {
            flex: 2;
            min-width: 300px;
            max-width: 600px;
        }

        .nav-listing {
            list-style: none;
            padding: 0;
        }

        .nav-listing li {
            margin-bottom: 10px;
        }

        .nav-listing a {
            text-decoration: none;
            color: #007bff;
        }

        .nav-listing a:hover {
            text-decoration: underline;
        }

        .category-nav {
            background: #f8f9fa;
            padding: 10px;
            margin-bottom: 20px;
        }

        .category-nav a {
            margin-right: 15px;
            color: #333;
            text-decoration: none;
        }

        .category-nav a:hover {
            color: #007bff;
        }

        .article-content {
            margin-bottom: 20px;
        }

        .article-meta {
            font-size: 0.9em;
            color: #6c757d;
            margin-bottom: 15px;
        }

        .article-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .related-articles h3 {
            font-size: 1.2em;
            margin-bottom: 10px;
        }

        .white-background {
            background: #fff;
        }

        .paddingt-2 {
            padding-top: 20px;
        }
    </style>
</head>

<body>
    <section class="jumbotron jumbotron-fluid text-center">
        <nav class="ct-nav">
            <div class="navbar navbar-dark navbar-static-top navbar-expand-lg">
                <a href="/celaeno_helpdesk/index.php" class="navbar-brand">
                    <small>Celaeno Assist</small>
                </a>
                <div class="ct-nav-list">
                    <a href="../index.php" class="">Home</a>
                    <a href="../knowledgebase.php" class="">Knowledge Base</a>
                </div>
                <div class="ct-head-btn-01">
                    <!-- <button class="ct-help-btn">Sign In</button> -->
                </div>
            </div>
        </nav>
        <div class="cover"></div>
        <div class="container">
            <h1 class="jumbotron-heading">Welcome to Celaeno Assist</h1>
            <div class="row justify-content-center text-center">
                <div class="col-md-10">
                    <form id="searchForm" action="/" class="search-form" method="GET">
                        <div class="input-group search">
                            <input type="text" name="q" class="search-field" aria-label="Search field"
                                placeholder="Search anything here..." aria-required="true" required
                                autoComplete="off" />
                            <button class="input-group-addon" aria-label="Search">
                                <i class="fa fa-search"> </i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <div class="container">
        <div class="row content section paddingt-2 white-background">
            <div class="col-sm-12">
                <!-- Top Category Navigation (All Categories for the Open Product) -->
                <div class="category-nav">
                    <?php
                    $articleId = isset($_GET["nid"]) ? mysqli_real_escape_string($con, $_GET["nid"]) : null;
                    $subcatId = isset($_GET["subcatid"]) ? mysqli_real_escape_string($con, $_GET["subcatid"]) : null;
                    $areaId = null;
                    $categoryId = null;

                    if ($articleId) {
                        $articleQuery = "SELECT CategoryId, AreaId, SubCategoryId FROM tblarticles WHERE id = $articleId AND Is_Active = 1";
                        $articleResult = mysqli_query($con, $articleQuery);
                        if ($articleResult && mysqli_num_rows($articleResult) > 0) {
                            $article = mysqli_fetch_assoc($articleResult);
                            $areaId = $article["AreaId"];
                            $subcatId = $article["SubCategoryId"];
                            $categoryId = $article["CategoryId"];
                        }
                    } elseif ($subcatId) {
                        $subcatQuery = "SELECT CategoryId FROM tblsubcategory WHERE SubCategoryId = $subcatId AND Is_Active = 1";
                        $subcatResult = mysqli_query($con, $subcatQuery);
                        if ($subcatResult && mysqli_num_rows($subcatResult) > 0) {
                            $subcat = mysqli_fetch_assoc($subcatResult);
                            $categoryId = $subcat["CategoryId"];
                            $categoryAreaQuery = "SELECT AreaId FROM tblcategory WHERE CategoryId = $categoryId AND Is_Active = 1";
                            $categoryAreaResult = mysqli_query($con, $categoryAreaQuery);
                            if ($categoryAreaResult && mysqli_num_rows($categoryAreaResult) > 0) {
                                $categoryArea = mysqli_fetch_assoc($categoryAreaResult);
                                $areaId = $categoryArea["AreaId"];
                            }
                        }
                    }

                    if ($areaId) {
                        $allCategoriesQuery = "SELECT CategoryId, Category FROM tblcategory WHERE AreaId = $areaId AND Is_Active = 1";
                        $allCategoriesResult = mysqli_query($con, $allCategoriesQuery);
                        if ($allCategoriesResult && mysqli_num_rows($allCategoriesResult) > 0) {
                            while ($category = mysqli_fetch_assoc($allCategoriesResult)) {
                                echo '<a href="category.php?catid=' . $category["CategoryId"] . '">' . htmlspecialchars($category["Category"]) . '</a>';
                            }
                        } else {
                            echo '<span>No categories found</span>';
                        }
                    } else {
                        echo '<span>Area not found</span>';
                    }
                    ?>
                </div>

                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb" role="navigation">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../index.php">Home</a></li>
                        <?php
                        if ($categoryId) {
                            $categoryQuery = "SELECT Category FROM tblcategory WHERE CategoryId = $categoryId AND Is_Active = 1";
                            $categoryResult = mysqli_query($con, $categoryQuery);
                            if ($categoryResult && mysqli_num_rows($categoryResult) > 0) {
                                $category = mysqli_fetch_assoc($categoryResult);
                                echo '<li class="breadcrumb-item"><a href="category.php?catid=' . $categoryId . '">' . htmlspecialchars($category["Category"]) . '</a></li>';
                            }
                        }
                        if ($subcatId) {
                            $subcatQuery = "SELECT SubCategory FROM tblsubcategory WHERE SubCategoryId = $subcatId AND Is_Active = 1";
                            $subcatResult = mysqli_query($con, $subcatQuery);
                            if ($subcatResult && mysqli_num_rows($subcatResult) > 0) {
                                $subcat = mysqli_fetch_assoc($subcatResult);
                                echo '<li class="breadcrumb-item active">' . htmlspecialchars($subcat["SubCategory"]) . '</li>';
                            }
                        }
                        if ($articleId) {
                            $articleQuery = "SELECT ArticleTitle FROM tblarticles WHERE id = $articleId AND Is_Active = 1";
                            $articleResult = mysqli_query($con, $articleQuery);
                            if ($articleResult && mysqli_num_rows($articleResult) > 0) {
                                $article = mysqli_fetch_assoc($articleResult);
                                echo '<li class="breadcrumb-item active">' . htmlspecialchars($article["ArticleTitle"]) . '</li>';
                            }
                        }
                        ?>
                    </ol>
                </nav>

                <div class="flex-container">
                    <!-- Left Sidebar: Articles in the Open Subcategory -->
                    <div class="left-sidebar">
                        <h3>Articles</h3>
                        <ul class="nav flex-column articles-listing">
                            <?php
                            if ($subcatId) {
                                $articleQuery = "SELECT id, ArticleTitle FROM tblarticles WHERE SubCategoryId = $subcatId AND Is_Active = 1";
                                $articleResult = mysqli_query($con, $articleQuery);
                                if ($articleResult && mysqli_num_rows($articleResult) > 0) {
                                    while ($articleRow = mysqli_fetch_assoc($articleResult)) {
                                        echo '<li><a href="subcategory.php?subcatid=' . $subcatId . '&nid=' . $articleRow["id"] . '">' . htmlspecialchars($articleRow["ArticleTitle"]) . '</a></li>';
                                    }
                                } else {
                                    echo '<li>No articles available.</li>';
                                }
                            }
                            ?>
                        </ul>
                    </div>

                    <!-- Main Content: Open Article -->
                    <div class="main-content">
                        <?php
                        if ($articleId) {
                            $articleContentQuery = "SELECT ArticleTitle, ArticleDetails FROM tblarticles WHERE id = $articleId AND SubCategoryId = $subcatId AND Is_Active = 1";
                            $articleContentResult = mysqli_query($con, $articleContentQuery);
                            if ($articleContentResult && mysqli_num_rows($articleContentResult) > 0) {
                                $article = mysqli_fetch_assoc($articleContentResult);
                                echo '<h2>' . htmlspecialchars($article["ArticleTitle"]) . '</h2>';
                                echo '<div class="article-content">' . htmlspecialchars_decode($article["ArticleDetails"]) . '</div>';
                            } else {
                                echo '<p>Article not found.</p>';
                            }
                        }
                        ?>
                    </div>

                    <!-- Right Sidebar: Subcategories in the Open Category -->
                    <div class="right-sidebar">
                        <h3>Subcategories</h3>
                        <ul class="nav-listing">
                            <?php
                            if ($categoryId) {
                                $subcatListQuery = "SELECT SubCategoryId, SubCategory FROM tblsubcategory WHERE CategoryId = $categoryId AND Is_Active = 1";
                                $subcatListResult = mysqli_query($con, $subcatListQuery);
                                if ($subcatListResult && mysqli_num_rows($subcatListResult) > 0) {
                                    while ($subcatListRow = mysqli_fetch_assoc($subcatListResult)) {
                                        echo '<li><a href="subcategory.php?subcatid=' . $subcatListRow["SubCategoryId"] . '">' . htmlspecialchars($subcatListRow["SubCategory"]) . '</a></li>';
                                    }
                                } else {
                                    echo '<li>No subcategories available.</li>';
                                }
                            }
                            ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    Celaeno HelpDesk
                    <p class="mt-1"><strong><small class="text-muted">Copyright © 2024 , All Right Reserved by <a href="#">Celaeno Technology</a></small></strong></p>
                </div>

            </div>
        </div>
    </footer>

    <!-- <script src="../dist/js/main.js"></script> -->
</body>

</html>