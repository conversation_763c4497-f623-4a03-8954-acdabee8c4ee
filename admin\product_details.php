<?php
session_start();
include('includes/config.php');

// Enable error reporting for debugging (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (empty($_SESSION['login'])) {
    header('Location: index.php');
    exit();
}

// Get product ID from URL
$productId = isset($_GET['id']) ? mysqli_real_escape_string($con, $_GET['id']) : '';

// Fetch product name for display
$productQuery = "SELECT ProductName FROM tblarea WHERE id = '$productId' AND Is_Active = 1";
$productResult = mysqli_query($con, $productQuery);
$productName = '';
if ($productResult && mysqli_num_rows($productResult) > 0) {
    $productRow = mysqli_fetch_assoc($productResult);
    $productName = htmlentities($productRow['ProductName']);
} else {
    // Redirect if product not found or inactive
    header('Location: products.php'); // Assuming you have a products.php listing all products
    exit();
}

// Fetch categories for the specific product
$categoryQuery = "SELECT c.CategoryId, c.Category
                  FROM tblcategory c
                  WHERE c.Is_Active = 1 AND c.AreaId = '$productId'
                  ORDER BY c.Category";
$categoryResult = mysqli_query($con, $categoryQuery);

// Fetch subcategories for the selected category
$selectedCategory = isset($_GET['category']) ? mysqli_real_escape_string($con, $_GET['category']) : '';
$subcategoryQuery = "";
$subcategoryResult = null;

if (!empty($selectedCategory)) {
    $subcategoryQuery = "SELECT s.SubCategoryId, s.SubCategory, s.SubCategoryDescription
                        FROM tblsubcategory s
                        WHERE s.Is_Active = 1 AND s.CategoryId = '$selectedCategory'
                        ORDER BY s.SubCategory";
    $subcategoryResult = mysqli_query($con, $subcategoryQuery);
}
?>

<!DOCTYPE html>
<html>

<head>
    <title>Knowledge Base - <?php echo $productName; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/knowledge-base.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .sidebar {
            max-width: 250px;
            background-color: #f8f9fa;
            border-right: 1px solid #e9ecef;
        }

        .sidebar .list-group-item {
            border: none;
            padding: 1rem;
            font-weight: 500;
            color: #343a40;
            transition: background-color 0.3s ease, color 0.3s ease;
            border-radius: 0.5rem;
        }

        .sidebar .list-group-item:hover,
        .sidebar .list-group-item.active {
            background-color: #007474;
            color: white;
        }

        .sidebar .list-group-item.active {
            box-shadow: 0 0.5rem 1rem rgba(0, 123, 255, 0.15);
        }

        .content {
            flex-grow: 1;
            padding: 2rem;
        }

        .page-title-box {
            margin-bottom: 2rem;

        }

        .page-title {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .page-button {
            float: right;
        }

        .subcategory-section {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }

        .subcategory-card {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            transition: background 0.3s ease;
            cursor: pointer;
        }

        .subcategory-card:hover {
            background: #e0f7e9;
        }

        .subcategory-card h3 {
            font-size: 1.95rem;
            color: #007474;
            margin-bottom: 0.5rem;
        }

        .subcategory-card p {
            font-size: 1rem;
            color: #495057;
            margin-bottom: 0;
        }

        .clearfix {
            margin-bottom: 1px;
        }
    </style>
</head>

<body class="fixed-left">
    <div id="wrapper">
        <?php include('includes/topheader.php'); ?>
        <?php include('includes/leftsidebar.php'); ?>

        <div class="content-page">
            <div class="content">
                <div class="container">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="page-title-box">
                                <div class="page-button">
                                    <h4 class="page-title">

                                        <a href="knowledge-base.php" class="btn btn-sm btn-default waves-effect waves-light pull-right">
                                            <i class="fa fa-arrow-left"></i> Back to Products
                                        </a>
                                    </h4>
                                </div>
                                <ol class="breadcrumb p-0 m-0">
                                    <li class="breadcrumb-item"><a href="knowledge-base.php">Products</a></li>
                                    <!-- <li class="breadcrumb-item"><?php echo $productName; ?></li> -->
                                    <?php if (!empty($selectedCategory)) : ?>
                                        <li class="breadcrumb-item active">
                                            <?php
                                            $catNameQuery = "SELECT Category FROM tblcategory WHERE CategoryId = '$selectedCategory'";
                                            $catNameResult = mysqli_query($con, $catNameQuery);
                                            $catNameRow = mysqli_fetch_assoc($catNameResult);
                                            echo htmlentities($catNameRow['Category']);
                                            ?>
                                        </li>
                                    <?php endif; ?>
                                </ol>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <nav class="col-md-4 col-lg-3 sidebar">
                            <input type="text" class="form-control mb-3" placeholder="Filter Categories" id="categoryFilter">
                            <ul class="list-group">
                                <?php
                                if (mysqli_num_rows($categoryResult) > 0) {
                                    while ($category = mysqli_fetch_array($categoryResult)) {
                                ?>
                                        <li class="list-group-item <?php echo ($selectedCategory == $category['CategoryId']) ? 'active' : ''; ?>">
                                            <a href="?id=<?php echo urlencode($productId); ?>&category=<?php echo urlencode($category['CategoryId']); ?>" style="text-decoration: none; color: inherit;">
                                                <?php echo htmlentities($category['Category']); ?>
                                            </a>
                                        </li>
                                <?php
                                    }
                                } else {
                                    echo "<li class=\"list-group-item\">No categories found for this product</li>";
                                }
                                ?>
                            </ul>
                        </nav>

                        <main class="col-md-8 col-lg-9">
                            <?php
                            if (!empty($selectedCategory) && $subcategoryResult) {
                                if (mysqli_num_rows($subcategoryResult) > 0) {
                                    echo "<h3 class='subcategory-title'>Subcategories</h3>";
                                    echo "<section class='subcategory-section'>";
                                    while ($subcategory = mysqli_fetch_array($subcategoryResult)) {
                            ?>
                                        <div class="subcategory-card">
                                            <a href="subcategory-articles.php?id=<?php echo urlencode($productId); ?>&subcategory=<?php echo urlencode($subcategory['SubCategoryId']); ?>&category=<?php echo urlencode($selectedCategory); ?>" style="text-decoration: none; color: inherit;">
                                                <h3><?php echo htmlentities($subcategory['SubCategory']); ?></h3>
                                                <p><?php echo htmlentities($subcategory['SubCategoryDescription']); ?></p>
                                            </a>
                                        </div>
                            <?php
                                    }
                                    echo "</section>";
                                } else {
                                    echo "<p>No subcategories found for this category.</p>";
                                }
                            } else {
                                echo "<p>Please select a category to view subcategories.</p>";
                            }
                            ?>
                        </main>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('categoryFilter').addEventListener('input', function(e) {
            let filter = e.target.value.toLowerCase();
            let items = document.querySelectorAll('.list-group-item');
            items.forEach(item => {
                let text = item.textContent.toLowerCase();
                item.style.display = text.includes(filter) ? '' : 'none';
            });
        });
    </script>
    <!-- jQuery  -->
    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/detect.js"></script>
    <script src="assets/js/fastclick.js"></script>
    <script src="assets/js/jquery.blockUI.js"></script>
    <script src="assets/js/waves.js"></script>
    <script src="assets/js/jquery.slimscroll.js"></script>
    <script src="assets/js/jquery.scrollTo.min.js"></script>

    <!-- App js -->
    <script src="assets/js/jquery.core.js"></script>
    <script src="assets/js/jquery.app.js"></script>
</body>

</html>