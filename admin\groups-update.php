<?php
require_once('./includes/config.php');
require_once("./utils/groups-functions.php");


$group_id = $_GET['groupId'];
$group = getGroupById($con, $group_id);

// Fetch current team members for the group
$currentMembersSql = "SELECT gtm.user_id
                     FROM tblgroup_team_member gtm
                     WHERE gtm.group_id = ?";
$currentMembersStmt = $con->prepare($currentMembersSql);
$currentMembersStmt->bind_param("i", $group_id);
$currentMembersStmt->execute();
$currentMembersResult = $currentMembersStmt->get_result();
$currentMemberIds = [];
while ($row = $currentMembersResult->fetch_assoc()) {
    $currentMemberIds[] = $row['user_id'];
}
$currentMembersStmt->close();

// Fetch all users, excluding the ones already in the group
$allUsersSql = "SELECT user_id, name FROM tblusers WHERE user_id NOT IN (" . implode(',', array_map('intval', $currentMemberIds)) . ")";
if (empty($currentMemberIds)) {
    $allUsersSql = "SELECT user_id, name FROM tblusers"; // If no members yet, fetch all
}
$allUsersResult = $con->query($allUsersSql);
$availableUsers = [];
if ($allUsersResult) {
    while ($row = $allUsersResult->fetch_assoc()) {
        $availableUsers[$row['user_id']] = $row['name'];
    }
    $allUsersResult->free();
} else {
    error_log("Error fetching available users: " . $con->error);
}


// Fetch current team members for the table
$currentMembersSql = "SELECT gtm.user_id, u.name AS user_name
                     FROM tblgroup_team_member gtm
                     JOIN tblusers u ON gtm.user_id = u.user_id
                     WHERE gtm.group_id = ?";
$currentMembersStmt = $con->prepare($currentMembersSql);
$currentMembersStmt->bind_param("i", $group_id);
$currentMembersStmt->execute();
$currentMembersResult = $currentMembersStmt->get_result();
$currentMembers = $currentMembersResult->fetch_all(MYSQLI_ASSOC);
$currentMembersStmt->close();

?>

<head>

    <title> Manage Resources</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../plugins/switchery/switchery.min.css">
    <script src="assets/js/modernizr.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
        integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        /* Table css */
        .table-gen {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-family: Arial, sans-serif;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            overflow: hidden;
        }

        .table-gen th,
        .table-gen td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .table-gen th {
            background-color: #007474;
            color: white;
            font-weight: 600;
        }

        .table-gen td {
            color: #333;
        }

        .table-gen tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .table-gen tr:hover {
            background-color: #f1f3f5;
            transition: background-color 0.2s ease;
        }

        /* End */
    </style>

</head>

<body class="fixed-left">

    <!-- Begin page -->
    <div id="wrapper">

        <!-- Top Bar Start -->
        <?php include('includes/topheader.php'); ?>

        <!-- ========== Left Sidebar Start ========== -->
        <?php include('includes/leftsidebar.php'); ?>
        <!-- Left Sidebar End -->

        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->
        <div class="content-page">
            <!-- Start content -->
            <div class="content">
                <div class="container">

                    <div class="row">
                        <div class="col-xs-12">
                            <div class="page-title-box">
                                <ol class="breadcrumb p-0 m-0">
                                    <li>
                                        <a href="./admin-page.php">Admin</a>
                                    </li>
                                    <li>
                                        <a href="./groups-view.php">Groups</a>
                                    </li>

                                    <li class="active">
                                        Update Groups
                                    </li>
                                </ol>
                                <div class="clearfix"></div>
                            </div>
                        </div>

                    </div>


                    <!-- Groups containers -->
                    <section class="row">
                        <div class="col-sm-12">
                            <form action="./groups-operations.php" style="width: 30%;" method="POST">
                                <input type="hidden" name="group_id" value="<?php echo htmlspecialchars($group_id); ?>">
                                <div class="form-group">
                                    <label for="name">Name</label>
                                    <input type="text" name="name" id="name" class="form-control"
                                        value="<?php echo $group['name']; ?>">
                                </div>
                                <div class="form-group">
                                    <label for="description">Description</label>
                                    <textarea name="description" id="description"
                                        class="form-control"><?php echo $group['description']; ?></textarea>
                                </div>
                                <div class="form-group">
                                    <button type="submit" name="update-group"
                                        class="btn btn-success waves-effect waves-light">
                                        Save
                                    </button>
                                </div>
                            </form>
                        </div>
                        <div class="col-sm-12">
                            <div class="">

                                <div class="add-member-form">
                                    <form action="./groups-operations.php" method="post">
                                        <input type="hidden" name="group_id"
                                            value="<?php echo htmlspecialchars($group_id); ?>">
                                        <label for="user_id">Select Team Member:</label>
                                        <select name="user_id" id="user_id">
                                            <option value="">-- Select User --</option>
                                            <?php if (!empty($availableUsers)): ?>
                                                <?php foreach ($availableUsers as $user_id => $user_name): ?>
                                                    <option value="<?php echo htmlspecialchars($user_id); ?>">
                                                        <?php echo htmlspecialchars($user_name); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <option value="">No users available to add</option>
                                            <?php endif; ?>
                                        </select>
                                        <button type="submit" name="add-team-member">Add Member</button>
                                    </form>
                                    <?php if (isset($addMemberError)): ?>
                                        <p style="color: red;"><?php echo htmlspecialchars($addMemberError); ?></p>
                                    <?php endif; ?>
                                    <?php if (isset($addMemberSuccess)): ?>
                                        <p style="color: green;"><?php echo htmlspecialchars($addMemberSuccess); ?></p>
                                    <?php endif; ?>
                                </div>

                                <div class="current-members-table">
                                    <h4>Team Members</h4>
                                    <?php if (!empty($currentMembers)): ?>
                                        <table class="table-gen">
                                            <thead>
                                                <tr>
                                                    <th>User ID</th>
                                                    <th>Name</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($currentMembers as $member): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($member['user_id']); ?></td>
                                                        <td><?php echo htmlspecialchars($member['user_name']); ?></td>
                                                        <td><a
                                                                href="./groups-operations.php?groupId=<?php echo htmlspecialchars($group_id); ?>&remove_member=<?php echo htmlspecialchars($member['user_id']); ?>">Remove</a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    <?php else: ?>
                                        <p>No team members added yet.</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </section>



                    <script>
                        var resizefunc = [];
                    </script>

                    <!-- jQuery  -->
                    <script src="assets/js/jquery.min.js"></script>
                    <script src="assets/js/bootstrap.min.js"></script>
                    <script src="assets/js/detect.js"></script>
                    <script src="assets/js/fastclick.js"></script>
                    <script src="assets/js/jquery.blockUI.js"></script>
                    <script src="assets/js/waves.js"></script>
                    <script src="assets/js/jquery.slimscroll.js"></script>
                    <script src="assets/js/jquery.scrollTo.min.js"></script>
                    <script src="../plugins/switchery/switchery.min.js"></script>

                    <!-- App js -->
                    <script src="assets/js/jquery.core.js"></script>
                    <script src="assets/js/jquery.app.js"></script>

</body>