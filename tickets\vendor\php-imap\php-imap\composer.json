{"name": "php-imap/php-imap", "description": "Manage mailboxes, filter/get/delete emails in PHP (supports IMAP/POP3/NNTP)", "keywords": ["PHP", "mail", "IMAP", "POP3", "mailbox", "receive emails"], "homepage": "https://github.com/barbushin/php-imap", "license": "MIT", "type": "library", "authors": [{"name": "<PERSON>", "homepage": "http://linkedin.com/in/barbushin", "email": "<EMAIL>"}], "config": {"sort-packages": true}, "require": {"php": "^7.4 || ^8.0", "ext-fileinfo": "*", "ext-iconv": "*", "ext-imap": "*", "ext-mbstring": "*", "ext-json": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.4", "maglnet/composer-require-checker": "^2.0|^3.2", "nikic/php-parser": "^4.3,<4.7|^4.10", "paragonie/hidden-string": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpunit/phpunit": "^8.5|^9.5", "povils/phpmnd": "^2.2", "psalm/plugin-phpunit": "^0.10.0|^0.15.1", "roave/security-advisories": "dev-master", "sebastian/phpcpd": "^4.1|^6.0"}, "scripts": {"static-analysis": ["parallel-lint .php-cs-fixer.dist.php src tests examples", "phpcpd src tests", "composer-require-checker check --config-file=composer-require-checker.config.json ./composer.json", "phpmnd ./ --exclude=./.github/ --exclude=./examples/ --exclude=./vendor/ --non-zero-exit-on-violation --hint", "php-cs-fixer fix --allow-risky=yes --no-interaction --dry-run -v", "psalm --show-info=false"], "tests": ["@static-analysis", "phpunit --testdox"]}, "suggest": {"ext-fileinfo": "To facilitate IncomingMailAttachment::getFileInfo() auto-detection"}, "autoload-dev": {"psr-4": {"PhpImap\\": "tests/unit"}}, "autoload": {"psr-4": {"PhpImap\\": "src/PhpImap"}}}