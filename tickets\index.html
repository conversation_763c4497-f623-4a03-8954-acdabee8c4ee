<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HelpDesk Tickets</title>
  <link href="https://unpkg.com/slim-select@latest/dist/slimselect.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/quill@2.0.1/dist/quill.snow.css" rel="stylesheet" />
  <link rel="stylesheet" href="css/style.css">
</head>

<body>
  <header>
    <div class="filter-con">
      <select name="filter" id="filter">
        <option value="all">All Tickets</option>
        <option value="Closed">Closed Tickets</option>
        <optgroup label="My tickets">
          <option value="Open">Open</option>
          <option value="Hold">Hold</option>
          <option value="Resolved">Resolved</option>
          <option value="Closed">Closed</option>
          <option value="Overdue">Overdue</option>
        </optgroup>
      </select>
    </div>
    <button>Add Tickets</button>
  </header>

  <main>
    <div class="list">
      <div class="item" id="t1">
        <div class="content">
          <div class="title">#101 Timesheet bug</div>
          <div class="other">
            <span class="created-by">Kishan</span>
            <div class="time">12 Jan 2024</div>
          </div>
        </div>

        <div class="actions">
          <select name="status" class="status">
            <option value="1">Open</option>
            <option value="2">In Progress</option>
            <option value="3">On Hold</option>
            <option value="4">Overdue</option>
            <option value="5">Closed</option>
          </select>
          <select name="priority" class="priority">
            <option value="0">Low</option>
            <option value="1">Medium</option>
            <option value="2">High</option>
            <option value="3">Critical</option>
          </select>
          <div class="assignee">
            <img src="./img/person.jpg" alt="">
          </div>
        </div>

      </div>

      <div class="item" id="t2">
        <div class="content">
          <div class="title">#101 Timesheet bug</div>
          <div class="other">
            <span class="created-by">Kishan</span>
            <div class="time">12 Jan 2024</div>
          </div>


        </div>

        <div class="actions">
          <select name="status" class="status">
            <option value="1">Open</option>
            <option value="2">In Progress</option>
            <option value="3">On Hold</option>
            <option value="4">Overdue</option>
            <option value="5">Closed</option>
          </select>
          <select name="priority" class="priority">
            <option value="0">Low</option>
            <option value="1">Medium</option>
            <option value="2">High</option>
            <option value="3">Critical</option>
          </select>

          <div class="assignee">
            <img src="./img/person.jpg" alt="">
          </div>
        </div>
      </div>

      <div class="item" id="t3">
        <div class="content">
          <div class="title">#101 Timesheet </div>
          <div class="other">
            <span class="created-by">Kishan</span>
            <div class="time">12 Jan 2024</div>
          </div>
        </div>

        <div class="actions">
          <select name="status" class="status">
            <option class="open" value="1">Open</option>
            <option class="inprogress" value="2">In Progress</option>
            <option class="onhold" value="3">On Hold</option>
            <option class="overdue" value="4">Overdue</option>
            <option class="closed" value="5">Closed</option>
          </select>
          <select name="priority" class="priority">
            <option value="0">Low</option>
            <option value="1">Medium</option>
            <option value="2">High</option>
            <option value="3">Critical</option>
          </select>

          <div class="assignee">
            <img src="./img/person.jpg" alt="">
          </div>
        </div>
      </div>
    </div>

    <div class="pop-up">
      <div class="main">
        <div class="heading">
          <h2 class="title">#101 Timesheet bug</h2>
        </div>
        <div class="tabs">
          <button class="tab-button active" data-target="conversation">Conversation</button>
          <button class="tab-button" data-target="resolustion">Resolution</button>
          <button class="tab-button" data-target="attachment">Attachment</button>
          <button class="tab-button" data-target="approval">Approval</button>
          <button class="tab-button" data-target="history">History</button>
        </div>

        <div class="content">
          <div id="conversation" class="tab-content active">
            <div id="comment-box"></div>

            <div class="comment">
              <div class="img">
                <img src="./img/person.jpg" alt="">
              </div>
              <div class="content-con">
                <div class="info">
                  <div class="name">Kishan</div>
                  <div class="time">1:00 am - 20 Mar 2024</div>
                </div>
                <div class="msg">
                  Lorem ipsum dolor sit amet consectetur adipisicing elit. Sint suscipit enim illo
                  illum minus vero minima veniam ipsam dolorem obcaecati maiores modi maxime
                  distinctio nihil, alias corrupti nulla odio itaque?
                </div>
              </div>
            </div>
          </div>

          <div id="resolustion" class="tab-content">
            <div id="resolustion-box"></div>
          </div>
          <div id="attachment" class="tab-content"></div>
          <div id="approval" class="tab-content"></div>
          <div id="history" class="tab-content">
            <svg display="none">
              <symbol id="arrow">
                <polyline points="7 10,12 15,17 10" fill="none" stroke="currentcolor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" />
              </symbol>
            </svg>
            <div id="timeline" class="timeline">
              <div class="btn-group">
                <button class="btn" type="button" data-action="expand">Expand All</button>
                <button class="btn" type="button" data-action="collapse">Collapse All</button>
              </div>
              <div class="timeline__item">
                <div class="timeline__item-header">
                  <button class="timeline__arrow" type="button" id="item1" aria-labelledby="item1-name" aria-expanded="false" aria-controls="item1-ctrld" aria-haspopup="true" data-item="1">
                    <svg class="timeline__arrow-icon" viewBox="0 0 24 24" width="24px" height="24px">
                      <use href="#arrow" />
                    </svg>
                  </button>
                  <span class="timeline__dot"></span>
                  <span id="item1-name" class="timeline__meta">
                    <time class="timeline__date" datetime="1970-01-01">January 1, 1970</time><br>
                    <strong class="timeline__title">Ticket closed</strong>
                  </span>
                </div>
                <div class="timeline__item-body" id="item1-ctrld" role="region" aria-labelledby="item1" aria-hidden="true">
                  <div class="timeline__item-body-content">
                    <p class="timeline__item-p">Lorem ipsum dolor sit amet consectetur adipisicing
                      elit. Numquam rem in eos, possimus rerum porro quisquam quae, nisi
                      laboriosam distinctio omnis repellat magni delectus voluptas aperiam soluta
                      doloribus excepturi quos?</p>
                  </div>
                </div>
              </div>
              <div class="timeline__item">
                <div class="timeline__item-header">
                  <button class="timeline__arrow" type="button" id="item2" aria-labelledby="item2-name" aria-expanded="false" aria-controls="item2-ctrld" aria-haspopup="true" data-item="2">
                    <svg class="timeline__arrow-icon" viewBox="0 0 24 24" width="24px" height="24px">
                      <use href="#arrow" />
                    </svg>
                  </button>
                  <span class="timeline__dot"></span>
                  <span id="item2-name" class="timeline__meta">
                    <time class="timeline__date" datetime="1973-10-17">October 17, 1973</time><br>
                    <strong class="timeline__title">Ticket on holds</strong>
                  </span>
                </div>
                <div class="timeline__item-body" id="item2-ctrld" role="region" aria-labelledby="item2" aria-hidden="true">
                  <div class="timeline__item-body-content">
                    <p class="timeline__item-p">Lorem ipsum dolor sit amet, consectetur adipisicing
                      elit. Nam explicabo quo deserunt, eligendi modi nostrum! Accusamus, animi,
                      dolorem suscipit incidunt, illum quasi recusandae nesciunt est et culpa fuga
                      veniam unde!</p>
                  </div>
                </div>
              </div>
              <div class="timeline__item">
                <div class="timeline__item-header">
                  <button class="timeline__arrow" type="button" id="item3" aria-labelledby="item3-name" aria-expanded="false" aria-controls="item3-ctrld" aria-haspopup="true" data-item="3">
                    <svg class="timeline__arrow-icon" viewBox="0 0 24 24" width="24px" height="24px">
                      <use href="#arrow" />
                    </svg>
                  </button>
                  <span class="timeline__dot"></span>
                  <span id="item3-name" class="timeline__meta">
                    <time class="timeline__date" datetime="2001-09-09">September 9, 2001</time><br>
                    <strong class="timeline__title">Ticket in progress</strong>
                  </span>
                </div>
                <div class="timeline__item-body" id="item3-ctrld" role="region" aria-labelledby="item3" aria-hidden="true">
                  <div class="timeline__item-body-content">
                    <p class="timeline__item-p">Lorem ipsum dolor sit amet consectetur adipisicing
                      elit. Reiciendis quos assumenda, aspernatur perspiciatis nesciunt ducimus
                      quia? Quo vel laborum reprehenderit quam, facere at cumque earum quasi cum,
                      voluptatibus repellendus rem?
                    </p>
                  </div>
                </div>
              </div>
              <div class="timeline__item">
                <div class="timeline__item-header">
                  <button class="timeline__arrow" type="button" id="item4" aria-labelledby="item4-name" aria-expanded="false" aria-controls="item4-ctrld" aria-haspopup="true" data-item="4">
                    <svg class="timeline__arrow-icon" viewBox="0 0 24 24" width="24px" height="24px">
                      <use href="#arrow" />
                    </svg>
                  </button>
                  <span class="timeline__dot"></span>
                  <span id="item4-name" class="timeline__meta">
                    <time class="timeline__date" datetime="2009-02-13">February 13, 2009</time><br>
                    <strong class="timeline__title">ticket assigned to jignesh</strong>
                  </span>
                </div>
                <div class="timeline__item-body" id="item4-ctrld" role="region" aria-labelledby="item4" aria-hidden="true">
                  <div class="timeline__item-body-content">
                    <p class="timeline__item-p">Lorem ipsum dolor sit, amet consectetur adipisicing
                      elit. Eveniet impedit dolorem provident officiis iusto aspernatur a cumque
                      voluptatem delectus dolore minima, corporis molestiae perferendis, eaque id
                      natus labore dicta ullam!.</p>
                  </div>
                </div>
              </div>
              <div class="timeline__item">
                <div class="timeline__item-header">
                  <button class="timeline__arrow" type="button" id="item5" aria-labelledby="item5-name" aria-expanded="false" aria-controls="item5-ctrld" aria-haspopup="true" data-item="5">
                    <svg class="timeline__arrow-icon" viewBox="0 0 24 24" width="24px" height="24px">
                      <use href="#arrow" />
                    </svg>
                  </button>
                  <span class="timeline__dot"></span>
                  <span id="item5-name" class="timeline__meta">
                    <time class="timeline__date" datetime="2033-05-18">May 18, 2033</time><br>
                    <strong class="timeline__title">ticket created</strong>
                  </span>
                </div>
                <div class="timeline__item-body" id="item5-ctrld" role="region" aria-labelledby="item5" aria-hidden="true">
                  <div class="timeline__item-body-content">
                    <p class="timeline__item-p">Lorem ipsum dolor sit amet consectetur adipisicing
                      elit. Fuga, eligendi sit? Soluta, maxime iste. Error vitae libero magnam
                      fuga dolor! Assumenda dolore dolor aperiam facilis impedit sunt placeat,
                      expedita magnam!</p>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
      <div class="sidebar">
        <div class="info">
          <span class="name">kishan</span>
          <span class="email"><EMAIL></span>
        </div>
        <div class="assignee-info">
          <span>assigned to</span>
          <b><img src="./img/person.jpg" alt=""><span>Jignesh</span></b>
        </div>
        <div class="status">
          <span>Status</span>
          <b>Open</b>
        </div>
        <div class="due">
          <span>Due Date</span>
          <b>12 Jun 2024</b>
        </div>

      </div>
    </div>
  </main>
  <script type="text/javascript" src="https://cdn.jsdelivr.net/jquery/latest/jquery.min.js"></script>
  <script src="https://unpkg.com/slim-select@latest/dist/slimselect.min.js"></script>
  <script src="js/index.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/quill@2.0.1/dist/quill.js"></script>
</body>

</html>