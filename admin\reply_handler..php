<?php
session_start();
include('includes/config.php');

// Check if the user is logged in
if (isset($_SESSION['user_id'])) {
    // Get the reply text and comment ID from the POST data
    $reply_text = $_POST['reply_text'];
    $comment_id = $_POST['id'];
    $user_id = $_SESSION['user_id'];  // Assuming the user is logged in and their user ID is stored in session

    // Sanitize the reply text
    $reply_text = htmlspecialchars($reply_text, ENT_QUOTES, 'UTF-8');

    // Insert the reply into the tblreplies table
    $query = "INSERT INTO tblreplies (id, user_id, reply, postingDate) VALUES (?, ?, ?, NOW())";
    $stmt = $con->prepare($query);
    $stmt->bind_param("iis", $id, $user_id, $reply_text);
    $stmt->execute();

    // Redirect back to the article page (assuming it’s handled by a GET request with articleId)
    header("Location: article.php?articleId=" . $_POST['articleId']);
} else {
    echo "You must be logged in to reply.";
}
