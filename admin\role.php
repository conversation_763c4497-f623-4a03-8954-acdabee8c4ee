<style>
    #role {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 20px auto;
        padding: 20px;
        background: #f9f9f9;
    }

    .title-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        background: #fff;
        padding: 10px;
        border: 1px solid #ddd;
    }

    .title-container label {
        font-weight: bold;
        margin-right: 10px;
        color: #333;
    }

    .title-container select {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        width: 200px;
    }

    .add {
        padding: 8px 20px;
        background: #007474;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.3s ease;
    }

    .role-detail-con {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        background: #fff;
        padding: 10px;
        border: 1px solid #ddd;
    }

    .role-detail span {
        margin-right: 10px;
        color: #333;
    }

    #edit-role {
        padding: 6px 15px;
        background: #007474;
        color: #fff;
        border: none;
        border-radius: 4px;
        cursor: not-allowed;
    }

    .role-table-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .module-permissions {
        background: #fff;
        border: 2px solid #e0ffff;
        padding: 15px;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .module-permissions h3 {
        margin: 0 0 10px 0;
        color: #333;
        font-size: 16px;
    }

    .permission-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .permission {
        display: flex;
        align-items: center;
    }

    .permission input[type="checkbox"] {
        margin-right: 8px;
        accent-color: #333;
    }

    .permission label {
        font-size: 14px;
        color: #333;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .title-container {
            flex-direction: column;
            gap: 10px;
        }

        .role-detail-con {
            flex-direction: column;
            gap: 10px;
        }

        .role-table-container {
            grid-template-columns: 1fr;
        }
    }
</style>
<div id="role">
    <div class="title-container">
        <div>
            <label for="role">Roles</label>
            <select onchange="ChangeDetailsOfRole(event);" id="role">
                <option value="#">SELECT ROLE</option>
                <option value="#">System Admin</option>
                <option value="#">Admin</option>
                <option value="#">Team Member</option>

a
            </select>
        </div>
        <button class="add" onclick="openPopUpWindow(action-role.php)">
            ADD ROLE
        </button>
    </div>
    <div class="role-detail-con">
        <div class="role-detail">
            <span class="roleid-det">-</span>
            <span class="rolename-det">-</span>
            <span class="roledesc-det">-</span>
        </div>
        <div>
            <button id="edit-role" disabled>Edit Role</button>
        </div>
    </div>

    <div class="role-table-container">
        <div class="module-permissions">
            <h3>Products</h3>
            <div class="permission-group">
                <div class="permission">
                    <input type="checkbox" value="1" />
                    <label for="VP">View</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="2" />
                    <label for="AEPRDE">Edit</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="3" />
                    <label for="pcreate">Create</label>
                </div>
            </div>
        </div>
        <div class="module-permissions">
            <h3>Category</h3>
            <div class="permission-group">
                <div class="permission">
                    <input type="checkbox" value="1" />
                    <label for="VP">View</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="2" />
                    <label for="AEPRDE">Edit</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="3" />
                    <label for="pcreate">Create</label>
                </div>
            </div>
        </div>

        <div class="module-permissions">
            <h3>Knowledge Base</h3>
            <div class="permission-group">
                <div class="permission">
                    <input type="checkbox" value="1" />
                    <label for="VP">View</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="2" />
                    <label for="AEPRDE">Edit</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="3" />
                    <label for="pcreate">Create</label>
                </div>
            </div>
        </div>

        <div class="module-permissions">
            <h3>Ticket</h3>
            <div class="permission-group">
                <div class="permission">
                    <input type="checkbox" value="4" />
                    <label for="VEC">View</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="5" />
                    <label for="cedit">Edit</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="6" />
                    <label for="ccreate">Create</label>
                </div>
            </div>
        </div>

        <div class="module-permissions">
            <h3>Report</h3>
            <div class="permission-group">
                <div class="permission">
                    <input type="checkbox" value="7" />
                    <label for="VE">View</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="8" />
                    <label for="AEE">Edit</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="9" />
                    <label for="ecreate">Create</label>
                </div>
            </div>
        </div>

        <div class="module-permissions">
            <h3>Review</h3>
            <div class="permission-group">
                <div class="permission">
                    <input type="checkbox" value="10" />
                    <label for="VRV">View</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="11" />
                    <label for="reedit">Approve / Reject</label>
                </div>
            </div>
        </div>

        <div class="module-permissions">
            <h3>Reports</h3>
            <div class="permission-group">
                <div class="permission">
                    <input type="checkbox" value="13" />
                    <label for="VR">View</label>
                </div>
            </div>
        </div>

        <div class="module-permissions">
            <h3>Users</h3>
            <div class="permission-group">
                <div class="permission">
                    <input type="checkbox" value="13" />
                    <label for="VR">View</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="13" />
                    <label for="VR">Edit</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="13" />
                    <label for="VR">Create</label>
                </div>
            </div>
        </div>

        <div class="module-permissions">
            <h3>Invite User</h3>
            <div class="permission-group">
                <div class="permission">
                    <input type="checkbox" value="19" />
                    <label for="tview">View</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="20" />
                    <label for="AET">Edit</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="21" />
                    <label for="tcreate">Create</label>
                </div>
            </div>
        </div>

        <div class="module-permissions">
            <h3>Allocation</h3>
            <div class="permission-group">
                <div class="permission">
                    <input type="checkbox" value="19" />
                    <label for="tview">View</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="20" />
                    <label for="AET">Edit</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="21" />
                    <label for="tcreate">Create</label>
                </div>
            </div>
        </div>

        <div class="module-permissions">
            <h3>Change Request</h3>
            <div class="permission-group">
                <div class="permission">
                    <input type="checkbox" value="28" />
                    <label for="AECR">View</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="29" />
                    <label for="AECR">Edit</label>
                </div>
                <div class="permission">
                    <input type="checkbox" value="30" />
                    <label for="AECR">Create</label>
                </div>
            </div>
        </div>
    </div>
</div>