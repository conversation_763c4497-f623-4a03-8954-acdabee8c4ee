<?php
session_start();
include('includes/config.php');
error_reporting(0);
if (strlen($_SESSION['login']) == 0) {
    header('location:index.php');
} else {
    if ($_GET['action'] == 'del' && $_GET['scid']) {
        $id = intval($_GET['scid']);
        $query = mysqli_query($con, "update  tblcategory set Is_Active='0' where CategoryId='$id'");
        $msg = "Category deleted ";
    }
    // Code for restore
    if ($_GET['resid']) {
        $id = intval($_GET['resid']);
        $query = mysqli_query($con, "update  tblcategory set Is_Active='1' where CategoryId='$id'");
        $msg = "Category restored successfully";
    }

    // Code for Forever deletionparmdel
    if ($_GET['action'] == 'perdel' && $_GET['scid']) {
        $id = intval($_GET['scid']);
        $query = mysqli_query($con, "delete from   tblcategory  where CategoryId='$id'");
        $delmsg = "Category deleted forever";
    }

?>
    <!DOCTYPE html>
    <html lang="en">

    <head>

        <title> News | Manage Categories</title>
        <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
        <link rel="stylesheet" href="../plugins/switchery/switchery.min.css">
        <script src="assets/js/modernizr.min.js"></script>

    </head>


    <body class="fixed-left">

        <!-- Begin page -->
        <div id="wrapper">

            <!-- Top Bar Start -->
            <?php include('includes/topheader.php'); ?>

            <!-- ========== Left Sidebar Start ========== -->
            <?php include('includes/leftsidebar.php'); ?>
            <!-- Left Sidebar End -->



            <!-- ============================================================== -->
            <!-- Start right Content here -->
            <!-- ============================================================== -->
            <div class="content-page">
                <!-- Start content -->
                <div class="content">
                    <div class="container">


                        <div class="row">
                            <div class="col-xs-12">
                                <div class="page-title-box">
                                    <!-- <h4 class="page-title">Manage Categories</h4> -->
                                    <ol class="breadcrumb p-0 m-0">
                                        <li>
                                            <a href="./admin-page.php">Admin</a>
                                        </li>

                                        <li class="active">
                                            Categories
                                        </li>
                                    </ol>
                                    <div class="clearfix"></div>
                                </div>
                            </div>
                        </div>
                        <!-- end row -->


                        <div class="row">
                            <div class="col-sm-6">

                                <?php if ($msg) { ?>
                                    <div class="alert alert-success" role="alert">
                                        <strong>Well done!</strong> <?php echo htmlentities($msg); ?>
                                    </div>
                                <?php } ?>

                                <?php if ($delmsg) { ?>
                                    <div class="alert alert-danger" role="alert">
                                        <strong>Oh snap!</strong> <?php echo htmlentities($delmsg); ?>
                                    </div>
                                <?php } ?>


                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="demo-box m-t-20">
                                        <div class="m-b-30">
                                            <a href="add-category.php">
                                                <button id="addToTable" class="btn btn-success waves-effect waves-light"
                                                    style="margin-bottom: 1rem; margin-left: auto; display: block;">
                                                    Add Categories
                                                    <i class="mdi mdi-plus-circle-outline"></i>
                                                </button>
                                            </a>
                                            <a href="add-subcategory.php">
                                                <button id="addToTable" class="btn btn-success waves-effect waves-light"
                                                    style="margin-bottom: 1rem; margin-left: auto; display: block;">
                                                    Add Sub Categories
                                                    <i class="mdi mdi-plus-circle-outline"></i>
                                                </button>
                                            </a>
                                        </div>
                                        <div class="table-responsive">
                                            <table class="table m-0 table-colored-bordered table-bordered-primary">
                                                <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        <th>Product</th>
                                                        <th>Category</th>
                                                        <th>Description</th>
                                                        <th>Accessibility</th>
                                                        <th>Status</th>
                                                        <th>Posting Date</th>
                                                        <th>Last updation Date</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php
                                                    $query = mysqli_query($con, "SELECT tblarea.ProductName AS catname, 
                                                  tblcategory.Category AS subcatname, 
                                                  tblcategory.CategoryDescription AS SubCatDescription, 
                                                  tblcategory.PostingDate AS subcatpostingdate, 
                                                  tblcategory.UpdationDate AS subcatupdationdate, 
                                                  tblcategory.CategoryId AS subcatid, 
                                                  tblcategory.Accessibility As subaccessability, 
                                                  tblcategory.Status AS substatus
                                                        FROM tblcategory 
                                                            JOIN tblarea ON tblcategory.AreaId = tblarea.id 
                                                            WHERE tblcategory.Is_Active = 1");
                                                    $cnt = 1;
                                                    $rowcount = mysqli_num_rows($query);
                                                    if ($rowcount == 0) {
                                                    ?>
                                                        <tr>

                                                            <td colspan="7" align="center">
                                                                <h3 style="color:red">No record found</h3>
                                                            </td>
                                                        <tr>
                                                            <?php
                                                        } else {

                                                            while ($row = mysqli_fetch_array($query)) {
                                                            ?>


                                                        <tr>
                                                            <th scope="row"><?php echo htmlentities($cnt); ?></th>
                                                            <td><?php echo htmlentities($row['catname']); ?></td>
                                                            <td><?php echo htmlentities($row['subcatname']); ?></td>
                                                            <td><?php echo htmlentities($row['SubCatDescription']); ?></td>
                                                            <td><?php echo htmlentities($row['subaccessability']); ?></td>
                                                            <td><?php echo htmlentities($row['substatus']); ?></td>
                                                            <td><?php echo htmlentities($row['subcatpostingdate']); ?></td>
                                                            <td><?php echo htmlentities($row['subcatupdationdate']); ?></td>
                                                            <td><a
                                                                    href="edit-category.php?scid=<?php echo htmlentities($row['subcatid']); ?>"><i
                                                                        class="fa fa-pencil" style="color: #29b6f6;"></i></a>
                                                                &nbsp;<a
                                                                    href="manage-categories.php?scid=<?php echo htmlentities($row['subcatid']); ?>&&action=del">
                                                                    <i class="fa fa-trash-o" style="color: #f05050"></i></a>
                                                            </td>
                                                        </tr>
                                                <?php
                                                                $cnt++;
                                                            }
                                                        } ?>
                                                </tbody>

                                            </table>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!--- end row -->



                            <div class="row">
                                <div class="col-md-12">
                                    <div class="demo-box m-t-20">
                                        <div class="m-b-30">

                                            <h4><i class="fa fa-trash-o"></i> Deleted Categories</h4>

                                        </div>

                                        <div class="table-responsive">
                                            <table class="table m-0 table-colored-bordered table-bordered-danger">
                                                <thead>
                                                    <tr>
                                                        <th>#</th>
                                                        <th>Product</th>
                                                        <th>Category</th>
                                                        <th>Description</th>
                                                        <th>Accessibility</th>
                                                        <th>Status</th>
                                                        <th>Posting Date</th>
                                                        <th>Last updation Date</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php
                                                    $query = mysqli_query($con, "SELECT tblarea.ProductName AS catname,
                                                   tblcategory.Category AS subcatname,
                                                   tblcategory.CategoryDescription AS SubCatDescription,
                                                   tblcategory.PostingDate AS subcatpostingdate,
                                                   tblcategory.UpdationDate AS subcatupdationdate,
                                                   tblcategory.CategoryId AS subcatid,
                                                   tblcategory.Accessibility AS subaccessibility ,
                                                   tblcategory.Status AS substatus
                                            FROM tblcategory
                                            JOIN tblarea ON tblcategory.AreaId = tblarea.id
                                            WHERE tblcategory.Is_Active = 0");
                                                    $cnt = 1;
                                                    $rowcount = mysqli_num_rows($query);
                                                    if ($rowcount == 0) {
                                                    ?>
                                                        <tr>

                                                            <td colspan="7" align="center">
                                                                <h3 style="color:red">No record found</h3>
                                                            </td>
                                                        <tr>
                                                            <?php
                                                        } else {

                                                            while ($row = mysqli_fetch_array($query)) {
                                                            ?>

                                                        <tr>
                                                            <th scope="row"><?php echo htmlentities($cnt); ?></th>
                                                            <td><?php echo htmlentities($row['catname']); ?></td>
                                                            <td><?php echo htmlentities($row['subcatname']); ?></td>
                                                            <td><?php echo htmlentities($row['SubCatDescription']); ?></td>
                                                            <td><?php echo htmlentities($row['subaccessability']); ?></td>
                                                            <td><?php echo htmlentities($row['substatus']); ?></td>
                                                            <td><?php echo htmlentities($row['subcatpostingdate']); ?></td>
                                                            <td><?php echo htmlentities($row['subcatupdationdate']); ?></td>
                                                            <td><a
                                                                    href="manage-categories.php?resid=<?php echo htmlentities($row['subcatid']); ?>"><i
                                                                        class="ion-arrow-return-right"
                                                                        title="Restore this Category"></i></a>
                                                                &nbsp;<a
                                                                    href="manage-categories.php?scid=<?php echo htmlentities($row['subcatid']); ?>&&action=perdel">
                                                                    <i class="fa fa-trash-o" style="color: #f05050"></i></a>
                                                            </td>
                                                        </tr>
                                                <?php
                                                                $cnt++;
                                                            }
                                                        } ?>
                                                </tbody>

                                            </table>
                                        </div>




                                    </div>

                                </div>


                            </div>

                        </div> <!-- container -->

                    </div> <!-- content -->


                    <!-- Sub Category Table content -->

                    <div class="container">
                        <div class="table-responsive">
                            <h4><i class="fa fa-list-alt" aria-hidden="true"></i> SubCategories</h4>
                            <table class="table m-0 table-colored-bordered table-bordered-primary">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Product</th>
                                        <th>Category</th>
                                        <th>Subcategory</th>
                                        <th>Subcategory Description</th>
                                        <th>Posting Date</th>
                                        <th>Last Updated</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $query = mysqli_query($con, "SELECT tblarea.ProductName AS productname,
                                                tblcategory.Category AS categoryname,
                                                tblsubcategory.SubCategory AS subcategoryname,
                                                tblsubcategory.SubCategoryDescription AS subcategorydesc,
                                                tblsubcategory.PostingDate AS postingdate,
                                                tblsubcategory.UpdationDate AS updationdate,
                                                tblsubcategory.SubCategoryId AS subcategoryid
                                                FROM tblsubcategory
                                                JOIN tblcategory ON tblsubcategory.CategoryId = tblcategory.CategoryId
                                                JOIN tblarea ON tblcategory.AreaId = tblarea.id
                                                WHERE tblsubcategory.Is_Active = 1");

                                    $cnt = 1;
                                    $rowcount = mysqli_num_rows($query);
                                    if ($rowcount == 0) {
                                    ?>
                                        <tr>
                                            <td colspan="8" align="center">
                                                <h3 style="color:red">No record found</h3>
                                            </td>
                                        </tr>
                                        <?php
                                    } else {
                                        while ($row = mysqli_fetch_array($query)) {
                                        ?>
                                            <tr>
                                                <th scope="row"><?php echo htmlentities($cnt); ?></th>
                                                <td><?php echo htmlentities($row['productname']); ?></td>
                                                <td><?php echo htmlentities($row['categoryname']); ?></td>
                                                <td><?php echo htmlentities($row['subcategoryname']); ?></td>
                                                <td><?php echo htmlentities($row['subcategorydesc']); ?></td>
                                                <td><?php echo htmlentities($row['postingdate']); ?></td>
                                                <td><?php echo htmlentities($row['updationdate']); ?></td>
                                                <td>
                                                    <a href="edit-subcategory.php?scid=<?php echo htmlentities($row['subcategoryid']); ?>">
                                                        <i class="fa fa-pencil" style="color: #29b6f6;"></i>
                                                    </a>
                                                    <a href="manage-categories.php?scid=<?php echo htmlentities($row['subcategoryid']); ?>&action=del">
                                                        <i class="fa fa-trash-o" style="color: #f05050"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                    <?php
                                            $cnt++;
                                        }
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>


                        <!-- Deleted Subcategories -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="demo-box m-t-20">
                                    <div class="m-b-30">
                                        <h4><i class="fa fa-trash-o"></i> Deleted Subcategories</h4>
                                    </div>

                                    <div class="table-responsive">
                                        <table class="table m-0 table-colored-bordered table-bordered-danger">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th>Product</th>
                                                    <th>Category</th>
                                                    <th>Subcategory</th>
                                                    <th>Subcategory Description</th>
                                                    <th>Posting Date</th>
                                                    <th>Last Updated</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                $query = mysqli_query($con, "SELECT tblarea.ProductName AS productname,
                                                tblcategory.Category AS categoryname,
                                                tblsubcategory.SubCategory AS subcategoryname,
                                                tblsubcategory.SubCategoryDescription AS subcategorydesc,
                                                tblsubcategory.PostingDate AS postingdate,
                                                tblsubcategory.UpdationDate AS updationdate,
                                                tblsubcategory.SubCategoryId AS subcategoryid
                                                FROM tblsubcategory
                                                JOIN tblcategory ON tblsubcategory.CategoryId = tblcategory.CategoryId
                                                JOIN tblarea ON tblcategory.AreaId = tblarea.id
                                                WHERE tblsubcategory.Is_Active = 0");

                                                $cnt = 1;
                                                $rowcount = mysqli_num_rows($query);
                                                if ($rowcount == 0) {
                                                ?>
                                                    <tr>
                                                        <td colspan="8" align="center">
                                                            <h3 style="color:red">No record found</h3>
                                                        </td>
                                                    </tr>
                                                    <?php
                                                } else {
                                                    while ($row = mysqli_fetch_array($query)) {
                                                    ?>
                                                        <tr>
                                                            <th scope="row"><?php echo htmlentities($cnt); ?></th>
                                                            <td><?php echo htmlentities($row['productname']); ?></td>
                                                            <td><?php echo htmlentities($row['categoryname']); ?></td>
                                                            <td><?php echo htmlentities($row['subcategoryname']); ?></td>
                                                            <td><?php echo htmlentities($row['subcategorydesc']); ?></td>
                                                            <td><?php echo htmlentities($row['postingdate']); ?></td>
                                                            <td><?php echo htmlentities($row['updationdate']); ?></td>
                                                            <td>
                                                                <a href="manage-subcategories.php?resid=<?php echo htmlentities($row['subcategoryid']); ?>">
                                                                    <i class="ion-arrow-return-right" title="Restore this Subcategory"></i>
                                                                </a>
                                                                <a href="manage-subcategories.php?scid=<?php echo htmlentities($row['subcategoryid']); ?>&action=perdel">
                                                                    <i class="fa fa-trash-o" style="color: #f05050"></i>
                                                                </a>
                                                            </td>
                                                        </tr>
                                                <?php
                                                        $cnt++;
                                                    }
                                                }
                                                ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>



                    </div>

                    <?php include('includes/footer.php'); ?>
                </div>

            </div>
            <!-- END wrapper -->



            <script>
                var resizefunc = [];
            </script>
            <script>
                // Check if the delete alert exists, then remove it after 4 seconds
                window.onload = function() {
                    var deleteAlert = document.getElementById('deleteAlert');
                    if (deleteAlert) {
                        setTimeout(function() {
                            deleteAlert.style.display = 'none'; // Hides the alert
                            // OR
                            // deleteAlert.remove(); // Completely removes the alert from DOM
                        }, 4000); // 4000 milliseconds = 4 seconds
                    }
                }
            </script>

            <!-- jQuery  -->
            <script src="assets/js/jquery.min.js"></script>
            <script src="assets/js/bootstrap.min.js"></script>
            <script src="assets/js/detect.js"></script>
            <script src="assets/js/fastclick.js"></script>
            <script src="assets/js/jquery.blockUI.js"></script>
            <script src="assets/js/waves.js"></script>
            <script src="assets/js/jquery.slimscroll.js"></script>
            <script src="assets/js/jquery.scrollTo.min.js"></script>


            <!-- App js -->
            <script src="assets/js/jquery.core.js"></script>
            <script src="assets/js/jquery.app.js"></script>

    </body>

    </html>
<?php } ?>