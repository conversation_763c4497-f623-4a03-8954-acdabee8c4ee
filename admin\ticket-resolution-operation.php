<?php
session_start();
include "./includes/config.php";

if (!isset($_SESSION['user_id'])) {
    header("Location: login.php"); // Redirect to login if not authenticated
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $ticketId = isset($_POST['ticketId']) ? trim($_POST['ticketId']) : null;
    $resolutionText = isset($_POST['newResolution']) ? trim($_POST['newResolution']) : '';

    if ($ticketId && $resolutionText) {
        // Sanitize resolution text (allow safe HTML)
        $allowedTags = '<p><br><strong><em><ul><ol><li>';
        $resolutionText = strip_tags($resolutionText, $allowedTags);

        // Check if a resolution already exists
        $stmt = $con->prepare("SELECT id FROM ticket_resolutions WHERE ticket_id = ?");
        $stmt->bind_param("s", $ticketId);
        $stmt->execute();
        $result = $stmt->get_result();
        $stmt->close();

        if ($result->num_rows > 0) {
            // Update existing resolution
            $stmt = $con->prepare("UPDATE ticket_resolutions SET resolution_text = ?, created_at = NOW() WHERE ticket_id = ?");
            $stmt->bind_param("ss", $resolutionText, $ticketId);
        } else {
            // Insert new resolution
            $stmt = $con->prepare("INSERT INTO ticket_resolutions (ticket_id, resolution_text, created_at) VALUES (?, ?, NOW())");
            $stmt->bind_param("ss", $ticketId, $resolutionText);
        }

        if ($stmt->execute()) {
            // Redirect to prevent form resubmission (Post/Redirect/Get pattern)
            header("Location: ticket.php?ticket_id=" . urlencode($ticketId));
            exit;
        } else {
            error_log("Error saving resolution: " . $stmt->error);
            header("Location: ticket.php?ticket_id=" . urlencode($ticketId) . "&error=Failed to save resolution");
            exit;
        }
        $stmt->close();
    } else {
        header("Location: ticket.php?ticket_id=" . urlencode($ticketId) . "&error=Invalid input");
        exit;
    }
} else {
    header("Location: ticket.php");
    exit;
}
