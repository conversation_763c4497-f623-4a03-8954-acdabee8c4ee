// Toggle Sidebar
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    const externalToggleBtn = document.getElementById('externalToggleBtn');
    
    sidebar.classList.toggle('collapsed');
    
    if (sidebar.classList.contains('collapsed')) {
        mainContent.style.marginLeft = '0';
        externalToggleBtn.style.display = 'block';
    } else {
        externalToggleBtn.style.display = 'none';
    }
}

// Show Ticket Details
function showTicketDetails(id, title, owner, date) {
    // Update ticket details
    document.getElementById('ticketTitle').textContent = `#${id} ${title}`;
    document.querySelector('.info-card p:nth-child(1)').innerHTML = `<strong>Owner</strong><span>${owner} • ${owner.toLowerCase()}.<EMAIL></span>`;
    document.querySelector('.info-card p:nth-child(2)').innerHTML = `<strong>Assigned To</strong><span>--</span>`;
    document.querySelector('.info-card p:nth-child(3)').innerHTML = `<strong>Status</strong><span class="status">In Progress</span>`;
    document.querySelector('.info-card p:nth-child(4)').innerHTML = `<strong>Channel</strong><span>WebForm</span>`;
    document.querySelector('.info-card p:nth-child(5)').innerHTML = `<strong>Product</strong><span>celano_booking</span>`;
    document.querySelector('.info-card p:nth-child(6)').innerHTML = `<strong>Type</strong><span>SUBSCRIPTION</span>`;
    document.querySelector('.info-card p:nth-child(7)').innerHTML = `<strong>Due Date</strong><span>--</span>`;

    // Highlight selected ticket
    const ticketItems = document.querySelectorAll('.ticket-item');
    ticketItems.forEach(item => item.classList.remove('active'));
    const selectedItem = Array.from(ticketItems).find(item => item.textContent.includes(id));
    if (selectedItem) {
        selectedItem.classList.add('active');
    }
}

// Toggle Theme
function toggleTheme() {
    document.body.classList.toggle('dark');
    localStorage.setItem('theme', document.body.classList.contains('dark') ? 'dark' : 'light');
}

// Load Theme Preference
document.addEventListener('DOMContentLoaded', () => {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark');
    }
});