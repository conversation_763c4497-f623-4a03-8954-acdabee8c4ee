<?php
session_start();
require_once('./includes/config.php');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create-department'])) {
        // Process data from the form
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        // $created_by = $_SESSION['logged_in_user']['id'] ?? null;
        // $updated_by = $_SESSION['logged_in_user']['id'] ?? null;

        $sql = "INSERT INTO tbldepartment (name, description) VALUES (?, ?)";
        $stmt = $con->prepare($sql);
        if ($stmt === false) {
            echo "Error preparing statement: " . $con->error;
        } else {
            $stmt->bind_param("ss", $name, $description);
            if ($stmt->execute()) {
                $department_id = $stmt->insert_id; // Get the ID of the newly inserted row
                echo `
                    <script>
                    alert('Department created successfully');
                    window.location.href = './departments-view.php';
                    </script>
                    `;
                header("Location: ./department-view.php");
                exit();

            } else {
                echo "Error inserting record: " . $stmt->error;
            }
            $stmt->close();
        }

    } else if (isset($_POST['update-department'])) {
        // Process data from the update form
        $department_id = $_POST['department_id'] ?? null;
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $updated_by = $_SESSION['logged_in_user']['id'] ?? null;


        if ($department_id === null || $updated_by === null) {
            echo "Error: Department ID or user session information is missing for update.";
            exit;
        }


        $sql = "UPDATE tbldepartment SET name = ?, description = ? WHERE id = ?";


        $stmt = $con->prepare($sql);

        if ($stmt === false) {

            echo "Error preparing update statement: " . $con->error;
        } else {
            $stmt->bind_param("ssi", $name, $description, $department_id);
            if ($stmt->execute()) {

                echo `
                    <script>
                    alert('Department updated successfully');
                    window.location.href = './departments-view.php';
                    </script>
                `;
                header("Location: ./department-update.php?departmentId=" . $department_id);
                exit();
            } else {

                echo "Error updating record: " . $stmt->error;
            }


            $stmt->close();
        }
    } else if (isset($_POST['add-group'])) {
        // Process data from the form to add a group to a department
        $department_id = $_POST['department_id'] ?? null;
        $group_id = $_POST['group_id'] ?? null;

        if ($department_id === null || $group_id === null) {
            echo "Error: Department ID or Group ID is missing.";
            exit;
        }

        $sql = "INSERT INTO tbldepartment_group (department_id, group_id) VALUES (?, ?)";
        $stmt = $con->prepare($sql);
        if ($stmt === false) {
            echo "Error preparing statement: " . $con->error;
        } else {
            $stmt->bind_param("ii", $department_id, $group_id);
            if ($stmt->execute()) {
                echo `
                    <script>
                    alert('Group added to department successfully');
                    window.location.href = './departments-view.php';
                    </script>
                `;
                header("Location: ./department-update.php?departmentId=" . $department_id);
                exit();
            } else {
                echo "Error inserting record: " . $stmt->error;
            }
            $stmt->close();
        }
    }
} else if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    if (isset($_GET['remove_group']) && isset($_GET['departmentId']) && is_numeric($_GET['remove_group']) && is_numeric($_GET['departmentId'])) {
        $group_to_remove = $_GET['remove_group'];
        $department_id = $_GET['departmentId'];
        $deleteSql = "DELETE FROM tbldepartment_group WHERE department_id = ? AND group_id = ?";
        $deleteStmt = $con->prepare($deleteSql);
        if ($deleteStmt === false) {
            echo "Error preparing delete statement: " . $con->error;
        } else {
            $deleteStmt->bind_param("ii", $department_id, $group_to_remove);
            if ($deleteStmt->execute()) {
                echo "<script>alert('Group removed from department successfully.'); window.location.reload();</script>";
                header("Location: ./department-update.php?departmentId=" . $department_id);
            } else {
                echo "Error removing group from department: " . $deleteStmt->error;
            }

            $deleteStmt->close();
        }
    }

} else {
    echo "Error: Invalid request method.";
}

?>