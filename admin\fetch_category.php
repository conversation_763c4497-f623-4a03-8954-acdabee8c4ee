<?php
// include('includes/config.php');

// if (isset($_POST['product_id'])) {
//     $productId = intval($_POST['product_id']); // Convert to integer for security

//     // Fetch categories linked to the selected product
//     $query = "SELECT CategoryId, Category FROM tblcategory WHERE AreaId = ? AND Is_Active = 1";
//     $stmt = mysqli_prepare($con, $query);
//     mysqli_stmt_bind_param($stmt, "i", $productId);
//     mysqli_stmt_execute($stmt);
//     $result = mysqli_stmt_get_result($stmt);

//     echo '<option value="">Select Category</option>';
//     while ($row = mysqli_fetch_assoc($result)) {
//         echo '<option value="' . htmlentities($row['CategoryId']) . '">' . htmlentities($row['Category']) . '</option>';
//     }
// }

include('includes/config.php');
if (isset($_POST['area_id']) && $_POST['area_id'] != '') {
    $area_id = intval($_POST['area_id']);
    $query = mysqli_query($con, "SELECT CategoryId, Category FROM tblcategory WHERE AreaId='$area_id' AND Is_Active=1");
    if (mysqli_num_rows($query) > 0) {
        echo '<option value="">Select Category</option>';
        while ($row = mysqli_fetch_array($query)) {
            echo '<option value="' . htmlentities($row['CategoryId']) . '">' . htmlentities($row['Category']) . '</option>';
        }
    } else {
        echo '<option value="">No Categories Found</option>';
    }
} else {
    echo '<option value="">Select Category</option>';
}
