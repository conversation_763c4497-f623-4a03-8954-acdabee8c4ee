<?php
session_start();
include('includes/config.php');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Ensure article_id is received from the form
    if (!isset($_POST['id']) || empty($_POST['id'])) {
        die("Error: Article ID is missing.");
    }

    $article_id = isset($_GET['id']) ? intval($_GET['id']) : 0; // Get article ID from URL

    // Retrieve and sanitize input values
    $article_id = intval($_POST['id']); // Convert to integer to prevent SQL injection
    $name = isset($_SESSION['name']) ? trim($_SESSION['name']) : 'Anonymous'; // Default to 'Anonymous' if session not set
    $description = trim($_POST['description']);

    // Sanitize input
    $name = $con->real_escape_string($name);
    $description = $con->real_escape_string($description);

    // Insert into the database
    $sql = "INSERT INTO tblcomments (article_id, name, description) 
            VALUES ('$article_id', '$name', '$description')";

    if ($con->query($sql) === TRUE) {
        echo "Your comment has been submitted successfully.";
    } else {
        echo "Error: " . $sql . "<br>" . $con->error;
    }
}

// Close the database connection
$con->close();
