<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <title>KnowledgeBase.dev - A free knowledge base template by HelpCenter.io - Search Page</title>

  <link rel="shortcut icon" href="dist/images/favicon.ico">

  <script src="https://kit.fontawesome.com/1b5fdf4bb2.js" crossorigin="anonymous"></script>
  <link href="../dist/css/main.css" rel="stylesheet">

  <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
  <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
  <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>

<body>

  <section class="jumbotron jumbotron-fluid text-center header-small">
    <div class="navbar navbar-dark navbar-static-top navbar-expand-lg" role="navbar">
      <a href="/" class="navbar-brand">
        KnowledgeBase.dev Help Center
      </a>
      <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarText"
        aria-controls="navbarText" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>

      <div class="collapse navbar-collapse" id="navbarText">
        <ul class="nav navbar-nav">
          <li class="nav-item header-link" style=padding-top:10px;>
            <a class="nav-link" href="#" target="_blank">Back to website</a>
          </li>

          <li class="nav-item">
            <div class="nav-link dropdown">
              <button class="btn btn-outline-light dropdown-toggle" type="button" id="dropdownMenuButton"
                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                English
              </button>
              <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                <a class="dropdown-item" href="#fr">French</a>
                <a class="dropdown-item" href="#bg">Bulgarian</a>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <div class="cover"></div>

    <div class="container">
      <div id="search-field">
        <div class="row justify-content-center text-center">
          <div class="col-md-10">
            <form id="searchForm" action="/" class="search-form" method="GET">
              <div class="input-group search">
                <input type="text" name="q" class="search-field" aria-label="Search field"
                  placeholder="Enter your question here to get started" value="" aria-required="true" required
                  autoComplete="off" />
                <button class="input-group-addon" aria-label="Search">
                  <i class="fa fa-search">&nbsp;</i>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </section>

  <div class="container">
    <div class="row content section paddingt-2 white-background">
      <div class="col-sm-12">
        <div class="row">
          <div class="col-md-12">
            <nav aria-label="breadcrumb" role="navigation">
              <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item active">Before you sign up</li>
              </ol>
            </nav>
          </div>
        </div>
        <div class="row justify-content-between">
          <div class="col-md-3 text-left d-none d-md-inline-block">
            <h2>Categories</h2>
            <ul class="nav categories-nav nav-pills flex-column" role="navigation">
              <li class="nav-item open">
                <a class="nav-link active" href="category.html">Get Started</a>

                <ul class="nav nav-pills flex-column">
                  <li class="nav-item">
                    <a class="nav-link" href="#">Get Started with One Thing</a>
                  </li>
                  <li class="nav-item">
                    <a class="nav-link" href="#">Get Started with Something Else</a>
                  </li>
                </ul>
              </li>

              <li class="nav-item">
                <a class="nav-link" href="#">Authentication</a>
              </li>
            </ul>
          </div>

          <div class="col-12 text-left d-block d-md-none">
            <h2 style="margin-bottom:30px;">
              <a href="#categories-nav" style="line-height:.5rem;" data-toggle="collapse" role="button"
                aria-expanded="false" aria-controls="categories-nav" class="btn btn-outline-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="30" height="30" focusable="false"
                  role="img">
                  <title>Categories</title>
                  <path stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10"
                    d="M4 7h22M4 15h22M4 23h22"></path>
                </svg>
              </a> Categories
            </h2>
            <div id="categories-nav" class="collapse">
              <ul class="nav categories-nav nav-pills flex-column" role="navigation">
                <li class="nav-item  open">
                  <a class="nav-link active" href="#">Get Started</a>
                  <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                      <a class="nav-link" href="#">Get Started with One Thing</a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="#">Get Started with Something Else</a>
                    </li>
                  </ul>
                </li>
                <li class="nav-item ">
                  <a class="nav-link" href="#">Authentication</a>
                </li>
              </ul>
            </div>
          </div>

          <div class="col-md-8 col-md-push-1">
            <h2 class="display-3 mb-5">Search Results</h2>
            <div class="list-group search-results">
              <a href="article.html" class="list-group-item list-group-item-action flex-column align-items-start">
                <div class="d-flex w-100 justify-content-between">
                  <h5 class="mb-1">Example article matching your search criteria</h5>
                  <small>Last Updated: 10 days ago</small>
                </div>
                <p class="mb-1">This is part of the content of the example article that matched what you search for.
                  Click on it to ...</p>
              </a>

              <a href="article.html" class="list-group-item list-group-item-action flex-column align-items-start">
                <div class="d-flex w-100 justify-content-between">
                  <h5 class="mb-1">Example article matching your search criteria</h5>
                  <small>Last Updated: 23 days ago</small>
                </div>
                <p class="mb-1">This is part of the content of the example article that matched what you search for.
                  Click on it to ...</p>
              </a>

              <a href="article.html" class="list-group-item list-group-item-action flex-column align-items-start">
                <div class="d-flex w-100 justify-content-between">
                  <h5 class="mb-1">Example article matching your search criteria</h5>
                  <small>Last Updated: 3 weeks ago</small>
                </div>
                <p class="mb-1">This is part of the content of the example article that matched what you search for.
                  Click on it to ...</p>
              </a>

              <a href="article.html" class="list-group-item list-group-item-action flex-column align-items-start">
                <div class="d-flex w-100 justify-content-between">
                  <h5 class="mb-1">Example article matching your search criteria</h5>
                  <small>Last Updated: 1 month ago</small>
                </div>
                <p class="mb-1">This is part of the content of the example article that matched what you search for.
                  Click on it to ...</p>
              </a>

              <a href="article.html" class="list-group-item list-group-item-action flex-column align-items-start">
                <div class="d-flex w-100 justify-content-between">
                  <h5 class="mb-1">Example article matching your search criteria</h5>
                  <small>Last Updated: 15 days ago</small>
                </div>
                <p class="mb-1">This is part of the content of the example article that matched what you search for.
                  Click on it to ...</p>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

 <footer>
      <div class="container">
        <div class="row">
          <div class="col-md-6">
            Celaeno HelpDesk 
            <p class="mt-1"><strong><small class="text-muted">Copyright © 2024 , All Right Reserved by <a
              href="#">Celaeno Technology</a></small></strong></p>
          </div>
          <div class="col-md-6 text-right">
            <p>
              <a href="#">Back to top</a>
            </p>
          </div>
        </div>
      </div>
    </footer>

  <script src="../dist/js/main.js"></script>
</body>

</html>