<?php
session_start();
include('includes/config.php');

// Enable error reporting for debugging (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (empty($_SESSION['login'])) {
    header('Location: index.php');
    exit();
}

// Array to define the class for each task status
$statuses = [
    "New" => "new",
    "In Progress" => "inprogress",
    "Waiting on Customer" => "waitingoncustomer",
    "Hold" => "hold",
    "Closed" => "closed",
    "Resolved" => "resolved",
    "Low" => "low",
    "High" => "high",
    "Medium" => "medium",
];

// Initialize session filters if not set
if (!isset($_SESSION['ticket_filters'])) {
    $_SESSION['ticket_filters'] = [
        'product' => '',
        'priority' => '',
        'status' => ''
    ];
}

// Update session filters based on POST data
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $_SESSION['ticket_filters']['product'] = isset($_POST['product']) && $_POST['product'] != '-1' ? $_POST['product'] : '';
    $_SESSION['ticket_filters']['priority'] = isset($_POST['priority']) && $_POST['priority'] != '-1' ? $_POST['priority'] : '';
    $_SESSION['ticket_filters']['status'] = isset($_POST['status']) && $_POST['status'] != '-1' ? $_POST['status'] : '';
}

// Get filter values from GET parameters (instead of session)
$product = isset($_GET['product']) && $_GET['product'] != '-1' ? $_GET['product'] : '';
$priority = isset($_GET['priority']) && $_GET['priority'] != '-1' ? $_GET['priority'] : '';
$status = isset($_GET['status']) && $_GET['status'] != '-1' ? $_GET['status'] : '';

// Build SQL query with filters
$sql = "SELECT * FROM tickets WHERE 1=1";
$params = [];

if (!empty($product)) {
    $sql .= " AND products = ?";
    $params[] = $product;
}

if (!empty($priority)) {
    $sql .= " AND priority = ?";
    $params[] = $priority;
}

if (!empty($status) && $status !== 'all') {
    $sql .= " AND status = ?";
    $params[] = $status;
}

// Prepare and execute the query
$stmt = $con->prepare($sql);
if (!empty($params)) {
    $types = str_repeat('s', count($params)); // All parameters are strings
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();

?>

<!DOCTYPE html>
<html>

<head>
    <title>Celaeno Technology | Manage Articles</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/knowledge-base.css" rel="stylesheet" type="text/css" />
    <style>
        .page-title-box .page-title {
            margin-bottom: 22px;
            float: left;
        }

        .ticket-table-outer {
            width: 100%;
            overflow-x: auto;
        }

        .ticket-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-family: Arial, sans-serif;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            overflow: auto;
        }

        .ticket-table th,
        .ticket-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .ticket-table th {
            background-color: #007474;
            color: white;
            font-weight: 600;
        }

        .ticket-table td {
            color: #333;
        }

        .ticket-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .ticket-table tr:hover {
            background-color: #f1f3f5;
            transition: background-color 0.2s ease;
        }

        .status-badge {
            width: fit-content;
            padding: 8px 12px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            font-size: 14px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 6px;
            white-space: nowrap;
        }

        .status-badge.urgent {
            color: #c12831;
            background-color: #f0d2d7;
        }

        .status-badge.high {
            color: #db4522;
            background-color: #f1d9cc;
        }

        .status-badge.medium {
            color: #b08106;
            background-color: #f5edce;
        }

        .status-badge.low {
            color: #39b126;
            background-color: #c7ebc7;
        }

        .status-badge img {
            width: 18px;
            height: 18px;
        }

        .status-badge.new {
            color: #398eb7;
            background-color: #d0e7f8;
        }

        .status-badge.inprogress {
            color: #e3751c;
            background-color: #f7e8da;
        }

        .status-badge.onhold {
            color: #d7a229;
            background-color: #fff2de;
        }

        .status-badge.canceled {
            color: #d82e2e;
            background-color: #f9c9c9;
        }

        .status-badge.completed {
            color: #007070;
            background-color: #c1e7e7;
        }

        .status-badge.waitingonclient {
            color: #4c4848;
            background-color: #d5d5d5;
        }

        .status-badge.done {
            color: #6e9f37;
            background-color: #e5f2d3;
        }

        .status-badge.testing {
            color: #9e6dcc;
            background-color: #ebddf4;
        }

        .status-badge.readytotest {
            color: #3b71cf;
            background-color: #e0ecfd;
        }

        .status-badge.readytodeploy {
            color: #38917e;
            background-color: #d3f1f1;
        }

        .status {
            width: 8rem;
            display: flex;
            justify-content: center;
        }

        .priority {
            width: 8rem;
            display: flex;
            justify-content: center;
        }



        /* Responsive design */
        @media (max-width: 600px) {
            .ticket-table {
                display: block;
                overflow-x: auto;
            }
        }
    </style>


</head>

<body class="fixed-left">
    <div id="wrapper">
        <?php include('includes/topheader.php'); ?>
        <?php include('includes/leftsidebar.php'); ?>

        <div class="content-page">
            <div class="content">
                <div class="container">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="page-title-box">
                                <ol class="breadcrumb p-0 m-0">
                                    <li class="active">
                                        Ticket
                                    </li>
                                </ol>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>



                    <div class="row">
                        <div class="col-sm-12">


                            <form method="get" class="filter-articles" id="filter-form">
                                <select class="form-control" name="product" id="area_product">
                                    <option value="-1">Select Product</option>
                                    <option value="celaeno_invoice" <?php echo $product === 'celaeno_invoice' ? 'selected' : ''; ?>>Invoice</option>
                                    <option value="celaeno_booking" <?php echo $product === 'celaeno_booking' ? 'selected' : ''; ?>>Booking</option>
                                    <option value="celaeno_timesheet" <?php echo $product === 'celaeno_timesheet' ? 'selected' : ''; ?>>Timesheet</option>
                                    <option value="celaeno_cms" <?php echo $product === 'celaeno_cms' ? 'selected' : ''; ?>>CMS</option>
                                    <option value="celaeno_product" <?php echo $product === 'celaeno_product' ? 'selected' : ''; ?>>Knowledge Base</option>
                                </select>
                                <select class="form-control" name="priority" id="priority">
                                    <option value="-1">Select Priority</option>
                                    <option value="low" <?php echo $priority === 'low' ? 'selected' : ''; ?>>Low</option>
                                    <option value="medium" <?php echo $priority === 'medium' ? 'selected' : ''; ?>>Medium
                                    </option>
                                    <option value="high" <?php echo $priority === 'high' ? 'selected' : ''; ?>>High
                                    </option>
                                    <option value="urgent" <?php echo $priority === 'urgent' ? 'selected' : ''; ?>>Urgent
                                    </option>
                                </select>
                                <select class="form-control" name="status" id="status">
                                    <option value="-1">Select Status</option>
                                    <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>All Tickets
                                    </option>
                                    <optgroup label="My tickets">
                                        <option value="New" <?php echo $status === 'New' ? 'selected' : ''; ?>>New
                                        </option>
                                        <option value="In Progress" <?php echo $status === 'In Progress' ? 'selected' : ''; ?>>In Progress</option>
                                        <option value="Waiting on Customer" <?php echo $status === 'Waiting on Customer' ? 'selected' : ''; ?>>Waiting on Customer</option>
                                        <option value="Hold" <?php echo $status === 'Hold' ? 'selected' : ''; ?>>Hold
                                        </option>
                                        <option value="Resolved" <?php echo $status === 'Resolved' ? 'selected' : ''; ?>>
                                            Resolved</option>
                                        <option value="Closed" <?php echo $status === 'Closed' ? 'selected' : ''; ?>>
                                            Closed</option>
                                    </optgroup>
                                </select>

                            </form>
                            <div style="width: fit-content; float: right;">
                                <a href="./ticket-create.php">
                                    <button type="button" id="addToTable"
                                        class="btn btn-success waves-effect waves-light"
                                        style="margin-bottom: 1rem; margin-left: auto; display: block;">
                                        Create Ticket <i class="mdi mdi-plus-circle-outline"></i>
                                    </button>
                                </a>
                            </div>
                        </div>
                    </div>


                    <div class="ticket-table-outer">

                        <table class="ticket-table">
                            <thead>
                                <tr>
                                    <th>Ticket ID</th>
                                    <th>Summary</th>
                                    <th>Reporter</th>
                                    <th>Type</th>
                                    <th>Product</th>
                                    <th>Priority</th>
                                    <th>Channel</th>
                                    <th>Status</th>
                                    <th>Assignee</th>
                                    <th>Created At</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if ($result->num_rows > 0) {
                                    while ($row = $result->fetch_assoc()) {
                                        $statusClass = isset($statuses[$row['status']]) ? $statuses[$row['status']] : "status-default";
                                        $priorityClass = isset($statuses[$row['priority']]) ? $statuses[$row['priority']] : "status-default";
                                        // Map database product values to display names
                                        $productDisplay = [
                                            'celaeno_invoice' => 'Invoice',
                                            'celaeno_booking' => 'Booking',
                                            'celaeno_timesheet' => 'Timesheet',
                                            'celaeno_cms' => 'CMS',
                                            'celaeno_product' => 'Knowledge Base'
                                        ];
                                        $productName = isset($productDisplay[$row['products']]) ? $productDisplay[$row['products']] : $row['products'];
                                        echo "<tr>";
                                        echo "<td><a href='ticket_view.php?ticket_id=" . $row['id'] . "'>" . $row['id'] . "</a></td>";
                                        echo "<td>" . substr(htmlspecialchars($row['description']), 0, 50) . "</td>";
                                        echo "<td>" . htmlspecialchars($row['first_name']) . "</td>";
                                        echo "<td>" . htmlspecialchars($row['request_type']) . "</td>";
                                        echo "<td>" . htmlspecialchars($productName) . "</td>";
                                        echo "<td><span class='status-badge $priorityClass'>" . htmlspecialchars($row['priority']) . "</span></td>";
                                        echo "<td>" . htmlspecialchars($row['channel']) . "</td>";
                                        echo "<td><span class='status-badge $statusClass'>" . htmlspecialchars($row['status']) . "</span></td>";
                                        echo "<td>" . "--" . "</td>";
                                        echo "<td>" . htmlspecialchars($row['created_at']) . "</td>";
                                        echo "</tr>";
                                    }
                                } else {
                                    echo "<tr><td colspan='10'>No tickets found</td></tr>";
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>

                </div>

            </div>
        </div>
    </div>
    </div>

    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/detect.js"></script>
    <script src="assets/js/fastclick.js"></script>
    <script src="admin/assets/js/jquery.blockUI.js"></script>

    <script src="admin/assets/js/jquery.slimscroll.js"></script>
    <script src="admin/assets/js/jquery.scrollTo.min.js"></script>
    <script src="admin/assets/js/jquery.core.js"></script>
    <script src="admin/assets/js/jquery.app.js"></script>


    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- <script>
        $(document).ready(function() {
            $(".btn-outline-secondary").click(function() {
                $(".cards-container").toggle(); // Toggles visibility
            });
        });
        document.addEventListener("DOMContentLoaded", function() {
            document.querySelector(".btn-outline-first").addEventListener("click", function() {
                document.querySelector(".cards-container").classList.toggle("list-view");
            });
        });
    </script> -->

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function () {
            // List view button
            $("#list-view-btn").click(function () {
                $("#view_mode").val("list"); // Update hidden input
                $(".cards-container").addClass("list-view"); // Apply list view
            });

            // Card view button
            $("#card-view-btn").click(function () {
                $("#view_mode").val("card"); // Update hidden input
                $(".cards-container").removeClass("list-view"); // Apply card view
            });

            // Apply the initial view based on the hidden input value
            if ($("#view_mode").val() === "list") {
                $(".cards-container").addClass("list-view");
            } else {
                $(".cards-container").removeClass("list-view");
            }
        });
    </script>

    <script>
        $(document).ready(function () {
            // Update URL with filter parameters on select change
            $('#area_product, #priority, #status').on('change', function () {
                let form = $('#filter-form');
                let url = form.attr('action') || window.location.pathname;
                let queryString = form.serialize(); // Serializes form data
                window.location.href = url + '?' + queryString; // Redirect to new URL
            });

            // Existing view mode scripts (unchanged, but note: view_mode not used in form)
            $("#list-view-btn").click(function () {
                $("#view_mode").val("list");
                $(".cards-container").addClass("list-view");
            });

            $("#card-view-btn").click(function () {
                $("#view_mode").val("card");
                $(".cards-container").removeClass("list-view");
            });

            if ($("#view_mode").val() === "list") {
                $(".cards-container").addClass("list-view");
            } else {
                $(".cards-container").removeClass("list-view");
            }
        });
    </script>



</body>

</html>