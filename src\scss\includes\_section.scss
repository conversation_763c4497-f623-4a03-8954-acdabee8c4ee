.section {
  padding-top: 3rem;
  padding-bottom: 3rem;
  background-color: #fff;

  &.white-background {
    background-color: #fff;
  }

  &.paddingt-2 {
    padding-top: 2rem;
  }

  h2 {
    margin-bottom: 1rem;
  }

  ul {
    padding-left: 20px;
    margin-top: 35px;
    font-weight: 300;
    list-style: none;

    li {
      margin: 10px 0;

      &.faq-item {
        &.is-open {
          span {
            display: block;
          }
        }
      }
    
      & > span {
        display: none;
        border-top: 1px solid #eee;
        margin-top: 10px;
        padding-top: 10px;
      }
      
      &:hover:before {
        color: #777;
      }
      
      a:hover {
        text-decoration: none;
      }
    }

    &.homepage-listing {
      li {
        &:before {
          content: '\f15c';
          font-family: "Font Awesome 5 Free";
          font-weight: 900;
          display: inline-block;
          margin-right: 15px;
          color: #cccccc;
        }

        &.faq-item {
          a.read-more {
            display: block;
            width: 100%;
            text-align: center;
            background-color: #eee;
            font-size: 1rem;
          }

          &:before {
            content: '\f059';
          }
        }
      }
    }
  }
}