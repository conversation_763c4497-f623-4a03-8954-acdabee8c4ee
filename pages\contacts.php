<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <title>KnowledgeBase.dev - A free knowledge base template by HelpCenter.io - Search Page</title>

  <link rel="shortcut icon" href="/dist/images/favicon.ico">

  <script src="https://kit.fontawesome.com/1b5fdf4bb2.js" crossorigin="anonymous"></script>
  <link href="../dist/css/main.css" rel="stylesheet">

  <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
  <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
  <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>

<body>

  <section class="jumbotron jumbotron-fluid text-center header-small">
    <div class="navbar navbar-dark navbar-static-top navbar-expand-lg" role="navbar">
      <a href="/" class="navbar-brand">
        KnowledgeBase.dev Help Center
      </a>
      <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarText"
        aria-controls="navbarText" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>

      <div class="collapse navbar-collapse" id="navbarText">
        <ul class="nav navbar-nav">
          <li class="nav-item header-link" style=padding-top:10px;>
            <a class="nav-link" href="#" target="_blank">Back to website</a>
          </li>

          <li class="nav-item">
            <div class="nav-link dropdown">
              <button class="btn btn-outline-light dropdown-toggle" type="button" id="dropdownMenuButton"
                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                English
              </button>
              <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                <a class="dropdown-item" href="#fr">French</a>
                <a class="dropdown-item" href="#bg">Bulgarian</a>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>

    <div class="cover"></div>

    <div class="container">
      <div id="search-field">
        <div class="row justify-content-center text-center">
          <div class="col-md-10">
            <form id="searchForm" action="/" class="search-form" method="GET">
              <div class="input-group search">
                <input type="text" name="q" class="search-field" aria-label="Search field"
                  placeholder="Enter your question here to get started" value="" aria-required="true" required
                  autoComplete="off" />
                <button class="input-group-addon" aria-label="Search">
                  <i class="fa fa-search">&nbsp;</i>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </section>

  <div class="container">
    <div class="row content section paddingt-2 white-background">
        <div class="col-12 text-center">
            <h1>Contacts</h1>
            <hr />
            <p class="lead">Coming soon.</p>
        </div>
    </div>
  </div>

 <footer>
      <div class="container">
        <div class="row">
          <div class="col-md-6">
            Celaeno HelpDesk 
            <p class="mt-1"><strong><small class="text-muted">Copyright © 2024 , All Right Reserved by <a
              href="#">Celaeno Technology</a></small></strong></p>
          </div>
          <div class="col-md-6 text-right">
            <p>
              <a href="#">Back to top</a>
            </p>
          </div>
        </div>
      </div>
    </footer>

</body>

</html>