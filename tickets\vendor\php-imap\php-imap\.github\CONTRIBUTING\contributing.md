# How to contribute

First of all, thanks for taking the time to contribute!

Every contribution, being it pull requests, bug reports or feature requests, will help to improve this library!

## Ways to contribute

* Found a bug? Want a feature? Or just having question? [Open an
issue!](https://github.com/barbushin/php-imap/issues/new/choose)
* Add a feature or fix a bug:
  * Check for existing issue or create a new one.
  * Fork the repo, make your changes.
  * Create a pull request, and reference the issue.
* Add examples, tests, or improve documentation.

## Test

When committing code, please make sure to test before creating a pull request.

We use PHPUnit for testing, feel free to add new tests. This is not a requirement, but helps us maintain code coverage.
