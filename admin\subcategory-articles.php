<?php
session_start();
include('includes/config.php');

// Enable error reporting for debugging (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (empty($_SESSION['login'])) {
    header('Location: index.php');
    exit();
}

// Get product ID, category, and subcategory from URL
$productId = isset($_GET['id']) ? mysqli_real_escape_string($con, $_GET['id']) : '';
$selectedCategory = isset($_GET['category']) ? mysqli_real_escape_string($con, $_GET['category']) : '';
$selectedSubcategory = isset($_GET['subcategory']) ? mysqli_real_escape_string($con, $_GET['subcategory']) : '';

// Fetch product name for display
$productQuery = "SELECT ProductName FROM tblarea WHERE id = '$productId' AND Is_Active = 1";
$productResult = mysqli_query($con, $productQuery);
$productName = '';
if ($productResult && mysqli_num_rows($productResult) > 0) {
    $productRow = mysqli_fetch_assoc($productResult);
    $productName = htmlentities($productRow['ProductName']);
} else {
    header('Location: products.php');
    exit();
}

// Fetch subcategories for the selected category
$subcategoryQuery = "SELECT s.SubCategoryId, s.SubCategory
                    FROM tblsubcategory s
                    WHERE s.Is_Active = 1 AND s.CategoryId = '$selectedCategory'
                    ORDER BY s.SubCategory";
$subcategoryResult = mysqli_query($con, $subcategoryQuery);

// Fetch articles for the selected subcategory
$articleQuery = "SELECT a.id, a.ArticleTitle, a.ArticleDetails, a.UpdationDate
                 FROM tblarticles a
                 WHERE a.Is_Active = 1 AND a.SubCategoryId = '$selectedSubcategory'
                 ORDER BY a.UpdationDate DESC";
$articleResult = mysqli_query($con, $articleQuery);
?>

<!DOCTYPE html>
<html>

<head>
    <title>Subcategory Articles - <?php echo $productName; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/knowledge-base.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .sidebar {
            max-width: 250px;
            background-color: #f8f9fa;
            border-right: 1px solid #e9ecef;
        }

        .sidebar .list-group-item {
            border: none;
            padding: 1rem;
            font-weight: 500;
            color: #343a40;
            transition: background-color 0.3s ease, color 0.3s ease;
            border-radius: 0.5rem;
        }

        .sidebar .list-group-item:hover,
        .sidebar .list-group-item.active {
            background-color: #007474;
            color: white;
        }

        .sidebar .list-group-item.active {
            box-shadow: 0 0.5rem 1rem rgba(0, 123, 255, 0.15);
        }

        .content {
            flex-grow: 1;
            padding: 2rem;
        }

        .page-title-box {
            margin-bottom: 2rem;
        }

        .page-title {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 1rem;
        }

        .article-section {
            margin-bottom: 2rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }

        .article-section h2 {
            font-size: 3rem;
            color: #25a8e0;
            margin-bottom: 0.5rem;
        }

        .article-section p.date-info {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .list-group {
            margin-top: 15px;
        }

        .page-button {
            float: right;
        }
    </style>
</head>

<body class="fixed-left">
    <div id="wrapper">
        <?php include('includes/topheader.php'); ?>
        <?php include('includes/leftsidebar.php'); ?>

        <div class="content-page">
            <div class="content">
                <div class="container">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="page-title-box">
                                <div class="page-button">
                                    <h4 class="page-title">
                                        <a href="product_details.php?id=<?php echo urlencode($productId); ?>" class="btn btn-sm btn-default waves-effect waves-light pull-right">
                                            <i class="fa fa-arrow-left"></i> Back to Categories
                                        </a>

                                    </h4>
                                </div>
                                <!-- <?php echo $productName; ?> -->
                                <ol class="breadcrumb p-0 m-0">
                                    <li class="breadcrumb-item"><a href="knowledge-base.php">Products</a></li>
                                    <li class="breadcrumb-item"><a href="product_details.php?id=<?php echo urlencode($productId); ?>"><?php echo $productName; ?></a></li>
                                    <li class="breadcrumb-item">
                                        <a href="knowledge-base.php?id=<?php echo urlencode($productId); ?>&category=<?php echo urlencode($selectedCategory); ?>">
                                            <?php
                                            $catNameQuery = "SELECT Category FROM tblcategory WHERE CategoryId = '$selectedCategory'";
                                            $catNameResult = mysqli_query($con, $catNameQuery);
                                            $catNameRow = mysqli_fetch_assoc($catNameResult);
                                            echo htmlentities($catNameRow['Category']);
                                            ?>
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active">
                                        <?php
                                        $subNameQuery = "SELECT SubCategory FROM tblsubcategory WHERE SubCategoryId = '$selectedSubcategory'";
                                        $subNameResult = mysqli_query($con, $subNameQuery);
                                        $subNameRow = mysqli_fetch_assoc($subNameResult);
                                        echo htmlentities($subNameRow['SubCategory']);
                                        ?>
                                    </li>
                                </ol>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <nav class="col-md-4 col-lg-3 sidebar">
                            <input type="text" class="form-control mb-3" placeholder="Filter Subcategories" id="subcategoryFilter">
                            <ul class="list-group">
                                <?php
                                if (mysqli_num_rows($subcategoryResult) > 0) {
                                    while ($subcategory = mysqli_fetch_array($subcategoryResult)) {
                                ?>
                                        <li class="list-group-item <?php echo ($selectedSubcategory == $subcategory['SubCategoryId']) ? 'active' : ''; ?>">
                                            <a href="?id=<?php echo urlencode($productId); ?>&subcategory=<?php echo urlencode($subcategory['SubCategoryId']); ?>&category=<?php echo urlencode($selectedCategory); ?>" style="text-decoration: none; color: inherit;">
                                                <?php echo htmlentities($subcategory['SubCategory']); ?>
                                            </a>
                                        </li>
                                <?php
                                    }
                                } else {
                                    echo "<li class=\"list-group-item\">No subcategories found</li>";
                                }
                                ?>
                            </ul>
                        </nav>

                        <main class="col-md-8 col-lg-9">
                            <?php
                            if (mysqli_num_rows($articleResult) > 0) {
                                while ($article = mysqli_fetch_array($articleResult)) {
                                    $timeAgo = !empty($article['UpdationDate']) ? (time() - strtotime($article['UpdationDate'])) : 0;
                                    $timeText = "Unknown date";
                                    if ($timeAgo > 0) {
                                        if ($timeAgo < 2592000) {
                                            $timeText = floor($timeAgo / 86400) . 'd ago';
                                        } elseif ($timeAgo > 31536000) {
                                            $timeText = floor($timeAgo / 31536000) . 'y ago';
                                        } else {
                                            $timeText = floor($timeAgo / 2592000) . 'mo ago';
                                        }
                                    }
                            ?>
                                    <section class="article-section">
                                        <h2>
                                            <a href="article_detail.php?article_id=<?php echo $article['id']; ?>" style="text-decoration: none;">
                                                <?php echo htmlentities($article['ArticleTitle']); ?>
                                            </a>
                                        </h2>
                                        <p class="date-info">Last updated <?php echo $timeText; ?></p>
                                    </section>
                            <?php
                                }
                            } else {
                                echo "<p>No articles found for this subcategory.</p>";
                            }
                            ?>
                        </main>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('subcategoryFilter').addEventListener('input', function(e) {
            let filter = e.target.value.toLowerCase();
            let items = document.querySelectorAll('.list-group-item');
            items.forEach(item => {
                let text = item.textContent.toLowerCase();
                item.style.display = text.includes(filter) ? '' : 'none';
            });
        });
    </script>
    <!-- jQuery  -->
    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/detect.js"></script>
    <script src="assets/js/fastclick.js"></script>
    <script src="assets/js/jquery.blockUI.js"></script>
    <script src="assets/js/waves.js"></script>
    <script src="assets/js/jquery.slimscroll.js"></script>
    <script src="assets/js/jquery.scrollTo.min.js"></script>

    <!-- App js -->
    <script src="assets/js/jquery.core.js"></script>
    <script src="assets/js/jquery.app.js"></script>
</body>

</html>