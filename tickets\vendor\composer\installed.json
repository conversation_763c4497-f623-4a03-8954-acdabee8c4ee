{"packages": [{"name": "php-imap/php-imap", "version": "5.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/barbushin/php-imap.git", "reference": "94107fdd1383285459a7f6c2dd2f39e25a1b8373"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/barbushin/php-imap/zipball/94107fdd1383285459a7f6c2dd2f39e25a1b8373", "reference": "94107fdd1383285459a7f6c2dd2f39e25a1b8373", "shasum": ""}, "require": {"ext-fileinfo": "*", "ext-iconv": "*", "ext-imap": "*", "ext-json": "*", "ext-mbstring": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.4", "maglnet/composer-require-checker": "^2.0|^3.2", "nikic/php-parser": "^4.3,<4.7|^4.10", "paragonie/hidden-string": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpunit/phpunit": "^8.5|^9.5", "povils/phpmnd": "^2.2", "psalm/plugin-phpunit": "^0.10.0|^0.15.1", "roave/security-advisories": "dev-master", "sebastian/phpcpd": "^4.1|^6.0"}, "suggest": {"ext-fileinfo": "To facilitate IncomingMailAttachment::getFileInfo() auto-detection"}, "time": "2022-12-05T15:47:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PhpImap\\": "src/PhpImap"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://linkedin.com/in/barbushin"}], "description": "Manage mailboxes, filter/get/delete emails in PHP (supports IMAP/POP3/NNTP)", "homepage": "https://github.com/barbushin/php-imap", "keywords": ["imap", "mail", "mailbox", "php", "pop3", "receive emails"], "support": {"issues": "https://github.com/barbushin/php-imap/issues", "source": "https://github.com/barbushin/php-imap/tree/5.0.1"}, "install-path": "../php-imap/php-imap"}], "dev": true, "dev-package-names": []}