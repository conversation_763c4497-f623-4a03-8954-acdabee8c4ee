<?php
session_start();
include('includes/config.php');
error_reporting(0);
if(strlen($_SESSION['login'])==0)
  { 
header('location:index.php');
}
else{
if($_GET['action']=='del' && $_GET['e_id'])
{
	$id=intval($_GET['e_id']);
	$query=mysqli_query($con,"UPDATE tblemployees SET status=0 WHERE employee_id='$id'");
	$msg="Employee removed ";
}
// Code for restore
if($_GET['resid'])
{
	$id=intval($_GET['resid']);
	$query=mysqli_query($con,"UPDATE tblemployees SET status=1 WHERE employee_id='$id'");
	$msg="Employee restored successfully";
}

// Code for Forever deletionparmdel
if($_GET['action']=='perdel' && $_GET['e_id'])
{
	$id=intval($_GET['e_id']);
	$query=mysqli_query($con,"DELETE FROM tblemployees WHERE employee_id='$id'");
	$delmsg="Employee deleted forever";
}

?>
<!DOCTYPE html>
<html lang="en">

<head>

    <title> Manage Resources</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../plugins/switchery/switchery.min.css">
    <script src="assets/js/modernizr.min.js"></script>

</head>


<body class="fixed-left">

    <!-- Begin page -->
    <div id="wrapper">

        <!-- Top Bar Start -->
        <?php include('includes/topheader.php');?>

        <!-- ========== Left Sidebar Start ========== -->
        <?php include('includes/leftsidebar.php');?>
        <!-- Left Sidebar End -->



        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->
        <div class="content-page">
            <!-- Start content -->
            <div class="content">
                <div class="container">

                    <div class="row">
                        <div class="col-xs-12">
                            <div class="page-title-box">
                                <h4 class="page-title">Manage Resources</h4>
                                <ol class="breadcrumb p-0 m-0">
                                    <li>
                                        <a href="#">Admin</a>
                                    </li>
                                    <li>
                                        <a href="#">Resources </a>
                                    </li>
                                    <li class="active">
                                        Manage Resources
                                    </li>
                                </ol>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>
                    <!-- end row -->


                    <div class="row">
                        <div class="col-sm-6">

                            <?php if($msg){ ?>
                            <div class="alert alert-success" role="alert">
                                <strong>Well done!</strong> <?php echo htmlentities($msg);?>
                            </div>
                            <?php } ?>

                            <?php if($delmsg){ ?>
                            <div class="alert alert-danger" role="alert">
                                <strong>Oh snap!</strong> <?php echo htmlentities($delmsg);?>
                            </div>
                            <?php } ?>


                        </div>


                        <div class="row">
                            <div class="col-md-12">
                                <div class="demo-box m-t-20">
                                    <div class="m-b-30">
                                        <a href="add-employee.php">
                                            <button id="addToTable" class="btn btn-success waves-effect waves-light">Add
                                                <i class="mdi mdi-plus-circle-outline"></i></button>
                                        </a>
                                    </div>

                                    <div class="table-responsive">
                                        <table class="table m-0 table-colored-bordered table-bordered-primary">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th>Name</th>
                                                    <th>Email</th>
                                                    <th>Position</th>
                                                    <th>Manager</th>
                                                    <th>Status</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php 
                                                    $query=mysqli_query($con,"SELECT
                                                                                `employee_id`,
                                                                                `employee_name`,
                                                                                `company_id`,
                                                                                `employee_email`,
                                                                                `password`,
                                                                                `status`,
                                                                                `manager_id`,
                                                                                `role_id`,
                                                                                `position`,
                                                                                `bill_rate`,
                                                                                `cost_rate`,
                                                                                `created_by`,
                                                                                `created_date`,
                                                                                `update_by`,
                                                                                `update_date`
                                                                                FROM
                                                                                `tblemployees`
                                                                                WHERE
                                                                                `status`=1");
$cnt=1;
$rowcount=mysqli_num_rows($query);
if($rowcount==0)
{
?>
                                                <tr>

                                                    <td colspan="7" align="center">
                                                        <h3 style="color:rgba(255, 0, 0, 0.4)">No record found</h3>
                                                    </td>
                                                <tr>
                                                    <?php 
} else {

while($row=mysqli_fetch_array($query))
{
?>


                                                <tr>
                                                    <th scope="row"><?php echo htmlentities($cnt);?></th>
                                                    <td><?php echo htmlentities($row['employee_name']);?></td>
                                                    <td><?php echo htmlentities($row['employee_email']);?></td>
                                                    <td><?php echo htmlentities($row['position']);?></td>
                                                    <td><?php echo htmlentities($row['manager_id']);?></td>
                                                    <td><?php echo htmlentities($row['status']);?></td>
                                                    <td><a
                                                            href="edit-employee.php?e_id=<?php echo htmlentities($row['employee_id']);?>"><i
                                                                class="fa fa-pencil" style="color: #29b6f6;"></i></a>
                                                        &nbsp;<a
                                                            href="manage-resources.php?e_id=<?php echo htmlentities($row['employee_id']);?>&&action=del">
                                                            <i class="fa fa-trash-o" style="color: #f05050"></i></a>
                                                    </td>
                                                </tr>
                                                <?php
$cnt++;
 }} ?>
                                            </tbody>

                                        </table>
                                    </div>




                                </div>

                            </div>


                        </div>
                        <!--- end row -->



                        <div class="row">
                            <div class="col-md-12">
                                <div class="demo-box m-t-20">
                                    <div class="m-b-30">

                                        <h4><i class="fa fa-trash-o"></i> Deleted Resources</h4>

                                    </div>

                                    <div class="table-responsive">
                                        <table class="table m-0 table-colored-bordered table-bordered-danger">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th>Name</th>
                                                    <th>Email</th>
                                                    <th>Position</th>
                                                    <th>Manager</th>
                                                    <th>Status</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php 
                                                    $query=mysqli_query($con,"SELECT
                                                                                `employee_id`,
                                                                                `employee_name`,
                                                                                `company_id`,
                                                                                `employee_email`,
                                                                                `password`,
                                                                                `status`,
                                                                                `manager_id`,
                                                                                `role_id`,
                                                                                `position`,
                                                                                `bill_rate`,
                                                                                `cost_rate`,
                                                                                `created_by`,
                                                                                `created_date`,
                                                                                `update_by`,
                                                                                `update_date`
                                                                                FROM
                                                                                `tblemployees`
                                                                                WHERE
                                                                                    `status`=0");
$cnt=1;
$rowcount=mysqli_num_rows($query);
if($rowcount==0)
{
?>
                                                <tr>

                                                    <td colspan="7" align="center">
                                                        <h3 style="color:rgba(255, 0, 0, 0.4)">No record found</h3>
                                                    </td>
                                                <tr>
                                                    <?php 
} else {

while($row=mysqli_fetch_array($query))
{
?>

                                                <tr>
                                                    <th scope="row"><?php echo htmlentities($cnt);?></th>
                                                    <td><?php echo htmlentities($row['employee_name']);?></td>
                                                    <td><?php echo htmlentities($row['employee_email']);?></td>
                                                    <td><?php echo htmlentities($row['position']);?></td>
                                                    <td><?php echo htmlentities($row['manager_id']);?></td>
                                                    <td><?php echo htmlentities($row['status']);?></td>
                                                    <td><a
                                                            href="manage-resources.php?resid=<?php echo htmlentities($row['employee_id']);?>"><i
                                                                class="ion-arrow-return-right"
                                                                title="Restore This Employee"></i></a>
                                                        &nbsp;<a
                                                            href="manage-resources.php?e_id=<?php echo htmlentities($row['employee_id']);?>&&action=perdel">
                                                            <i class="fa fa-trash-o" style="color: #f05050"></i></a>
                                                    </td>
                                                </tr>
                                                <?php
$cnt++;
 } }?>
                                            </tbody>

                                        </table>
                                    </div>




                                </div>

                            </div>


                        </div>


                    </div> <!-- container -->

                </div> <!-- content -->
                <?php include('includes/footer.php');?>
            </div>

        </div>
        <!-- END wrapper -->



        <script>
        var resizefunc = [];
        </script>

        <!-- jQuery  -->
        <script src="assets/js/jquery.min.js"></script>
        <script src="assets/js/bootstrap.min.js"></script>
        <script src="assets/js/detect.js"></script>
        <script src="assets/js/fastclick.js"></script>
        <script src="assets/js/jquery.blockUI.js"></script>
        <script src="assets/js/waves.js"></script>
        <script src="assets/js/jquery.slimscroll.js"></script>
        <script src="assets/js/jquery.scrollTo.min.js"></script>
        <script src="../plugins/switchery/switchery.min.js"></script>

        <!-- App js -->
        <script src="assets/js/jquery.core.js"></script>
        <script src="assets/js/jquery.app.js"></script>

</body>

</html>
<?php } ?>