<?php

  // Check if the server is localhost
  $isLocalhost = false;

  // Check various ways to detect localhost
  if (isset($_SERVER['SERVER_NAME'])) {
      $isLocalhost = ($_SERVER['SERVER_NAME'] == 'localhost' ||
                     $_SERVER['SERVER_NAME'] == '127.0.0.1' ||
                     $_SERVER['SERVER_NAME'] == '::1');
  }

  // Also check HTTP_HOST as fallback
  if (!$isLocalhost && isset($_SERVER['HTTP_HOST'])) {
      $isLocalhost = ($_SERVER['HTTP_HOST'] == 'localhost' ||
                     strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0 ||
                     $_SERVER['HTTP_HOST'] == '127.0.0.1' ||
                     strpos($_SERVER['HTTP_HOST'], '127.0.0.1:') === 0);
  }

  // For command line or if no server variables, assume localhost
  if (!isset($_SERVER['SERVER_NAME']) && !isset($_SERVER['HTTP_HOST'])) {
      $isLocalhost = true;
  }

  if ($isLocalhost) {
    // Localhost credentials
    $servername = "localhost";
    $username = "root"; // Local DB username
    $password = "";     // Local DB password
    $dbname = "helpdesk"; // Local database name
    } else {
        $servername = "localhost";
        $username = "u603122711_helpdesk_live"; // Update with your DB username
        $password = "/i3LP9^8@Cv";     // Update with your DB password
        $dbname = "u603122711_helpdesk_live";
    }

// Establish database connection
$con = mysqli_connect($servername, $username, $password, $dbname);

// Check connection
if (!$con) {
    die("Failed to connect to MySQL: " . mysqli_connect_error());
}
?>