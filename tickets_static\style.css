* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    display: flex;
    height: 100vh;
    overflow: hidden;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    transition: background 0.3s ease;
}

body.dark {
    background: linear-gradient(135deg, #1e2a44 0%, #2c3e50 100%);
    color: #e0e0e0;
}

.container {
    display: flex;
    width: 100%;
}

/* Theme Toggle */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50px;
    padding: 10px;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: background 0.3s ease;
}

body.dark .theme-toggle {
    background: rgba(0, 0, 0, 0.2);
}

.theme-toggle .moon-icon {
    display: none;
}

body.dark .theme-toggle .sun-icon {
    display: none;
}

body.dark .theme-toggle .moon-icon {
    display: inline;
}

/* External Hamburger Menu */
.external-toggle-btn {
    position: fixed;
    top: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50px;
    padding: 10px;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: background 0.3s ease, transform 0.3s ease;
    display: none;
}

body.dark .external-toggle-btn {
    background: rgba(0, 0, 0, 0.2);
}

.external-toggle-btn:hover {
    transform: rotate(90deg);
}

.external-toggle-btn .menu-icon {
    font-size: 24px;
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    transition: width 0.3s ease, transform 0.3s ease;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
}

body.dark .sidebar {
    background: rgba(30, 42, 68, 0.9);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar.collapsed {
    width: 0;
    padding: 0;
    overflow: hidden;
}

.sidebar.collapsed .sidebar-header,
.sidebar.collapsed .ticket-list {
    display: none;
}

.sidebar-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.toggle-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    transition: transform 0.3s ease;
}

.toggle-btn:hover {
    transform: rotate(90deg);
}

.sidebar-title {
    flex: 1;
    font-size: 18px;
    font-weight: 500;
    margin-left: 10px;
}

.ticket-list {
    list-style: none;
}

.ticket-item {
    padding: 15px;
    margin: 10px 0;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s ease, background 0.3s ease;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

body.dark .ticket-item {
    background: rgba(255, 255, 255, 0.1);
}

.ticket-item:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.8);
}

body.dark .ticket-item:hover {
    background: rgba(255, 255, 255, 0.2);
}

.ticket-item.active {
    background: linear-gradient(90deg, #007bff, #00c4ff);
    color: #fff;
}

.ticket-id {
    font-weight: 500;
    font-size: 16px;
}

.ticket-title {
    font-size: 14px;
    opacity: 0.9;
}

.ticket-meta {
    font-size: 12px;
    opacity: 0.7;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    padding: 5rem 30px;
    overflow-y: auto;
    transition: margin-left 0.3s ease;
}

.ticket-details {
    display: flex;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.ticket-content {
    flex: 2;
    background: rgba(255, 255, 255, 0.9);
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

body.dark .ticket-content {
    background: rgba(30, 42, 68, 0.9);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.ticket-content h2 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 20px;
    background: linear-gradient(90deg, #007bff, #00c4ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.tabs {
    display: flex;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

body.dark .tabs {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab {
    padding: 10px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: color 0.3s ease;
}

.tab:hover {
    color: #007bff;
}

.tab.active {
    color: #007bff;
    border-bottom: 3px solid #007bff;
    font-weight: 600;
}

.conversation-content {
    min-height: 200px;
    color: #666;
}

body.dark .conversation-content {
    color: #b0b0b0;
}

/* Ticket Metadata (Right Section) */
.ticket-meta {
    flex: 1;
    max-width: 300px;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

body.dark .ticket-meta {
    background: rgba(30, 42, 68, 0.9);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.info-card p {
    margin: 10px 0;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
}

.info-card p strong {
    font-weight: 500;
    color: #333;
}

body.dark .info-card p strong {
    color: #e0e0e0;
}

.status {
    background: #ffcc80;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 220px;
    }

    .sidebar.collapsed {
        width: 0;
        padding: 0;
    }

    .main-content {
        padding: 20px;
    }

    .ticket-details {
        flex-direction: column;
        gap: 20px;
    }

    .ticket-content,
    .ticket-meta {
        max-width: 100%;
    }
}