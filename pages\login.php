<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Redirect if already logged in
if (isset($_SESSION['username'])) {
    header("Location: index.php"); // Redirect to a dashboard
    exit;
}

// Initialize error message
$error = '';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Database connection
    $config_file = './dist/include/config.php';
    if (!file_exists($config_file)) {
        $error = "Configuration file not found.";
    } else {
        include($config_file);

        // Check if $con is set and valid
        if (!isset($con) || !$con) {
            $error = "Database connection failed.";
        } else {
            // Sanitize inputs
            $username = trim($_POST['username']);
            $password = $_POST['password'];

            // Basic input validation
            if (empty($username) || empty($password)) {
                $error = "Please fill in all fields.";
            } else {
                try {
                    // Prepare and execute query
                    $sql = "SELECT * FROM tblusers WHERE username = ?";
                    $stmt = $con->prepare($sql);
                    if (!$stmt) {
                        $error = "Query preparation failed: " . $con->error;
                    } else {
                        $stmt->bind_param("s", $username);
                        $stmt->execute();
                        $result = $stmt->get_result();

                        if ($result->num_rows === 1) {
                            $user = $result->fetch_assoc();

                            // Verify password
                            if (password_verify($password, $user['password'])) {
                                session_regenerate_id(true);
                                $_SESSION['username'] = $username;
                                $_SESSION['user_id'] = $user['id'];

                                header("Location: index.php");
                                exit;
                            } else {
                                $error = "Incorrect username or password.";
                            }
                        } else {
                            $error = "Incorrect username or password.";
                        }

                        $stmt->close();
                    }
                    $con->close();
                } catch (Exception $e) {
                    $error = "An error occurred: " . $e->getMessage();
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Secure Access</title>
</head>

<body>
    <h2>Login to Your Account</h2>

    <?php if (!empty($error)): ?>
        <p style="color: red;"><?php echo htmlspecialchars($error); ?></p>
    <?php endif; ?>

    <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
        <div>
            <label for="username">Username:</label><br>
            <input type="text" id="username" name="username" value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>" required>
        </div>
        <br>
        <div>
            <label for="password">Password:</label><br>
            <input type="password" id="password" name="password" required>
        </div>
        <br>
        <div>
            <button type="submit">Sign In</button>
        </div>
    </form>
</body>

</html>