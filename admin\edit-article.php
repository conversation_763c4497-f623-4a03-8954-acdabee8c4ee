<?php
session_start();
include('includes/config.php');
$msg = "";
$error = "";

if (strlen($_SESSION['login']) == 0) {
    header('location:index.php');
    exit;
}

// Handle AJAX requests for categories and subcategories
if (isset($_GET['action']) && ($_GET['action'] === 'get_categories' || $_GET['action'] === 'get_subcategories')) {
    if ($_GET['action'] === 'get_categories') {
        $productId = isset($_GET['product_id']) ? intval($_GET['product_id']) : 0;
        $query = mysqli_query($con, "SELECT CategoryId, Category FROM tblcategory WHERE AreaId = '$productId' AND Is_Active = 1");
        $options = '';
        while ($row = mysqli_fetch_array($query)) {
            $options .= '<option value="' . htmlentities($row['CategoryId']) . '">' . htmlentities($row['Category']) . '</option>';
        }
        echo $options;
    } elseif ($_GET['action'] === 'get_subcategories') {
        $categoryId = isset($_GET['category_id']) ? intval($_GET['category_id']) : 0;
        $query = mysqli_query($con, "SELECT SubCategoryId, SubCategory FROM tblsubcategory WHERE CategoryId = '$categoryId' AND Is_Active = 1");
        $options = '';
        while ($row = mysqli_fetch_array($query)) {
            $options .= '<option value="' . htmlentities($row['SubCategoryId']) . '">' . htmlentities($row['SubCategory']) . '</option>';
        }
        echo $options;
    }
    mysqli_close($con);
    exit;
}

$articleId = intval($_GET['pid']); // Get article ID

// Fetch article details
$query = mysqli_query($con, "SELECT
    tblarticles.id AS postid,
    tblarticles.ArticleImage,
    tblarticles.ArticleTitle AS title,
    tblarticles.ArticleDetails,
    tblarea.ProductName AS category_name,
    tblarea.id AS area_id,
    tblcategory.CategoryId AS subcatid,
    tblcategory.Category AS subcategory_name,
    tblsubcategory.SubCategoryId AS subcategory_id,
    tblsubcategory.SubCategory AS subcategory_name_actual
    FROM
    tblarticles
    LEFT JOIN tblarea ON tblarea.id = tblarticles.AreaId
    LEFT JOIN tblcategory ON tblcategory.CategoryId = tblarticles.CategoryId
    LEFT JOIN tblsubcategory ON tblsubcategory.SubCategoryId = tblarticles.SubCategoryId
    WHERE
    tblarticles.id = '$articleId' AND tblarticles.Is_Active = 1");

$row = mysqli_fetch_array($query); // Fetch the article data

if (isset($_POST['update'])) {
    $updatedArticleTitle = $_POST['articletitle'];
    $updatedAreaId = $_POST['area'];
    $updatedCategoryId = $_POST['category'];
    $updatedSubCategoryId = $_POST['subcategory'];
    $updatedArticleDetails = $con->real_escape_string($_POST['articledescription']);
    $arr = explode(" ", $updatedArticleTitle);
    $url = implode("-", $arr);
    $status = 1;

    // Store old data in history table
    $historyQuery = "INSERT INTO `tblarticleshistory` (`id`,`updateby`, `ArticleTitle`, `AreaId`, `CategoryId`, `SubCategoryId`, `ArticleDetails`, `PostingDate`, `UpdationDate`, `Is_Active`, `ArticleUrl`, `ArticleImage`) 
                   SELECT `id`,'" . $_SESSION["login"] . "',`ArticleTitle`, `AreaId`, `CategoryId`, `SubCategoryId`, `ArticleDetails`, `PostingDate`, `UpdationDate`, `Is_Active`, `ArticleUrl`, `ArticleImage` 
                   FROM `tblarticles` WHERE `id`='$articleId'";
    mysqli_query($con, $historyQuery); // Execute history query

    // Update the article
    $updateQuery = mysqli_query($con, "UPDATE tblarticles SET ArticleTitle='$updatedArticleTitle', AreaId='$updatedAreaId', CategoryId='$updatedCategoryId', SubCategoryId='$updatedSubCategoryId', ArticleDetails='$updatedArticleDetails', ArticleUrl='$url', Is_Active='$status' WHERE id='$articleId'");

    if ($updateQuery) {
        $msg = "Article updated successfully!";
        // Refresh the page to show updated data
        header("Location: edit-article.php?pid=$articleId");
        exit;
    } else {
        $error = "Something went wrong. Please try again.";
    }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="A fully featured admin theme which can be used to build CRM, CMS, etc.">
    <meta name="author" content="Celaeno">
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <link rel="shortcut icon" href="assets/images/favicon.ico">
    <title>Celaeno Technology | Edit Article</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <script src="assets/js/modernizr.min.js"></script>
</head>

<body class="fixed-left">
    <div id="wrapper">
        <?php include('includes/topheader.php'); ?>
        <?php include('includes/leftsidebar.php'); ?>
        <div class="content-page">
            <div class="content">
                <div class="container">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="page-title-box">
                                <h4 class="page-title">Edit Article</h4>
                                <a href="knowledge-base.php" class="btn btn-sm btn-default waves-effect waves-light pull-right">
                                    <i class="fa fa-arrow-left"></i> Back to Products
                                </a>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <?php if ($msg) { ?>
                                <div class="alert alert-success" role="alert">
                                    <strong>Well done!</strong> <?php echo htmlentities($msg); ?>
                                </div>
                            <?php } ?>
                            <?php if ($error) { ?>
                                <div class="alert alert-danger" role="alert">
                                    <strong>Oh snap!</strong> <?php echo htmlentities($error); ?>
                                </div>
                            <?php } ?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-10 col-md-offset-1">
                            <div class="p-6">
                                <div class="">
                                    <form name="editpost" method="post" id="postFormUpdate">
                                        <div class="form-group m-b-20">
                                            <label for="articletitle">Article Title</label>
                                            <input type="text" class="form-control" id="articletitle" value="<?php echo htmlentities($row['title']); ?>" name="articletitle" placeholder="Enter title" required>
                                        </div>

                                        <div class="form-group m-b-20">
                                            <label for="area">Product Name</label>
                                            <select class="form-control" name="area" id="area" onchange="loadCategories()" required>
                                                <option value="<?php echo htmlentities($row['area_id']); ?>">
                                                    <?php echo htmlentities($row['category_name']); ?>
                                                </option>
                                                <?php
                                                $ret = mysqli_query($con, "SELECT id, ProductName FROM tblarea WHERE Is_Active=1 AND id != " . intval($row['area_id']));
                                                while ($result = mysqli_fetch_array($ret)) {
                                                ?>
                                                    <option value="<?php echo htmlentities($result['id']); ?>">
                                                        <?php echo htmlentities($result['ProductName']); ?>
                                                    </option>
                                                <?php } ?>
                                            </select>
                                        </div>

                                        <div class="form-group m-b-20">
                                            <label for="category">Category</label>
                                            <select class="form-control" name="category" id="category" onchange="loadSubCategories()" required>
                                                <option value="<?php echo htmlentities($row['subcatid']); ?>">
                                                    <?php echo htmlentities($row['subcategory_name']); ?>
                                                </option>
                                            </select>
                                        </div>

                                        <div class="form-group m-b-20">
                                            <label for="subcategory">Subcategory</label>
                                            <select class="form-control" name="subcategory" id="subcategory">
                                                <option value="<?php echo htmlentities($row['subcategory_id']); ?>">
                                                    <?php echo htmlentities($row['subcategory_name_actual']); ?>
                                                </option>
                                            </select>
                                        </div>

                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="card-box">
                                                    <h4 class="m-b-30 m-t-0 header-title"><b>Article Details</b></h4>
                                                    <div id="editor"><?php echo $row['ArticleDetails']; ?></div>
                                                    <input type="hidden" name="articledescription" id="articledescription">
                                                </div>
                                            </div>
                                        </div>

                                        <button type="submit" name="update" class="btn btn-success waves-effect waves-light" onclick="savePost()">Update</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php include('includes/footer.php'); ?>
        </div>
    </div>

    <script>
        function savePost() {
            var postDescriptionHTML = document.querySelector('#editor .ql-editor').innerHTML;
            document.getElementById("articledescription").value = postDescriptionHTML;
            document.getElementById("postFormUpdate").submit();
        }

        function loadCategories() {
            var productId = document.getElementById('area').value;
            var categorySelect = document.getElementById('category');
            var subcategorySelect = document.getElementById('subcategory');

            // Clear existing options and show loading
            categorySelect.innerHTML = '<option value="">Select Category</option>';
            subcategorySelect.innerHTML = '<option value="">Select Subcategory</option>';

            if (productId) {
                var xhr = new XMLHttpRequest();
                xhr.open('GET', 'edit-article.php?action=get_categories&product_id=' + encodeURIComponent(productId), true);
                xhr.onreadystatechange = function() {
                    if (xhr.readyState == 4 && xhr.status == 200) {
                        categorySelect.innerHTML = '<option value="">Select Category</option>' + xhr.responseText;
                        // Load the current article's category if it matches the product
                        <?php if ($row['subcatid']) { ?>
                            var currentCategoryId = '<?php echo $row['subcatid']; ?>';
                            var options = categorySelect.options;
                            for (var i = 0; i < options.length; i++) {
                                if (options[i].value == currentCategoryId) {
                                    categorySelect.value = currentCategoryId;
                                    break;
                                }
                            }
                        <?php } ?>
                        loadSubCategories();
                    }
                };
                xhr.send();
            }
        }

        function loadSubCategories() {
            var categoryId = document.getElementById('category').value;
            var subcategorySelect = document.getElementById('subcategory');

            // Clear existing options and show loading
            subcategorySelect.innerHTML = '<option value="">Select Subcategory</option>';

            if (categoryId) {
                var xhr = new XMLHttpRequest();
                xhr.open('GET', 'edit-article.php?action=get_subcategories&category_id=' + encodeURIComponent(categoryId), true);
                xhr.onreadystatechange = function() {
                    if (xhr.readyState == 4 && xhr.status == 200) {
                        subcategorySelect.innerHTML = '<option value="">Select Subcategory</option>' + xhr.responseText;
                        // Load the current article's subcategory if it matches the category
                        <?php if ($row['subcategory_id']) { ?>
                            var currentSubcategoryId = '<?php echo $row['subcategory_id']; ?>';
                            var options = subcategorySelect.options;
                            for (var i = 0; i < options.length; i++) {
                                if (options[i].value == currentSubcategoryId) {
                                    subcategorySelect.value = currentSubcategoryId;
                                    break;
                                }
                            }
                        <?php } ?>
                    }
                };
                xhr.send();
            }
        }

        // Load categories and subcategories on page load to ensure correct initial state
        window.onload = function() {
            loadCategories();
        };
    </script>

    <script>
        var resizefunc = [];
    </script>

    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/detect.js"></script>
    <script src="assets/js/fastclick.js"></script>
    <script src="assets/js/jquery.blockUI.js"></script>
    <script src="assets/js/waves.js"></script>
    <script src="assets/js/jquery.slimscroll.js"></script>
    <script src="assets/js/jquery.scrollTo.min.js"></script>
    <script src="../plugins/switchery/switchery.min.js"></script>
    <script src="../plugins/summernote/summernote.min.js"></script>
    <script src="../plugins/select2/js/select2.min.js"></script>
    <script src="../plugins/jquery.filer/js/jquery.filer.min.js"></script>
    <script src="assets/pages/jquery.blog-add.init.js"></script>
    <script src="assets/js/jquery.core.js"></script>
    <script src="assets/js/jquery.app.js"></script>
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <script>
        var quill = new Quill('#editor', {
            theme: 'snow',
            modules: {
                toolbar: [
                    ['bold', 'italic', 'underline', 'strike'],
                    [{
                        'header': 1
                    }, {
                        'header': 2
                    }],
                    [{
                        'list': 'ordered'
                    }, {
                        'list': 'bullet'
                    }],
                    ['link', 'image', 'video'],
                    [{
                        'color': []
                    }, {
                        'background': []
                    }],
                    [{
                        'indent': '-1'
                    }, {
                        'indent': '+1'
                    }],
                    [{
                        'script': 'sub'
                    }, {
                        'script': 'super'
                    }],
                    ['clean']
                ]
            }
        });
    </script>
    <script>
        jQuery(document).ready(function() {
            $('.summernote').summernote({
                height: 240,
                minHeight: null,
                maxHeight: null,
                focus: false
            });
            $(".select2").select2();
            $(".select2-limiting").select2({
                maximumSelectionLength: 2
            });
        });
    </script>
</body>

</html>
<?php
mysqli_close($con);
?>