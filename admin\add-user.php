<?php
include "./includes/config.php";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Sanitize and retrieve form data
    $name = mysqli_real_escape_string($con, $_POST["name"]);
    $role = mysqli_real_escape_string($con, $_POST["role"]);
    $title = mysqli_real_escape_string($con, $_POST["title"]);
    $department = mysqli_real_escape_string($con, $_POST["department"]);
    $email = mysqli_real_escape_string($con, $_POST["email"]);
    $isActive = isset($_POST["isActive"]) ? 1 : 0; // Convert checkbox value
    $businessPhone = mysqli_real_escape_string($con, $_POST["business_phone"]);
    $mobilePhone = mysqli_real_escape_string($con, $_POST["mobile_phone"]);
    $password = mysqli_real_escape_string($con, $_POST["password"]);
    $isPasswordGenerated = mysqli_real_escape_string($con, $_POST['isPasswordGenerated']);

    // Hash the password (important for security)
    $hashedPassword = isset($password) ? password_hash($password, PASSWORD_DEFAULT) : "";

    // SQL query to insert data
    $sql = "INSERT INTO tblusers (name, title, role, department, email, isActive, business_phone, mobile_phone, password, isPasswordGenerated)
            VALUES ('$name', '$title', '$role', '$department', '$email', $isActive, '$businessPhone', '$mobilePhone', '$hashedPassword', '$isPasswordGenerated')";

    if ($con->query($sql) === TRUE) {
        // User added successfully, send email notification

        require_once './EmailHelper.php'; // Include your EmailHelper class file. Adjust the path if needed

        // Send the account creation email
        if (EmailHelper::sendAccountCreationEmail($email, $password)) { // security risk, see previous answers.
            echo "<script>alert('User added successfully and email sent.')</script>";
        } else {
            echo "<script>alert('User added successfully, but email failed to send.')</script>";
        }
    } else {
        echo "Error: " . $sql . "<br>" . $con->error;
    }
}

$con->close();


?>

<head>

    <title>Manage Resources</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../plugins/switchery/switchery.min.css">
    <script src="assets/js/modernizr.min.js"></script>
    <style>
        .c-flex-center {
            display: flex;
            justify-content: center;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .form-group label {
            font-size: medium;
            font-weight: bold;
            margin-bottom: 0 !important;
        }

        .form-group-check {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .add-user-form {
            width: 100%;
            display: flex;
            justify-content: start;
            align-items: start;

            margin-bottom: 20px;
            padding: 20px 0;
        }

        .add-user-left,
        .add-user-right {
            width: 40%;
        }

        .form-group-check label {
            font-size: medium;
            font-weight: bold;
            margin-bottom: 0 !important;
        }

        .form-group input,
        .form-group select {
            width: 75%;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }

        .form-group input[type="checkbox"] {
            width: auto;
        }

        .form-group button {
            width: fit-content;
            padding: 2px 4px;
            background-color: #007474;
            color: white;
            margin-top: 20px;
        }
    </style>

</head>

<body class="fixed-left">

    <!-- Begin page -->
    <div id="wrapper">

        <!-- Top Bar Start -->
        <?php include('includes/topheader.php'); ?>

        <!-- ========== Left Sidebar Start ========== -->
        <?php include('includes/leftsidebar.php'); ?>
        <!-- Left Sidebar End -->

        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->
        <div class="content-page">
            <!-- Start content -->
            <div class="content">
                <div class="container">

                    <div class="row">
                        <div class="col-xs-12">
                            <div class="page-title-box">
                                <ol class="breadcrumb p-0 m-0">
                                    <li>
                                        <a href="./admin-page.php">Admin</a>
                                    </li>
                                    <li>
                                        <a href="./userview.php">User</a>
                                    </li>

                                    <li class="active">
                                        Add User
                                    </li>
                                </ol>

                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>


                    <!-- Admin containers -->
                    <div class="outer-wrapper">
                        <div class="inner-wrapper">

                            <form action="" class="" method="post">
                                <div class="add-user-form">
                                    <div class="add-user-left">
                                        <div class="form-group">
                                            <label for="name">Name</label>
                                            <input type="text" id="name" name="name">
                                        </div>
                                        <div class="form-group">
                                            <label for="title">Title</label>
                                            <input type="text" id="title" name="title">
                                        </div>
                                        <div class="form-group">
                                            <label for="department">Department</label>
                                            <input type="text" id="department" name="department">
                                        </div>
                                        <div class="form-group">
                                            <label for="email">Email</label>
                                            <input type="email" id="email" name="email">
                                        </div>
                                        <div class="form-group-check">
                                            <label>Active</label>
                                            <input type="checkbox" checked name="isActive">
                                        </div>

                                    </div>
                                    <div class="add-user-left">
                                        <div class="form-group">
                                            <label for="role">Role</label>
                                            <select class="role-select" name="role" id="role">
                                                <option value="">Select Role</option>
                                                <option value="System Admin">System Admin</option>
                                                <option value="Admin">Admin</option>
                                                <option value="Manager">Manager</option>
                                                <option value="Client">Client</option>
                                                <option value="User">User</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="businessPhone">Business phone</label>
                                            <input type="text" id="businessPhone" name="business_phone">
                                        </div>
                                        <div class="form-group">
                                            <label for="mobilePhone">Mobile phone</label>
                                            <input type="text" id="mobilePhone" name="mobile_phone">
                                        </div>
                                        <div class="form-group">
                                            <label for="password">Password</label>
                                            <div style="position: relative; width: 75%;">
                                                <input type="hidden" id="isPasswordGenerated" name="isPasswordGenerated"
                                                    value="0">
                                                <input type="password" id="password" name="password"
                                                    style="width: 100%;">
                                                <span id="password-toggle"
                                                    style="position: absolute; right: 5px; top: 50%; transform: translateY(-50%); cursor: pointer;">
                                                    <i class="fa fa-eye"></i>
                                                </span>
                                            </div>
                                            <button type="button" onclick="generatePassword()">Generate</button>
                                        </div>
                                    </div>

                                </div>
                                <div class="form-group">
                                    <button type="submit">Save</button>
                                </div>

                            </form>

                        </div>
                    </div>

                </div>
            </div>
        </div>

        <script>
            var resizefunc = [];

            /* Randome password generator */
            function generatePassword() {
                const isPasswordGenerated = document.getElementById("isPasswordGenerated");
                fetch('./utils/password_generator.php') // Create a php file that returns the generated password.
                    .then(response => response.text())
                    .then(password => {
                        document.getElementById("password").value = password;
                        isPasswordGenerated.value = 1;
                    });

            }

            const passwordInput = document.getElementById("password");
            const passwordToggle = document.getElementById("password-toggle");

            passwordToggle.addEventListener("click", function () {
                if (passwordInput.type === "password") {
                    passwordInput.type = "text";
                    this.innerHTML = '<i class="fa fa-eye-slash"></i>';
                } else {
                    passwordInput.type = "password";
                    this.innerHTML = '<i class="fa fa-eye"></i>';
                }
            });
            /* End */

        </script>

        <!-- jQuery  -->
        <script src="assets/js/jquery.min.js"></script>
        <script src="assets/js/bootstrap.min.js"></script>
        <script src="assets/js/detect.js"></script>
        <script src="assets/js/fastclick.js"></script>
        <script src="assets/js/jquery.blockUI.js"></script>
        <script src="assets/js/waves.js"></script>
        <script src="assets/js/jquery.slimscroll.js"></script>
        <script src="assets/js/jquery.scrollTo.min.js"></script>

        <!-- App js -->
        <script src="assets/js/jquery.core.js"></script>
        <script src="assets/js/jquery.app.js"></script>

</body>