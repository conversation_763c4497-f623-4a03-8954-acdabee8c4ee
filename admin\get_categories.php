<?php
// get_categories.php
include('includes/config.php');
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if the database connection is established.
if (!$con) {
    echo json_encode(array('error' => 'Database connection error: ' . mysqli_connect_error()));
    exit;
}

if (isset($_POST['product_id'])) {
    $product_id = mysqli_real_escape_string($con, $_POST['product_id']);

    $query = "SELECT CategoryId, Category FROM tblcategory WHERE AreaId = '$product_id' AND Is_Active = 1";
    $result = mysqli_query($con, $query);

    if (!$result) {
        echo json_encode(array('error' => 'Database query error: ' . mysqli_error($con)));
        exit;
    }

    $categories = array();
    while ($row = mysqli_fetch_assoc($result)) {
        $categories[] = array(
            'CategoryId' => $row['CategoryId'],
            'Category' => $row['Category']
        );
    }

    header('Content-Type: application/json');
    echo json_encode($categories);
} else {
    echo json_encode(array('error' => 'Invalid Request: product_id is missing'));
}
