<?php
session_start();
include('../dist/include/config.php');

if (!isset($_SESSION['user'])) {
    header("Location:../login.php");
}

$loggedInUserEmail = $_SESSION['user']['email'];
$loggedInUserFirstName = explode(" ", $_SESSION['user']['name'])[0];
$loggedInUserLastName = explode(" ", $_SESSION['user']['name'])[1];
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Celaeno HelpDesk</title>

    <link rel="shortcut icon" href="../dist/images/favicon.ico">

    <script src="https://kit.fontawesome.com/1b5fdf4bb2.js" crossorigin="anonymous"></script>
    <link href="../dist/css/main.css" rel="stylesheet">
    <link href="../dist/css/input.css" rel="stylesheet">
    <link href="../dist/css/texteditor.css" rel="stylesheet">
    <style>
        .btn-container-8976000 {
            display: flex;
            align-items: center;
            justify-content: end;
            gap: 1rem;

        }

        .topic-add-btn {
            width: 5rem;
            padding: 2px;
            background: #007474;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
        }

        .topic-add-btn:hover {
            background: white;
            color: #007474;
            font-weight: 700;
        }

        .topic-cancle-btn {
            width: 6rem;
            padding: 2px;
            border: 1px solid #007474;
            background: white;
            color: #007474;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
        }

        .topic-cancle-btn:hover {
            background-color: #f1f1ec;
            font-weight: 700;
        }
        .ct-ticket-head {
            display: flex;
            gap: 1rem;
            margin-bottom: 20px;
        }
    </style>
</head>

<body>
    <section class="jumbotron jumbotron-fluid text-center">
        <nav class="ct-nav" style="">
            <div class="navbar navbar-dark navbar-static-top navbar-expand-lg">
                <a href="/celaeno_helpdesk/index.php" class="navbar-brand">
                    <small>Celaeno Assist</small>
                </a>
                <div class="ct-nav-list">
                    <a href="../index.php" class="">Home</a>
                    <a href="../knowledgebase.php" class="">Knowledge Base</a>
                </div>
                <div class="ct-head-btn-01">
                <?php
                    if (isset($_SESSION['user'])) {
                        echo '<a href="../logout.php"><button class="ct-help-btn">Log out</button></a>';
                    } else {
                        echo '<a href="../login.php"><button class="ct-help-btn">Log In</button></a>';
                    }
                    ?>
                </div>
            </div>
        </nav>

        <div class="cover"></div>

        <div class="container">
            <p class="lead">Welcome to</p>
            <h1 class="jumbotron-heading">Celaeno Assist</h1>

            <div class="row justify-content-center text-center">
                <div class="col-md-10">
                    <form id="searchForm" action="/" class="search-form" method="GET">
                        <div class="input-group search">
                            <input type="text" name="q" class="search-field" aria-label="Search field"
                                placeholder="Search anything here..." aria-required="true" required
                                autoComplete="off" />
                            <button class="input-group-addon" aria-label="Search">
                                <i class="fa fa-search">&nbsp;</i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <div class="section text-muted">
        <div class="container">
            <div class="row content justify-content-center">
                <div class="ct-help-ticket">
                    <div class="ct-ticket-body">
                        <div class="ct-ticket-head">
                            <a href="../knowledgebase.php">
                                <img src="../dist/images/backbtn.svg" alt="back" style="width: 32px; height: 32px;">
                            </a>
                            <h3>Submit a Ticket</h3>
                        </div>
                        <form method="POST" action="ticket_submit.php" enctype="multipart/form-data">
                            <input type="hidden" name="source" value="KNOWLEDGE_BASE">
                            <input type="hidden" name="channel" value="Web Form">
                            <div class="grid-container">
                                <div class="form-input">
                                    <input type="text" id="first_name" name="first_name" placeholder="First Name" value="<?php echo $loggedInUserFirstName;?>"
                                        required readonly/>
                                    <label for="first_name">First Name</label>
                                </div>
                                <div class="form-input">
                                    <input type="text" id="last_name" name="last_name" placeholder="Last Name" value="<?php echo $loggedInUserLastName;?>"
                                        required readonly/>
                                    <label for="last_name">Last Name</label>
                                </div>
                                <div class="form-input">
                                    <input type="email" id="email" name="email" placeholder="Email" value="<?php echo $loggedInUserEmail;?>" required readonly />
                                    <label for="email">Email</label>
                                </div>
                                <div class="custom-select">
                                    <select name="products" id="" required>
                                        <option value="none">Product</option>
                                        <option value="celaeno_booking">Celaeno Booking</option>
                                        <option value="celaeno_invoice">Celaeno Invoice</option>
                                        <option value="celaeno_timesheet">Celaeno Timesheet</option>
                                    </select>
                                </div>
                                <div class="form-input">
                                    <input type="text" id="subject" name="subject" placeholder="Subject" required />
                                    <label for="subject">Subject</label>
                                </div>
                                <div class="form-input">
                                    <input type="tel" id="phone" name="phone" placeholder="Phone" required />
                                    <label for="Phone">Phone</label>
                                </div>
                                <h4 style="margin-bottom: 1rem;">Description</h4>
                                <div class="form-input-des item6" style="margin-bottom: 20px;">
                                    <input type="hidden" name="texteditor" id="texteditor-hidden-Ticket-32ik45">
                                    <div class="wsyig" id="wsyigTicket-32ik45"></div>
                                </div>
                                <div class="custom-select">
                                    <select name="priority" id="" required>
                                        <option value="NONE">Priority</option>
                                        <option value="URGENT">Urgent</option>
                                        <option value="HIGH">High</option>
                                        <option value="MEDIUM">Medium</option>
                                        <option value="LOW">Low</option>
                                    </select>
                                </div>
                                <div class="custom-select">
                                    <select name="request_type" id="" required>
                                        <option value="NONE">Request Type</option>
                                        <option value="QUESTION">Question</option>
                                        <option value="NOT_WORKING">Something isn't working</option>
                                        <option value="PAYMENT">Payment</option>
                                        <option value="SUBSCRIPTION">Subscription</option>
                                        <option value="OTHER">Other</option>
                                    </select>
                                </div>

                            </div>
                            <div class="form-input">
                                <input type="file" class="form-control" id="attachment" name="attachment">
                                <label for="attachment" class="form-label">Attachment</label>
                            </div>
                            <div class="btn-container-8976000">

                                <button type="button" onclick="closePopup()" class="topic-cancle-btn">Cancel</button>
                                <button type="submit" class="topic-add-btn">Save</button>
                            </div>

                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <footer>
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    Celaeno HelpDesk
                    <p class="mt-1"><strong><small class="text-muted">Copyright © 2024 , All Right Reserved by <a
                                    href="#">Celaeno
                                    Technology</a></small></strong></p>
                </div>
                <div class="col-md-6 text-right">
                    <p>
                        <a href="#">Back to top</a>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="../dist/js/main.js"></script>
    <script src="../dist/js/TextEditor.js"></script>
    <script>
        new TextEditor("wsyigTicket-32ik45", "texteditor-hidden-Ticket-32ik45");
    </script>
    <script>
        var x, i, j, l, ll, selElmnt, a, b, c;
        x = document.getElementsByClassName("custom-select");
        l = x.length;
        for (i = 0; i < l; i++) {
            selElmnt = x[i].getElementsByTagName("select")[0];
            ll = selElmnt.length;

            // Create the selected item DIV
            a = document.createElement("DIV");
            a.setAttribute("class", "select-selected");
            a.innerHTML = selElmnt.options[selElmnt.selectedIndex].innerHTML;
            x[i].appendChild(a);

            // Create the options container DIV
            b = document.createElement("DIV");
            b.setAttribute("class", "select-items select-hide");

            // Loop through all options in the original select element
            for (j = 0; j < ll; j++) {
                c = document.createElement("DIV");
                c.innerHTML = selElmnt.options[j].innerHTML;

                if (selElmnt.options[j].disabled) {
                    c.classList.add("disabled-option");
                    c.style.color = "#aaa";
                    c.style.pointerEvents = "none";
                }

                // Add click event to each option
                c.addEventListener("click", function (e) {
                    var y, i, k, s, h, sl, yl;
                    s = this.parentNode.parentNode.getElementsByTagName("select")[0];
                    sl = s.length;
                    h = this.parentNode.previousSibling;
                    for (i = 0; i < sl; i++) {
                        if (s.options[i].innerHTML == this.innerHTML) {
                            s.selectedIndex = i;
                            h.innerHTML = this.innerHTML;
                            y = this.parentNode.getElementsByClassName("same-as-selected");
                            yl = y.length;
                            for (k = 0; k < yl; k++) {
                                y[k].removeAttribute("class");
                            }
                            this.setAttribute("class", "same-as-selected");
                            break;
                        }
                    }
                    h.click();
                });
                b.appendChild(c);
            }
            x[i].appendChild(b);

            // Add event listener to toggle dropdown
            a.addEventListener("click", function (e) {
                e.stopPropagation();
                closeAllSelect(this);
                this.nextSibling.classList.toggle("select-hide");
                this.classList.toggle("select-arrow-active");
            });
        }

        // Function to close all dropdowns
        function closeAllSelect(elmnt) {
            var x, y, i, xl, yl, arrNo = [];
            x = document.getElementsByClassName("select-items");
            y = document.getElementsByClassName("select-selected");
            xl = x.length;
            yl = y.length;
            for (i = 0; i < yl; i++) {
                if (elmnt == y[i]) {
                    arrNo.push(i)
                } else {
                    y[i].classList.remove("select-arrow-active");
                }
            }
            for (i = 0; i < xl; i++) {
                if (arrNo.indexOf(i)) {
                    x[i].classList.add("select-hide");
                }
            }
        }

        document.addEventListener("click", closeAllSelect);
    </script>
</body>

</html>