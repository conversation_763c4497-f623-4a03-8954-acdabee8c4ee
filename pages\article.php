<?php
session_start();
include('../dist/include/config.php');
//Genrating CSRF Token
if (empty($_SESSION['token'])) {
    $_SESSION['token'] = bin2hex(random_bytes(32));
}

if (isset($_POST['submit'])) {
    //Verifying CSRF Token

    if (!empty($_POST['csrftoken'])) {

        if (hash_equals($_SESSION['token'], $_POST['csrftoken'])) {

            $name = $_POST['name'];
            $email = $_POST['email'];
            $comment = $_POST['comment'];
            $postid = intval($_GET['nid']);
            $st1 = '0';
            $query = mysqli_query($con, "insert into tblcomments(postId,name,email,comment,status) values('$postid','$name','$email','$comment','$st1')");
            $t = "insert into tblcomments(postId,name,email,comment,status) values('$postid','$name','$email','$comment','$st1')";

            if ($query):
                echo "<script>alert('comment successfully submit. Comment will be display after admin review ');</script>";
                unset($_SESSION['token']);
            else:
                echo "<script>alert('Something went wrong. Please try again.');</script>";

            endif;
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Celaeno HelpDesk</title>

    <link rel="shortcut icon" href="/dist/images/favicon.ico">

    <script src="https://kit.fontawesome.com/1b5fdf4bb2.js" crossorigin="anonymous"></script>
    <!-- Include jQuery from CDN -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <link href="../dist/css/main.css" rel="stylesheet">

    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        * {
            box-sizing: border-box;
            margin: 0px;
        }

        body {
            font-family: Arial, Helvetica, sans-serif;
            margin: 0;
        }

        /*header*/
        .brand {
            text-align: center;
            float: end;
            margin-top: 0px;
        }

        .brand a {
            text-decoration: none;
            color: #fff;
            font-family: Berlin Sans FB;
            -webkit-animation-name: example;
            /* Chrome, Safari, Opera */
            -webkit-animation-duration: 4s;
            /* Chrome, Safari, Opera */
            animation-name: branddesign;
            animation-duration: 4s;
            animation-iteration-count: 2;
        }


        /*sidenavigation*/
        .sidenav {
            height: 100%;
            width: 0;
            position: fixed;
            z-index: 1;
            top: 0;
            left: 0;
            background-color: white;
            overflow-x: hidden;
            transition: 0.5s;
            padding-top: 60px;
        }

        .sidenav a {
            padding: 8px 8px 8px 32px;
            text-decoration: none;
            font-size: 25px;
            display: block;
            transition: 0.3s
        }

        .sidenav a:hover {
            color: #ff5a1c;
            text-decoration-thickness: 3px;
        }

        .closebtn {
            font-size: 36px !important;
        }

        .main-side-container {
            display: -webkit-flex;
            display: flex;
            -webkit-flex-wrap: nowrap;
            flex-wrap: nowrap;
        }

        .main-side-container div a {
            font-size: 15pt;
        }

        .main-side-container div hr {
            height: 2px;
            width: 80%;
            border: none;
            border-radius: 7px;
            background-color: #fff;
        }

        .w-f-u {
            background-color: #00b600;
            border-radius: 5px;
        }

        .w-f-u:hover {
            color: white !important;
            box-shadow: 0px 0px 10px red;
        }

        .sub-side-container:first-child {
            color: red;
        }

        h4 {
            text-align: center;
            font-size: 60px;
            margin-top: 0px;
        }

        .svg-clock {
            width: 11px;
            fill: grey;
        }

        /* Increase the font size of the h1 element */
        .header h1 {
            font-size: 40px;
        }

        /* Column container */
        .rows {
            display: flex;
            flex-wrap: wrap;
        }

        /* Create two unequal columns that sits next to each other */
        /* Sidebar/left column */
        .side {
            flex: 30%;
            background-color: #f1f1f1;
            padding: 20px;
        }

        /* Main column */
        .main {
            flex: 70%;
            background-color: white;
            padding: 50px;
        }

        /*  image */
        .imgprops {
            width: 100%;
        }



        .sidenav {
            height: 100%;
            width: 0;
            position: fixed;
            z-index: 2;
            top: 0;
            right: 0;
            overflow-x: hidden;
            transition: 0.5s;
            padding: 0px;
            opacity: .90;
        }

        .sidenav a {
            padding: 8px 8px 8px 32px;
            text-decoration: none;
            font-size: 25px;
            color: #fff;
            display: block;
            transition: 0.3s;

        }

        .sidenav a:hover,
        .offcanvas a:focus {
            color: red;
            text-decoration: none;
        }

        .closebtn {
            position: relative;
            top: 0;
            right: 25px;
            font-size: 36px !important;

        }

        #main {
            transition: margin-left .5s;

            color: white;
        }



        .top-nav {
            margin-bottom: 70px;
            margin-left: 20%;
        }

        .nav {
            width: auto;
            height: 40px;
            border: none;
            line-height: 10px;

        }

        .top-nav a {
            text-decoration: none;
            color: #fff;
            font-family: Berlin Sans FB;
        }

        .top-nav a:hover {
            color: red;
        }

        .top-nav a li {
            list-style: none;
            float: left;
            font-size: 12pt;
            margin-right: 20px;
        }

        .header-box {
            background-color: #1d2330;
            height: 70px;
        }

        .News-heading {
            text-decoration: navy;

            font-size: 12pt;
            display: flex;
            flex-direction: row;
        }

        .News-heading:before,
        .News-heading:after {
            content: "";
            flex: 1 1;
            border-bottom: 1px solid;
            margin: auto;
        }

        .News-heading:before {
            margin-right: 10px;
        }

        .News-heading:after {
            margin-left: 10px;
        }



        .card-title-font {
            color: #113277;
            font-size: 15pt
        }

        .postimgtop {
            width: 100%;
            height: 320px;
            border-radius: 5px 5px 0 0;
        }

        .postimgtop-con {
            height: auto;
        }

        .postimgtopt {
            width: 100%;
            height: 150px;
            border-radius: 5px 5px 0 0;
        }

        .fixed-bar {
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1;
        }

        /*scroll to top*/
        @keyframes rotate {
            to {
                transform: rotate(2520deg);
            }
        }

        #myBtn {
            display: none;
            position: fixed;
            bottom: 20px;
            right: 30px;
            z-index: 99;
            font-size: 18px;
            border: none;
            outline: none;
            background-color: red;
            color: white;
            cursor: pointer;
            padding: 15px;
            border-radius: 4px;
        }

        #myBtn:hover {
            background-color: #555;
            animation-name: rotate;
            animation-duration: 3s;
            animation-timing-function: linear;
            animation-iteration-count: infinite;
        }
    </style>
</head>

<body>

    <section class="jumbotron jumbotron-fluid text-center header-small">
        <div class="navbar navbar-dark navbar-static-top navbar-expand-lg" role="navbar">
            <a href="/celaeno_helpdesk/index.php" class="navbar-brand">
            <img src="../dist/images/logo.jpg" alt="" srcset="">
            </a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarText"
                aria-controls="navbarText" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="ct-nav-list">
                    <a href="../index.php" class="">Home</a>
                    <!-- <a href="knowledgebase.php" class="">Knowledge Base</a> -->
                </div>
            <!-- <div class="collapse navbar-collapse" id="navbarText">
                <ul class="nav navbar-nav">
                    <li class="nav-item header-link" style=padding-top:10px;>
                        <a class="nav-link" href="../index.php">Home</a>
                    </li>

                    <li class="nav-item">
                        <div class="nav-link dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="dropdownMenuButton"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                English
                            </button>
                            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton">
                                <a class="dropdown-item" href="#fr">French</a>
                                <a class="dropdown-item" href="#bg">Bulgarian</a>
                            </div>
                        </div>
                    </li>
                </ul>
            </div> -->
        </div>

        <div class="cover"></div>


    </section>





    <!-- Page Content -->
    <div class="rows">

        <div class="main">
            <div class="imgprops" style="height:auto;">
                <!-- Blog Post -->
                <?php
                $pid = intval($_GET['nid']);
                $query = mysqli_query($con, "SELECT
                                            tblarticles.ArticleTitle AS posttitle,
                                            tblarticles.ArticleImage,
                                            tblarea.ProductName AS PRODUCT,
                                            tblcategory.Category AS category,
                                            tblcategory.CategoryId AS cid,
                                            tblarticles.ArticleDetails AS postdetails,
                                            tblarticles.PostingDate AS postingdate,
                                            tblarticles.ArticleUrl AS url
                                        FROM
                                            tblarticles
                                        LEFT JOIN tblarea ON tblarea.id = tblarticles.AreaId
                                        LEFT JOIN tblcategory ON tblcategory.CategoryId = tblarticles.CategoryId
                                        WHERE
                                            tblarticles.id = '$pid'");
                while ($row = mysqli_fetch_array($query)) {
                    ?>

                    <div class="card mb-4">

                        <div class="card-body">
                            <h2 class="card-title"><?php echo htmlentities($row['posttitle']); ?></h2>
                            <p><b>Category : </b> <a
                                    href="category.php?catid=<?php echo htmlentities($row['cid']) ?>"><?php echo htmlentities($row['category']); ?></a>
                                |
                                <b> Posted on </b><?php echo htmlentities($row['postingdate']); ?>
                            </p>
                            <hr />


                            <p class="card-text"><?php
                            $pt = $row['postdetails'];
                            echo (substr($pt, 0)); ?></p>

                        </div>
                        <div class="card-footer text-muted">


                        </div>
                    </div>
                <?php } ?>


            </div>
            <!-- <div class="imgprops" style="height:auto;">
             
                <div class="row">
                    <div class="col-md-12">
                        <div class="card my-4">
                            <h5 class="card-header">Leave a Comment:</h5>
                            <div class="card-body">
                                <form name="Comment" method="post">
                                    <input type="hidden" name="csrftoken" value="<?php echo htmlentities($_SESSION['token']); ?>" />
            <input type="hidden" name="postId" value="<?php echo $_GET['nid']; ?>" />  

            <div class="form-group">
                <input type="text" name="name" class="form-control" placeholder="Enter your fullname" required>
            </div>

            <div class="form-group">
                <input type="email" name="email" class="form-control" placeholder="Enter your Valid email" required>
            </div>

            <div class="form-group">
                <textarea class="form-control" name="comment" rows="3" placeholder="Comment" required></textarea>
            </div>
            <button type="submit" class="btn btn-primary" name="submit">Submit</button>
            </form>
        </div>
    </div>


            <?php
            // $sts = 1;
            // $query = mysqli_query($con, "SELECT name, comment, postingDate FROM tblcomments WHERE postId='$pid' and status='$sts'");
            // while ($row = mysqli_fetch_array($query)) {
            ?>
                <div class="media mb-4">
                    <img class="d-flex mr-3 rounded-circle" src="images/usericon.png" alt="image">
                    <div class="media-body">
                        <h5 class="mt-0"> <?php //echo htmlentities($row['name']); 
                        ?> <br />
                            <span style="font-size:11px;"><b>at</b>
                                <?php //echo htmlentities($row['postingDate']); 
                                ?></span>
                        </h5>

                        <?php //echo htmlentities($row['comment']); 
                        ?>
                    </div>
                </div>
            <?php //} 
            ?>

        </div>
    </div>

    </div> -->


            <div class="article">
                <p>Helpful?</p>

                <!-- Include Font Awesome for icons and Bootstrap for styling -->
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">

                <div class="d-flex align-items-center gap-3 mt-3">
                    <!-- Like Button -->
                    <button class="btn btn-success btn-lg d-flex align-items-center gap-2"
                        onclick="updateReview('like')">
                        <i class="fa-solid fa-thumbs-up"></i> <span id="like-count">0</span>
                    </button>

                    <!-- Dislike Button -->
                    <button class="btn btn-danger btn-lg d-flex align-items-center gap-2"
                        onclick="updateReview('dislike')">
                        <i class="fa-solid fa-thumbs-down"></i> <span id="dislike-count">0</span>
                    </button>
                </div>




            </div>
        </div>

        <div class="side">

            <div class="imgprops" style="height:auto;">
                <!-- Sidebar Widgets Column -->
                <?php include('../dist/include/sidebar.php'); ?>
            </div>


            <?php
            function time_elapsed_string($datetime, $full = false)
            {
                $now = new DateTime;
                $ago = new DateTime($datetime);
                $diff = $now->diff($ago);

                $diff->w = floor($diff->d / 7);
                $diff->d -= $diff->w * 7;

                $string = array(
                    'y' => 'year',
                    'm' => 'month',
                    'w' => 'week',
                    'd' => 'day',
                    'h' => 'hour',
                    'i' => 'minute',
                    's' => 'second',
                );
                foreach ($string as $k => &$v) {
                    if ($diff->$k) {
                        $v = $diff->$k . ' ' . $v . ($diff->$k > 1 ? 's' : '');
                    } else {
                        unset($string[$k]);
                    }
                }
                if (!$full)
                    $string = array_slice($string, 0, 1);
                return $string ? implode(', ', $string) . ' ago' : 'just now';
            }
            ?>
        </div>
        <div class="imgprops" style="height:auto;"></div>
    </div>


    </div>

    </div>

    <footer>
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    Celaeno HelpDesk
                    <p class="mt-1"><strong><small class="text-muted">Copyright © 2024 , All Right Reserved by <a
                                    href="#">Celaeno Technology</a></small></strong></p>
                </div>
                <div class="col-md-6 text-right">
                    <p>
                        <a href="#">Back to top</a>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- <script>
        //Get the button
        var mybutton = document.getElementById("myBtn");

        // When the user scrolls down 20px from the top of the document, show the button
        window.onscroll = function() {
            scrollFunction()
        };

        function scrollFunction() {
            if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                mybutton.style.display = "block";
            } else {
                mybutton.style.display = "none";
            }
        }

        // When the user clicks on the button, scroll to the top of the document
        function topFunction() {
            document.body.scrollTop = 0;
            document.documentElement.scrollTop = 0;
        }
    </script> -->

    <script>
        function getUrlParameter(name) {
            let params = new URLSearchParams(window.location.search);
            return params.get(name);
        }

        // Get 'id' from the URL (e.g., example.com/page.php?id=1)
        let reviewId = getUrlParameter('nid');

        function updateReview(action) {
            $.ajax({
                url: 'update_review.php',
                type: 'POST',
                data: {
                    id: reviewId,
                    action: action
                },
                success: function (response) {
                    console.log(response)
                    let data = JSON.parse(response);
                    $('#like-count').text(data.likes);
                    $('#dislike-count').text(data.dislikes);
                }
            });
        }

        $(document).ready(function () {
            // Function to get query parameters from URL


            if (reviewId) { // Ensure ID is not null
                $.ajax({
                    url: 'get_review.php',
                    type: 'GET',
                    data: {
                        id: reviewId
                    },
                    success: function (response) {
                        console.log(response.likes)

                        let data = (response);
                        $('#like-count').text(data.likes);
                        $('#dislike-count').text(data.dislikes);
                    },
                    error: function (xhr, status, error) {
                        console.error("AJAX Error:", error);
                    }
                });
            } else {
                console.warn("No ID found in URL.");
            }
        });
    </script>

    <!-- Bootstrap core JavaScript -->
    <!-- <script src="vendor/jquery/jquery.min.js"></script>
    <script src="vendor/bootstrap/js/bootstrap.bundle.min.js"></script> -->

    <script src="../dist/js/main.js"></script>
</body>

</html>