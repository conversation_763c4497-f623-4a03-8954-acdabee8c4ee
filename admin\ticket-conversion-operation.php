<?php
session_start();
require_once("./includes/config.php");

function saveNewConversationMessage(mysqli $con, string $ticketId, int $senderId, string $senderType, string $message): ?int
{
    $stmt = $con->prepare("INSERT INTO tblticket_conversation (ticket_id, sender_id, sender_type, message) VALUES (?, ?, ?, ?)");
    if ($stmt) {
        $stmt->bind_param("siss", $ticketId, $senderId, $senderType, $message);
        if ($stmt->execute()) {
            $lastId = $con->insert_id;
            $stmt->close();
            return $lastId;
        } else {
            error_log("Error saving new conversation message: " . $stmt->error);
            $stmt->close();
            return null;
        }
    } else {
        error_log("Error preparing statement for new conversation message: " . $con->error);
        return null;
    }
}

$currentUserId = isset($_SESSION['logged_in_user']["id"]) ? $_SESSION['logged_in_user']["id"] : null; // Get current user ID if logged in
$currentUserType = isset($_POST['senderType']) ? trim($_POST['senderType']) : null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['newMessage']) && isset($_POST['ticketId']) && $currentUserId && $currentUserType) {
    $newMessage = json_decode(trim($_POST['newMessage']));
    $currentTicketId = $_POST['ticketId'];
    if (!empty($newMessage)) {
        if (saveNewConversationMessage($con, $currentTicketId, $currentUserId, $currentUserType, $newMessage)) {
            // Optionally, redirect to refresh the page and show the new message
            header("Location: ./ticket_view.php?ticket_id=" . urlencode($currentTicketId));
            exit();
        } else {
            $errorMessage = "Failed to send message.";
        }
    } else {
        $errorMessage = "Message cannot be empty.";
    }
}

?>