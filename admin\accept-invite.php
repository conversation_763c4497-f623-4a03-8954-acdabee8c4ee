<?php
session_start();
include "./includes/config.php";

if (!isset($_GET['token'])) {
    die("Invalid or missing token.");
}

try {
    // Fetch invitation details
    $token = $_GET['token'];
    $stmt = $con->prepare("SELECT * FROM invitations WHERE token = ?");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();
    $invitation = $result->fetch_assoc();
    $stmt->close();

    if (!$invitation) {
        die("Invalid or expired invitation.");
    }

    // Check if the invitation has expired
    if (strtotime($invitation['expires_at']) < time()) {
        die("This invitation has expired.");
    }

    // Check if the email is already registered
    $email = $invitation['email'];
    $stmt = $con->prepare("SELECT COUNT(*) FROM tblusers WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $stmt->bind_result($userCount);
    $stmt->fetch();
    $stmt->close();

    if ($userCount > 0) {
        die("This email is already registered.");
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $password = trim($_POST['password'] ?? '');
        $confirmPassword = trim($_POST['confirm_password'] ?? '');

        // Validate input
        if (empty($password) || empty($confirmPassword)) {
            $error = "Please fill in all required fields (username, password, and confirm password).";
        } elseif ($password !== $confirmPassword) {
            $error = "Passwords do not match.";
        } elseif (strlen($password) < 8) {
            $error = "Password must be at least 8 characters long.";
        } else {
            // Hash the password
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

            // Start a transaction to ensure data consistency
            $con->begin_transaction();

            try {
                // Insert the user
                $stmt = $con->prepare("
                    INSERT INTO tblusers (name, email, role, password, isActive, created_at)
                    VALUES (?, ?, ?, ?, 1, NOW())
                ");
                $stmt->bind_param("ssss", $invitation['name'], $invitation['email'], $invitation['role'], $hashedPassword);
                $stmt->execute();
                $userId = $con->insert_id;
                $stmt->close();

                // Delete the invitation
                $stmt = $con->prepare("DELETE FROM invitations WHERE id = ?");
                $stmt->bind_param("i", $invitation['id']);
                $stmt->execute();
                $stmt->close();

                // Commit the transaction
                $con->commit();

                // Redirect to login page with success message
                header("Location: index.php?message=" . urlencode("Account created successfully. Please log in."));
                exit;
            } catch (mysqli_sql_exception $e) {
                $con->rollback();
                $error = "Error creating account: " . $e->getMessage();
            }
        }
    }
} catch (Exception $e) {
    die("Error: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accept Invitation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background: #f0f0f0;
        }

        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            width: 400px;
        }

        .container h2 {
            text-align: center;
            margin-bottom: 20px;
        }

        .container label {
            display: block;
            margin-bottom: 5px;
        }

        .container input {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .container button {
            width: 100%;
            padding: 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .container button:hover {
            background: #0056b3;
        }

        .error {
            color: red;
            text-align: center;
            margin-bottom: 10px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h2>Accept Invitation</h2>
        <?php if (isset($error)): ?>
            <p class="error"><?php echo htmlspecialchars($error); ?></p>
        <?php endif; ?>
        <form method="POST" action="">
            <label for="password">Password <span style="color: red;">*</span>:</label>
            <input type="password" id="password" name="password" required>

            <label for="confirm_password">Confirm Password <span style="color: red;">*</span>:</label>
            <input type="password" id="confirm_password" name="confirm_password" required>

            <button type="submit">Set Up Account</button>
        </form>
    </div>
</body>

</html>