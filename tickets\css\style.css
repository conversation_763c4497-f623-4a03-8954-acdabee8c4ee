* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

header {
    margin: 1rem auto;
    width: 90vw;
    display: flex;
    justify-content: space-between;
}

header button {
    border-radius: 10px;
    background: rgb(140, 202, 202);
    border: none;
    padding: .5rem 1rem;
}

.filter-con {
    width: 10rem;
}

main {
    width: 100vw;
    height: 90vh;
}

.list {
    display: flex;
    flex-direction: column;
    padding: .5rem 2rem;
    gap: 1rem;
}

.item {
    width: 100%;
    display: flex;
    justify-content: space-between;
    box-shadow: 0px 3px 3px 0px rgba(100, 100, 111, 0.2);
    border-radius: 10px;
    padding: .3rem 1rem;
    cursor: pointer;
}

.content {
    display: flex;
    flex-direction: column;
    width: 95%;
    height: 100%;
}

.other {
    display: flex;
    padding-left: .5rem;
    gap: 1rem;
}

.actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    width: 20%;
}

.status,
.priority {
    width: 7rem;
    border: none;
}

.status .ss-arrow,
.priority .ss-arrow {
    display: none;
}

.status:focus .ss-arrow,
.priority:focus .ss-arrow {
    display: flex;
}

.status .ss-values option {
    color: black !important;
    background-color: var(--bg-color);
    padding: .3rem 1rem;
    border-radius: 20px;
}

.assignee {
    height: 2rem;
    width: 2rem;
}

.assignee img {
    height: 100%;
    width: 100%;
    border-radius: 50%;
}

.pop-up {
    width: 80%;
    height: 100vh;
    display: flex;
    position: absolute;
    right: 0;
    top: 0;
    background-color: #fff;
    box-shadow: -3px 3px 3px 0px rgba(100, 100, 111, 0.2);
}

.main {
    width: 70%;
    margin-top: 1rem;
}

.main .heading {
    padding: .5rem 3rem;
}

.main .tabs {
    padding: .5rem 2rem;
    display: flex;
    gap: 1rem;
}

.main .tabs button {
    border: none;
    background-color: transparent;
    cursor: pointer;
    padding: .5rem;
}

.main .tabs button.active {
    border-bottom: 2px solid gray;
}

.tab-content {
    overflow: auto;
}

.tab-content::-webkit-scrollbar {
    width: .5rem;
}

.main .content>div {
    width: 100%;
    height: 100%;
    margin: 1rem;

}

.main .content>div {
    display: none;
}

.main .content .active {
    display: initial;
}

#conversation .comment {
    display: flex;
}

#conversation .comment .img {
    width: 5rem;
    height: 2rem;
    padding-right: 1rem;
}

#conversation .comment .img img {
    width: 100%;
    height: 100%;
}

#conversation .comment .content {
    width: 90%;
}

#comment-box,
#resolustion-box {
    margin-bottom: 2rem;
    width: 100%;
    height: max-content;
}

/* History design */
#history {
    overflow: auto;
    height: 100%;
}

:root {
    --hue: 223;
    --bg: hsl(var(--hue), 10%, 90%);
    --fg: hsl(var(--hue), 10%, 10%);
    --primary: hsl(var(--hue), 90%, 50%);
    --trans-dur: 0.3s;
    --trans-timing: cubic-bezier(0.65, 0, 0.35, 1);
}

a {
    color: var(--primary);
    transition: color var(--trans-dur);
}

#history button {
    font: 1em/1.5 "IBM Plex Sans", sans-serif;
}

#history .btn {
    font-size: .8em;
}

h1 {
    font-size: 2em;
    margin: 0 0 3rem;
    padding-top: 1.5rem;
    text-align: center;
}

.btn {
    background-color: rgb(136, 194, 194);
    border-radius: 10.25em;
    color: var(--bg);
    cursor: pointer;
    padding: 0.5em 1em;
    border: none;
    transition:
        background-color calc(var(--trans-dur) / 2) linear,
        color var(--trans-dur);
    -webkit-tap-highlight-color: transparent;
}

.btn:hover {
    background-color: hsl(var(--hue), 10%, 50%);
}

.btn-group {
    display: flex;
    gap: 0.375em;
    margin-bottom: 1.5em;
}

.timeline {
    margin: auto;
    padding: 0 1.5em;
    width: 100%;
}

.timeline__arrow {
    background-color: transparent;
    border-radius: 0.25em;
    cursor: pointer;
    flex-shrink: 0;
    margin-inline-end: 0.25em;
    outline: transparent;
    width: 1.5em;
    height: 1.5em;
    transition:
        background-color calc(var(--trans-dur) / 2) linear,
        color var(--trans-dur);
    -webkit-appearance: none;
    appearance: none;
    -webkit-tap-highlight-color: transparent;
}

.timeline__arrow:focus-visible,
.timeline__arrow:hover {
    background-color: hsl(var(--hue), 10%, 50%, 0.4);
}

.timeline__arrow-icon {
    display: block;
    pointer-events: none;
    transform: rotate(-90deg);
    transition: transform var(--trans-dur) var(--trans-timing);
    width: 100%;
    height: auto;
}

.timeline__date {
    font-size: 0.833em;
    line-height: 2.4;
}

.timeline__dot {
    background-color: currentColor;
    border-radius: 50%;
    display: inline-block;
    flex-shrink: 0;
    margin: 0.625em 0;
    margin-inline-end: 1em;
    position: relative;
    width: 0.75em;
    height: 0.75em;
}

.timeline__item {
    position: relative;
    padding-bottom: 2.25em;
}

.timeline__item:not(:last-child):before {
    background-color: currentColor;
    content: "";
    display: block;
    position: absolute;
    top: 1em;
    left: 2.25em;
    width: 0.125em;
    height: 100%;
    transform: translateX(-50%);
}

[dir="rtl"] .timeline__arrow-icon {
    transform: rotate(90deg);
}

[dir="rtl"] .timeline__item:not(:last-child):before {
    right: 2.625em;
    left: auto;
    transform: translateX(50%);
}

.timeline__item-header {
    display: flex;
}

.timeline__item-body {
    border-radius: 0.375rem;
    overflow: hidden;
    margin-top: 0.5rem;
    margin-inline-start: 4rem;
    height: 0;
}

.timeline__item-body-content {
    background-color: hsl(var(--hue), 10%, 50%, 0.2);
    opacity: 0;
    padding: 0.5rem 0.75rem;
    visibility: hidden;
    transition:
        opacity var(--trans-dur) var(--trans-timing),
        visibility var(--trans-dur) steps(1, end);
}

.timeline__meta {
    width: 100%;
}

.timeline__title {
    font-size: 1.2em;
    line-height: 1.333;
}

/* Expanded state */
.timeline__item-body--expanded {
    height: auto;
}

.timeline__item-body--expanded .timeline__item-body-content {
    opacity: 1;
    visibility: visible;
    transition-delay: var(--trans-dur), 0s;
}

.timeline__arrow[aria-expanded="true"] .timeline__arrow-icon {
    transform: rotate(0);
}

/* Dark theme */
@media (prefers-color-scheme: dark) {
    :root {
        --bg: hsl(var(--hue), 10%, 10%);
        --fg: hsl(var(--hue), 10%, 90%);
        --primary: hsl(var(--hue), 90%, 70%);
    }
}

/* Sidebar */
.sidebar {
    padding: .5rem 2rem;
    display: flex;
    height: 100%;
    flex-direction: column;
    border-left: 2px solid rgba(128, 128, 128, 0.39);
}

.sidebar div {
    display: flex;
    flex-direction: column;
    margin: 1rem .5rem;
}

.sidebar .info {}

.sidebar div span {
    color: gray;
    text-transform: capitalize;
}

.sidebar .assignee-info {
    display: flex;
    gap: 2rem;
}

.sidebar .assignee-info b {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar .assignee-info img {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
}