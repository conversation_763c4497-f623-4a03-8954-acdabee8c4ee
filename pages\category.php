<?php
session_start();
include('../dist/include/config.php');
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Celaeno HelpDesk</title>


    <link rel="shortcut icon" href="/dist/images/favicon.ico">

    <script src="https://kit.fontawesome.com/1b5fdf4bb2.js" crossorigin="anonymous"></script>
    <link href="../dist/css/main.css" rel="stylesheet">
    <style>
        .col-md-8 h3 {
            margin-bottom: 25px;
        }

        .col-md-6 {
            text-align: center;
        }
    </style>
</head>

<body>

    <section class="jumbotron jumbotron-fluid text-center">
        <nav class="ct-nav" style="">
            <div class="navbar navbar-dark navbar-static-top navbar-expand-lg">
                <a href="/celaeno_helpdesk/index.php" class="navbar-brand">
                    <small>Celaeno Assist</small>
                </a>
                <div class="ct-nav-list">
                    <a href="../index.php" class="">Home</a>
                    <a href="../knowledgebase.php" class="">Knowledge Base</a>
                </div>
                <div class="ct-head-btn-01">
                    <!-- <button class="ct-help-btn">Sign In</button> -->
                </div>
            </div>
        </nav>

        <div class="cover"></div>

        <div class="container">
            <h1 class="jumbotron-heading">Welcome to Celaeno Assist</h1>

            <div class="row justify-content-center text-center">
                <div class="col-md-10">
                    <form id="searchForm" action="/" class="search-form" method="GET">
                        <div class="input-group search">
                            <input type="text" name="q" class="search-field" aria-label="Search field"
                                placeholder="Search anything here..." aria-required="true" required
                                autoComplete="off" />
                            <button class="input-group-addon" aria-label="Search">
                                <i class="fa fa-search">&nbsp;</i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <div class="container">
        <div class="row content section paddingt-2 white-background">
            <div class="col-sm-12">
                <div class="row">
                    <div class="col-md-12">
                        <nav aria-label="breadcrumb" role="navigation">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="../index.php">Home</a></li>
                                <li class="breadcrumb-item active">Before you start</li>
                            </ol>
                        </nav>
                    </div>
                </div>
                <div class="row justify-content-between">
                    <div class="col-12 text-left d-block d-md-none">
                        <h2 style="margin-bottom:30px;">
                            <a href="#categories-nav" style="line-height:.5rem;" data-toggle="collapse" role="button"
                                aria-expanded="false" aria-controls="categories-nav" class="btn btn-outline-secondary">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" width="30" height="30"
                                    focusable="false" role="img">
                                    <title>Categories</title>
                                    <path stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-miterlimit="10" d="M4 7h22M4 15h22M4 23h22"></path>
                                </svg>
                            </a> Categories
                        </h2>
                        <div id="categories-nav" class="collapse">
                            <ul class="nav categories-nav nav-pills flex-column" role="navigation">
                                <li class="nav-item  open">
                                    <a class="nav-link active" href="#">Get Started</a>
                                    <ul class="nav nav-pills flex-column">
                                        <li class="nav-item">
                                            <a class="nav-link" href="#">Get Started with One Thing</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" href="#">Get Started with Something Else</a>
                                        </li>
                                    </ul>
                                </li>
                                <li class="nav-item ">
                                    <a class="nav-link" href="#">Authentication</a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-md-8">
                        <?php
                        // Get main category details
                        $catId = mysqli_real_escape_string($con, $_GET["catid"]);
                        $query = "SELECT Category FROM tblcategory WHERE CategoryId = $catId";
                        $result = mysqli_query($con, $query);
                        if ($result && mysqli_num_rows($result) > 0) {
                            $cname = mysqli_fetch_assoc($result)["Category"];
                            echo '<h1><i class="fas fa-map-signs"> </i> ' . htmlspecialchars($cname) . '</h1>';
                        } else {
                            echo '<h1>Category Not Found</h1>';
                        }
                        ?>

                        <hr />

                        <?php
                        // Check for subcategories
                        $subcatQuery = "SELECT SubCategoryId, SubCategory FROM tblsubcategory WHERE CategoryId = $catId";
                        $subcatResult = mysqli_query($con, $subcatQuery);
                        $hasSubcategories = $subcatResult && mysqli_num_rows($subcatResult) > 0;

                        // Check for articles
                        $selectArticleQuery = "SELECT * FROM tblarticles WHERE CategoryId = $catId";
                        $selectArticleFetch = mysqli_query($con, $selectArticleQuery);
                        $hasArticles = $selectArticleFetch && mysqli_num_rows($selectArticleFetch) > 0;

                        // Display subcategories if available
                        if ($hasSubcategories) {
                            echo '<h3>Subcategories</h3>';
                            echo '<ul class="nav flex-column articles-listing">';
                            while ($subcatRow = mysqli_fetch_assoc($subcatResult)) {
                                echo '<li>
                                <a href="subcategory.php?subcatid=' . $subcatRow["SubCategoryId"] . '">' . htmlspecialchars($subcatRow["SubCategory"]) . '</a>
                                    </li>';
                            }
                            echo '</ul>';
                            echo '<hr />';
                        }

                        //         // Display articles if available
                        //         if ($hasArticles) {
                        //             echo '<h3>Articles</h3>';
                        //             echo '<ul class="nav flex-column articles-listing">';
                        //             while ($row = mysqli_fetch_assoc($selectArticleFetch)) {
                        //                 echo '<li>
                        //     <a href="article.php?nid=' . $row["id"] . '">' . htmlspecialchars($row["ArticleTitle"]) . '</a>
                        //   </li>';
                        //             }
                        //             echo '</ul>';
                        //         }

                        // Display message if no subcategories or articles are available
                        if (!$hasSubcategories && !$hasArticles) {
                            echo '<p>No subcategories or articles are available for this category.</p>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <footer>
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    Celaeno HelpDesk
                    <p class="mt-1"><strong><small class="text-muted">Copyright © 2024 , All Right Reserved by <a
                                    href="#">Celaeno Technology</a></small></strong></p>
                </div>

            </div>
        </div>
    </footer>

    <script src="../dist/js/main.js"></script>
</body>

</html>