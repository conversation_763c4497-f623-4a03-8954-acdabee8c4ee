<?php

/**
 * Fetches all users from the 'tblusers' table and 
 * returns an associative array where keys are user IDs and values are user names.
 */
function getAllUsers($conn)
{
    $users = [];
    $sql = "SELECT user_id, name FROM tblusers";
    $result = $conn->query($sql);

    if ($result) {
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $users[$row['user_id']] = $row['name'];
            }
        }
        $result->free(); // Free the result set
    } else {
        // Handle the error appropriately (log it, display a message, etc.)
        error_log("Error fetching users: " . $conn->error);
        // You might want to return an empty array or throw an exception here
    }

    return $users;
}


/**
 * Fetches basic information for a specific group from the 'tblgroups' table.
 *
 * @param mysqli $conn The mysqli database connection object.
 * @param int $group_id The ID of the group to retrieve.
 * @return array|null An associative array containing the group's information
 */
function getGroupById($conn, $group_id)
{
    $sql = "SELECT * FROM tblgroups WHERE id = ?";

    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        error_log("Error preparing statement: " . $conn->error);
        return null;
    }

    $stmt->bind_param("i", $group_id);
    if (!$stmt->execute()) {
        error_log("Error executing query: " . $stmt->error);
        return null;
    }

    $result = $stmt->get_result();
    if ($result->num_rows === 1) {
        return $result->fetch_assoc();
    } elseif ($result->num_rows === 0) {
        return null; // Group not found
    } else {
        // Should ideally not happen with a query on a unique ID
        error_log("Error: Multiple groups found with the same ID: " . $group_id);
        return null;
    }
}




