//---------------------------------------------------
//  LESS Prefixer - https://github.com/JoelSutherland/LESS-Prefixer
//---------------------------------------------------
//
//  All of the CSS3 fun, none of the prefixes!
//
//  As a rule, you can use the CSS properties you
//  would expect just by adding a '.':
//
//  box-shadow => .box-shadow(@args)
//
//  Also, when shorthand is available, arguments are
//  not parameterized. Learn CSS, not LESS Prefixer.
//
//  -------------------------------------------------
//  TABLE OF CONTENTS
//  (*) denotes a syntax-sugar helper
//  -------------------------------------------------
//
//      .animation(@args)
//          .animation-delay(@delay)
//          .animation-direction(@direction)
//          .animation-duration(@duration)
//          .animation-fill-mode(@mode)
//          .animation-iteration-count(@count)
//          .animation-name(@name)
//          .animation-play-state(@state)
//          .animation-timing-function(@function)
//      .background-size(@args)
//      .border-radius(@args)
//      .box-shadow(@args)
//          .inner-shadow(@args) *
//      .box-sizing(@args)
//          .border-box() *
//          .content-box() *
//      .columns(@args)
//          .column-count(@count)
//          .column-gap(@gap)
//          .column-rule(@args)
//          .column-width(@width)
//      .gradient(@default,@start,@stop) *
//          .linear-gradient-top(@default,@color1,@stop1,@color2,@stop2,[@color3,@stop3,@color4,@stop4])*
//          .linear-gradient-left(@default,@color1,@stop1,@color2,@stop2,[@color3,@stop3,@color4,@stop4])*
//      .opacity(@factor)
//      .transform(@args)
//          .transform-origin(@args)
//          .transform-style(@style)
//          .rotate(@deg)
//          .scale(@factor)
//          .translate(@x,@y)
//          .translate3d(@x,@y,@z)
//          .translateHardware(@x,@y) *
//      .text-shadow(@args)
//      .transition(@args)
//          .transition-delay(@delay)
//          .transition-duration(@duration)
//          .transition-property(@property)
//          .transition-timing-function(@function)
//      Flexbox: 
//          .flex-block()
//          .flex-inline()
//              .flex-flow(@direction: row, @wrap: nowrap)
//                  .flex-direction(@direction: row)
//                  .flex-wrap(@wrap: nowrap)
//              .justify-content(@justification)
//              .align-items(@mode)
//              .align-content(@alignment)
//              .flex(@args: none)
//                  .flex-grow(@grow: 1)
//                  .flex-shrink(@shrink: 1)
//                  .flex-basis(@basis: auto)
//              .order(@num: 0)
//              .align-self(@align: auto)
//
//  Credit to LESS Elements for the motivation and
//  to CSS3Please.com for implementation.
//
//  Copyright (c) 2012 Joel Sutherland
//  MIT Licensed:
//  http://www.opensource.org/licenses/mit-license.php
//
//---------------------------------------------------


// Animation

.animation(@args) {
    -webkit-animation: @args;
    -moz-animation: @args;
    -ms-animation: @args;
    -o-animation: @args;
    animation: @args;
}
.animation-delay(@delay) {
    -webkit-animation-delay: @delay;
    -moz-animation-delay: @delay;
    -ms-animation-delay: @delay;
    -o-animation-delay: @delay;
    animation-delay: @delay;
}
.animation-direction(@direction) {
    -webkit-animation-direction: @direction;
    -moz-animation-direction: @direction;
    -ms-animation-direction: @direction;
    -o-animation-direction: @direction;
}
.animation-duration(@duration) {
    -webkit-animation-duration: @duration;
    -moz-animation-duration: @duration;
    -ms-animation-duration: @duration;
    -o-animation-duration: @duration;
}
.animation-fill-mode(@mode) {
    -webkit-animation-fill-mode: @mode;
    -moz-animation-fill-mode: @mode;
    -ms-animation-fill-mode: @mode;
    -o-animation-fill-mode: @mode;
    animation-fill-mode: @mode;
}
.animation-iteration-count(@count) {
    -webkit-animation-iteration-count: @count;
    -moz-animation-iteration-count: @count;
    -ms-animation-iteration-count: @count;
    -o-animation-iteration-count: @count;
    animation-iteration-count: @count;
}
.animation-name(@name) {
    -webkit-animation-name: @name;
    -moz-animation-name: @name;
    -ms-animation-name: @name;
    -o-animation-name: @name;
    animation-name: @name;
}
.animation-play-state(@state) {
    -webkit-animation-play-state: @state;
    -moz-animation-play-state: @state;
    -ms-animation-play-state: @state;
    -o-animation-play-state: @state;
    animation-play-state: @state;
}
.animation-timing-function(@function) {
    -webkit-animation-timing-function: @function;
    -moz-animation-timing-function: @function;
    -ms-animation-timing-function: @function;
    -o-animation-timing-function: @function;
    animation-timing-function: @function;
}


// Background Size

.background-size(@args) {
    -webkit-background-size: @args;
    background-size: @args;
    -moz-background-size: @args;
    -o-background-size: @args;
}


// Border Radius

.border-radius(@args) {
    -webkit-border-radius: @args;
    border-radius: @args;
    -moz-border-radius: @args;

    background-clip: padding-box;
}


// Box Shadows

.box-shadow(@args) {
    -webkit-box-shadow: @args;
    box-shadow: @args;
   -moz-box-shadow: @args;
}
.inner-shadow(@args) {
    .box-shadow(inset @args);
}


// Box Sizing

.box-sizing(@args) {
    -webkit-box-sizing: @args;
    -moz-box-sizing: @args;
    box-sizing: @args;
}
.border-box(){
    .box-sizing(border-box);
}
.content-box(){
    .box-sizing(content-box);
}


// Columns

.columns(@args) {
    -webkit-columns: @args;
    -moz-columns: @args;
    columns: @args;
}
.column-count(@count) {
    -webkit-column-count: @count;
    -moz-column-count: @count;
    column-count: @count;
}
.column-gap(@gap) {
    -webkit-column-gap: @gap;
    -moz-column-gap: @gap;
    column-gap: @gap;
}
.column-width(@width) {
    -webkit-column-width: @width;
    -moz-column-width: @width;
    column-width: @width;
}
.column-rule(@args) {
    -webkit-column-rule: @args;
    -moz-column-rule: @args;
    column-rule: @args;
}


// Gradients

.gradient(@default: #F5F5F5, @start: #EEE, @stop: #FFF) {
    .linear-gradient-top(@default,@start,0%,@stop,100%);
}
.linear-gradient-top(@default,@color1,@stop1,@color2,@stop2) {
    background-color: @default;
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(@stop1, @color1), color-stop(@stop2 @color2));
    background-image: -webkit-linear-gradient(top, @color1 @stop1, @color2 @stop2);
    background-image: -moz-linear-gradient(top, @color1 @stop1, @color2 @stop2);
    background-image: -ms-linear-gradient(top, @color1 @stop1, @color2 @stop2);
    background-image: -o-linear-gradient(top, @color1 @stop1, @color2 @stop2);
    background-image: linear-gradient(top, @color1 @stop1, @color2 @stop2);
}
.linear-gradient-top(@default,@color1,@stop1,@color2,@stop2,@color3,@stop3) {
    background-color: @default;
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(@stop1, @color1), color-stop(@stop2 @color2), color-stop(@stop3 @color3));
    background-image: -webkit-linear-gradient(top, @color1 @stop1, @color2 @stop2, @color3 @stop3);
    background-image: -moz-linear-gradient(top, @color1 @stop1, @color2 @stop2, @color3 @stop3);
    background-image: -ms-linear-gradient(top, @color1 @stop1, @color2 @stop2, @color3 @stop3);
    background-image: -o-linear-gradient(top, @color1 @stop1, @color2 @stop2, @color3 @stop3);
    background-image: linear-gradient(top, @color1 @stop1, @color2 @stop2, @color3 @stop3);
}
.linear-gradient-top(@default,@color1,@stop1,@color2,@stop2,@color3,@stop3,@color4,@stop4) {
    background-color: @default;
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(@stop1, @color1), color-stop(@stop2 @color2), color-stop(@stop3 @color3), color-stop(@stop4 @color4));
    background-image: -webkit-linear-gradient(top, @color1 @stop1, @color2 @stop2, @color3 @stop3, @color4 @stop4);
    background-image: -moz-linear-gradient(top, @color1 @stop1, @color2 @stop2, @color3 @stop3, @color4 @stop4);
    background-image: -ms-linear-gradient(top, @color1 @stop1, @color2 @stop2, @color3 @stop3, @color4 @stop4);
    background-image: -o-linear-gradient(top, @color1 @stop1, @color2 @stop2, @color3 @stop3, @color4 @stop4);
    background-image: linear-gradient(top, @color1 @stop1, @color2 @stop2, @color3 @stop3, @color4 @stop4);
}
.linear-gradient-left(@default,@color1,@stop1,@color2,@stop2) {
    background-color: @default;
    background-image: -webkit-gradient(linear, left top, left top, color-stop(@stop1, @color1), color-stop(@stop2 @color2));
    background-image: -webkit-linear-gradient(left, @color1 @stop1, @color2 @stop2);
    background-image: -moz-linear-gradient(left, @color1 @stop1, @color2 @stop2);
    background-image: -ms-linear-gradient(left, @color1 @stop1, @color2 @stop2);
    background-image: -o-linear-gradient(left, @color1 @stop1, @color2 @stop2);
    background-image: linear-gradient(left, @color1 @stop1, @color2 @stop2);
}
.linear-gradient-left(@default,@color1,@stop1,@color2,@stop2,@color3,@stop3) {
    background-color: @default;
    background-image: -webkit-gradient(linear, left top, left top, color-stop(@stop1, @color1), color-stop(@stop2 @color2), color-stop(@stop3 @color3));
    background-image: -webkit-linear-gradient(left, @color1 @stop1, @color2 @stop2, @color3 @stop3);
    background-image: -moz-linear-gradient(left, @color1 @stop1, @color2 @stop2, @color3 @stop3);
    background-image: -ms-linear-gradient(left, @color1 @stop1, @color2 @stop2, @color3 @stop3);
    background-image: -o-linear-gradient(left, @color1 @stop1, @color2 @stop2, @color3 @stop3);
    background-image: linear-gradient(left, @color1 @stop1, @color2 @stop2, @color3 @stop3);
}
.linear-gradient-left(@default,@color1,@stop1,@color2,@stop2,@color3,@stop3,@color4,@stop4) {
    background-color: @default;
    background-image: -webkit-gradient(linear, left top, left top, color-stop(@stop1, @color1), color-stop(@stop2 @color2), color-stop(@stop3 @color3), color-stop(@stop4 @color4));
    background-image: -webkit-linear-gradient(left, @color1 @stop1, @color2 @stop2, @color3 @stop3, @color4 @stop4);
    background-image: -moz-linear-gradient(left, @color1 @stop1, @color2 @stop2, @color3 @stop3, @color4 @stop4);
    background-image: -ms-linear-gradient(left, @color1 @stop1, @color2 @stop2, @color3 @stop3, @color4 @stop4);
    background-image: -o-linear-gradient(left, @color1 @stop1, @color2 @stop2, @color3 @stop3, @color4 @stop4);
    background-image: linear-gradient(left, @color1 @stop1, @color2 @stop2, @color3 @stop3, @color4 @stop4);
}


// Opacity

.opacity(@factor) {
    @iefactor: @factor*100;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=@{iefactor})";
    filter: ~"alpha(opacity=(@{iefactor}))";
    opacity: @factor;
}


// Text Shadow

.text-shadow(@args) {
    text-shadow: @args;
}


// Transforms

.transform(@args) {
    -webkit-transform: @args;
    -moz-transform: @args;
    -ms-transform: @args;
    -o-transform: @args;
    transform: @args;
}
.transform-origin(@args) {
    -webkit-transform-origin: @args;
    -moz-transform-origin: @args;
    -ms-transform-origin: @args;
    -o-transform-origin: @args;
    transform-origin: @args;
}
.transform-style(@style) {
    -webkit-transform-style: @style;
    -moz-transform-style: @style;
    -ms-transform-style: @style;
    -o-transform-style: @style;
    transform-style: @style;
}
.rotate(@deg:45deg){
    .transform(rotate(@deg));
}
.scale(@factor:.5){
    .transform(scale(@factor));
}
.translate(@x,@y){
    .transform(translate(@x,@y));
}
.translate3d(@x,@y,@z) {
    .transform(translate3d(@x,@y,@z));
}
.translateHardware(@x,@y) {
    .translate(@x,@y);
    -webkit-transform: translate3d(@x,@y,0);
    -moz-transform: translate3d(@x,@y,0);
    -o-transform: translate3d(@x,@y,0);
    -ms-transform: translate3d(@x,@y,0);
    transform: translate3d(@x,@y,0);
}


// Transitions

.transition(@args:200ms) {
    -webkit-transition: @args;
    -moz-transition: @args;
    -o-transition: @args;
    -ms-transition: @args;
    transition: @args;
}
.transition-delay(@delay:0) {
    -webkit-transition-delay: @delay;
    -moz-transition-delay: @delay;
    -o-transition-delay: @delay;
    -ms-transition-delay: @delay;
    transition-delay: @delay;
}
.transition-duration(@duration:200ms) {
    -webkit-transition-duration: @duration;
    -moz-transition-duration: @duration;
    -o-transition-duration: @duration;
    -ms-transition-duration: @duration;
    transition-duration: @duration;
}
.transition-property(@property:all) {
    -webkit-transition-property: @property;
    -moz-transition-property: @property;
    -o-transition-property: @property;
    -ms-transition-property: @property;
    transition-property: @property;
}
.transition-timing-function(@function:ease) {
    -webkit-transition-timing-function: @function;
    -moz-transition-timing-function: @function;
    -o-transition-timing-function: @function;
    -ms-transition-timing-function: @function;
    transition-timing-function: @function;
}

// Flexbox
.flex-block() {
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
}
.flex-inline() {
    display: -ms-inline-flexbox;
    display: -webkit-inline-flex;
    display: inline-flex;
}
.flex-flow(@direction: row, @wrap: nowrap) {
    -moz-flex-flow: @direction @wrap;
    -ms-flex-direction: @direction;
    -ms-flex-wrap: @wrap;
    -webkit-flex-flow: @direction @wrap;
    flex-flow: @direction @wrap;
}
.align-content(@alignment) {
    -moz-align-content: @alignment;
    -ms-align-content: @alignment;
    -webkit-align-content: @alignment;
    align-content: @alignment;
}
.flex-direction(@direction: row) {
    -webkit-flex-direction: @direction;
    -moz-flex-direction: @direction;
    -ms-flex-direction: @direction;
    flex-direction: @direction;
}
.flex-wrap(@wrap: nowrap) {
    -webkit-flex-wrap: @wrap;
    -moz-flex-wrap: @wrap;
    -ms-flex-wrap: @wrap;
    flex-wrap: @wrap;
}
.justify-content(@justification) {
    -webkit-box-pack: @justification;
    -moz-box-pack: @justification;
    -ms-flex-pack: @justification;
    -webkit-justify-content: @justification;
    justify-content: @justification;
}
.align-items(@mode) {
    -webkit-box-align: @mode;
    -moz-box-align: @mode;
    -ms-flex-align: @mode;
    -webkit-align-items: @mode;
    align-items: @mode;
}
.flex(@args: none) {
    -webkit-flex: @args;
    -moz-flex: @args;
    -ms-flex: @args;
    -webkit-flex: @args;
    flex: @args;

}
.order(@num: 0) {
    -webkit-box-ordinal-group: @order;
    -moz-box-ordinal-group: @order;
    -ms-box-ordinal-group: @order;
    -ms-flex-order: @order;
    -webkit-order: @order;
    -moz-order: @order;
    -ms-order: @order;
    order: @order;
}
.flex-grow(@grow: 1) {
    -webkit-flex-grow: @grow;
    -moz-flex-grow: @grow;
    -ms-flex-grow: @grow;
    -webkit-flex-grow: @grow;
    flex-grow: @grow;
}
.flex-shrink(@shrink: 1) {
    -webkit-flex-shrink: @shrink;
    -moz-flex-shrink: @shrink;
    -ms-flex-shrink: @shrink;
    -webkit-flex-shrink: @shrink;
    flex-shrink: @shrink;
}
.flex-basis(@basis: auto) {
    -webkit-flex-basis: @basis;
    -moz-flex-basis: @basis;
    -ms-flex-basis: @basis;
    -webkit-flex-basis: @basis;
    flex-basis: @basis;
}
.align-self(@align: auto) {
    -webkit-align-self: @align;
    -moz-align-self: @align;
    -ms-align-self: @align;
    -webkit-align-self: @align;
    align-self: @align;
}