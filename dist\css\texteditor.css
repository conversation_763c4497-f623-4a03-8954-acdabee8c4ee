/* Editor Css */
#code-editor {
  border: 1px solid #d1d1d1;
  max-width: 100%;
  margin: 0px auto;
}

.editor-block-controls {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  background: #fff;
  border-bottom: 2px solid #f1f1ec;
}

.editor-block-controls > div > button.command {
  outline: none;
  background: transparent;
  color: black;
  cursor: pointer;
  border: 1px solid #e1e1e1;
  transition: 0.2s;
  border-radius: 3px;
  padding: 0 12px;
  font-size: small;
}

.editor-block-controls > div > button.command:hover {
  color: #007474;
}

#editor-block-content {
  min-height: 200px;
  /* width: 100%; */
  height: auto;
  padding: 12px;
  overflow: auto;
  font-family: Helvetica, sans-serif;
  font-size: 12px;
  /* border: 2px solid #e1e1e1; */
  /* border-radius: 0 0 3px 3px; */
  background: #f1f1ec;
  outline: none;
}

#editor-block-content > blockquote {
  border-left: 2px solid black;
  padding: 5px;
  background: #1111;
  font-style: italic;
}

.wsy-command {
  padding: 3px;
  border-radius: 3px;
  font-weight: bold;
  outline: none;
  cursor: pointer;
  transition: 0.2s;
  font-size: small;
}

.command:hover {
  color: #007474 !important;
}
/* End of Editor css */
