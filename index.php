<?php
session_start();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Celaeno HelpDesk</title>

    <link rel="shortcut icon" href="/dist/images/favicon.ico">

    <script src="https://kit.fontawesome.com/1b5fdf4bb2.js" crossorigin="anonymous"></script>
    <link href="dist/css/main.css" rel="stylesheet">
</head>

<body>
    <section class="jumbotron jumbotron-fluid text-center">
        <nav class="ct-nav" style="">
            <div class="navbar navbar-dark navbar-static-top navbar-expand-lg">
                <a href="#" class="navbar-brand">
                    <img src="./dist/images/logo.jpg" alt="" srcset="">
                </a>
                <div class="ct-nav-list">
                    <a href="index.php" class="">Home</a>
                    <a href="knowledgebase.php" class="">Knowledge Base</a>
                </div>
                <div class="ct-head-btn-01">
                    <?php
                    if (isset($_SESSION['user'])) {
                        echo '<a href="./logout.php"><button class="ct-help-btn">Log out</button></a>';
                    } else {
                        echo '<a href="./login.php"><button class="ct-help-btn">Log In</button></a>';
                    }
                    ?>

                </div>
            </div>
        </nav>

        <div class="cover"></div>

        <div class="container">
            <h1 class="jumbotron-heading">Welcome to Celaeno Assist</h1>

            <div class="row justify-content-center text-center">
                <div class="col-md-10">
                    <form id="searchForm" action="/" class="search-form" method="GET">
                        <div class="input-group search">
                            <input type="text" name="q" class="search-field" aria-label="Search field"
                                placeholder="Search anything here..." aria-required="true" required
                                autoComplete="off" />
                            <button class="input-group-addon" aria-label="Search">
                                <i class="fa fa-search">&nbsp;</i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>


    <?php include 'Supportplan.php'; ?>

    <?php include 'pricing.php'; ?>


    <div class="contact-page-container">
        <div class="content">
            <div class="sp_support_container1">
                <div class="sp_support_cell talktous">
                    <div class="sp_support_agent"></div>
                    <div>
                        <div class="sp_support_title">
                            Talk to us!
                        </div>
                        <div class="sp_support_description">
                            Get in touch with our support team via email or call.
                        </div>
                        <div>
                            <button class="sp_support_link ContactZohoSupport" onclick="openPopup()">
                                Contact Celaeno Technology
                            </button>
                        </div>
                    </div>
                </div>

                <!-- <div class="sp_support_cell gotaquestion">
                    <div class="sp_support_question"></div>
                    <div>
                        <div class="sp_support_title">
                            Got a Question?
                        </div>
                        <div class="sp_support_description">
                            Get answers from the experts in the Zoho Support community.
                        </div>
                        </br>
                        <div>
                            <a class="sp_support_link" href="https://help.zoho.com/portal/community">
                                Visit our forum
                            </a>
                        </div>
                    </div>
                </div> -->

                <div class="sp_support_cell stillcannotfind">
                    <div class="sp_support_ticket"></div>
                    <div>
                        <div class="sp_support_title">
                            Still can't find what you're looking for?
                        </div>
                        <div class="sp_support_description">
                            Submit a request and we'll get back to you soon!
                        </div>
                        <div>
                            <a class="sp_support_link" href="./knowledgebase.php">
                                Submit a ticket
                            </a>
                        </div>
                    </div>
                </div>


            </div>
        </div>

        <?php include 'popup.php'; ?>
    </div>


    <?php include 'footer.php'; ?>

    <script src="./dist/js/main.js"></script>
    <script>
        function openPopup() {
            document.querySelector('.popup-overlay').style.display = 'block';
            document.querySelector('.popup-form').style.display = 'block';
        }

        function closePopup() {
            document.querySelector('.popup-overlay').style.display = 'none';
            document.querySelector('.popup-form').style.display = 'none';
        }
        // Wait for the DOM content to be fully loaded
        document.addEventListener("DOMContentLoaded", function () {
            // Get the toggle button and the table
            const toggleBtn = document.getElementById("toggle-btn");
            const comparisonTable = document.getElementById("comparison-table");

            // Add an event listener to the button
            toggleBtn.addEventListener("click", function () {
                // Toggle the 'hidden' class on the comparison table
                comparisonTable.classList.toggle("hidden");

                // Change the arrow direction when the table is toggled
                const arrow = toggleBtn.querySelector("span");
                if (comparisonTable.classList.contains("hidden")) {
                    arrow.innerHTML = "&#9650;"; // Upward arrow (table hidden)
                } else {
                    arrow.innerHTML = "&#9660;"; // Downward arrow (table visible)
                }
            });
        });
    </script>
</body>

</html>