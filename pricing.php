<?php
// You can include additional logic if needed, such as data fetching from a database or managing user sessions.
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Compare Plans</title>
  <style>
    /* Pricing Table Styles */
    .pricing-table {
      max-width: 1000px;
      margin: 40px auto;
      background: #d9d6d1;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
    }

    .pricing-header {
      text-align: center;
      padding: 20px;
      font-weight: bold;
      font-size: 22px;
    }

    .classic {
      background-color: #ffcccb;
      border-top: 4px solid #ff0000;
    }

    .premium {
      background-color: #cce5ff;
      border-top: 4px solid #007bff;
    }

    .enterprise {
      background-color: #d4edda;
      border-top: 4px solid #28a745;
    }

    .pricing-table th,
    .pricing-table td {
      text-align: center;
      padding: 15px;
      border-bottom: 1px solid #ddd;
      font-size: 16px;
    }

    .checkmark {
      color: green;
      font-weight: bold;
    }

    .service-column {
      font-weight: bold;
      text-align: left;
      background-color: #f8f9fa;
    }

    .hidden {
      display: none;
    }

    /* Compare Button */
    .compare-title {
      font-size: 24px;
      font-weight: bold;
      color: #333;
      margin-bottom: 10px;
    }

    .arrow-btn {
      background: #007474;
      color: #ffffff;
      border: none;
      padding: 10px 20px;
      font-size: 18px;
      border-radius: 5px;
      cursor: pointer;
      transition: background 0.3s;
      display: inline;
    }

    .arrow-btn:hover {
      background: #0056b3;
    }
  </style>
</head>

<body>



  <div id="comparison-table" class="container mt-4 hidden">
    <table class="pricing-table table table-bordered text-center">
      <thead>
        <tr>
          <th class="service-column">Services</th>
          <th class="classic">Classic<br /><small>Available by default with a paid subscription of any Celaeno Technology product.</small></th>
          <th class="premium">Premium<br /><small>Costs 20% of your Celaeno Technology subscription fee</small></th>
          <th class="enterprise">Enterprise<br /><small>Costs 25% of your annual Celaeno Technology subscription fee</small></th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td class="service-column">Knowledge Base</td>
          <td>&#10004;</td>
          <td>&#10004;</td>
          <td>&#10004;</td>
        </tr>
        <tr>
          <td class="service-column">Community Forums</td>
          <td>&#10004;</td>
          <td>&#10004;</td>
          <td>&#10004;</td>
        </tr>
        <tr>
          <td class="service-column">Self-service Portal</td>
          <td>&#10004;</td>
          <td>&#10004;</td>
          <td>&#10004;</td>
        </tr>
        <tr>
          <td class="service-column">Email Support</td>
          <td>&#10004;</td>
          <td>&#10004;</td>
          <td>&#10004;</td>
        </tr>
        <tr>
          <td class="service-column">Live Chat Support</td>
          <td>8 hours, 5 days/week</td>
          <td>24 hours, 5 days/week</td>
          <td>24 hours, 7 days/week</td>
        </tr>
        <tr>
          <td class="service-column">Phone Support (Toll-free)</td>
          <td>8 hours, 5 days/week</td>
          <td>24 hours, 5 days/week</td>
          <td>24 hours, 7 days/week</td>
        </tr>
        <tr>
          <td class="service-column">Remote Assistance</td>
          <td>&#10004;</td>
          <td>&#10004;</td>
          <td>&#10004;</td>
        </tr>
        <tr>
          <td class="service-column">Acknowledgement & Initial Response Time</td>
          <td>8 Hours</td>
          <td>3 Hours</td>
          <td>1 Hour</td>
        </tr>
        <tr>
          <td class="service-column">Dedicated Technical Account Manager</td>
          <td>-</td>
          <td>&#10004;</td>
          <td>&#10004;</td>
        </tr>
        <tr>
          <td class="service-column">Quarterly Report (Feature Usage Reports + Feature Recommendations)</td>
          <td>-</td>
          <td>-</td>
          <td>&#10004;</td>
        </tr>
      </tbody>
    </table>
  </div>
  </section>

  <script src="script.js"></script>

</body>

</html>