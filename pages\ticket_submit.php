<?php
// Enable error reporting for development
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

function generateTicketId($conn)
{
    // Get the last ticket ID
    $sql = "SELECT id FROM tickets ORDER BY id DESC LIMIT 1";
    $result = $conn->query($sql);

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $lastId = $row['id'];
        // Extract number and increment
        $number = (int) substr($lastId, 4); // Assuming format TICK0001
        $newNumber = $number + 1;
    } else {
        $newNumber = 1; // Start with 1 if no tickets exist
    }

    // Format with prefix and padded number
    return "TICK" . str_pad($newNumber, 4, "0", STR_PAD_LEFT);
}

// Usage example:


if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Retrieve form data
    $source = isset($_POST['source']) ? $_POST['source'] : null;
    $first_name = isset($_POST['first_name']) ? $_POST['first_name'] : null;
    $last_name = isset($_POST['last_name']) ? $_POST['last_name'] : null;
    $email = isset($_POST['email']) ? $_POST['email'] : null;
    $products = isset($_POST['products']) ? $_POST['products'] : null;
    $subject = isset($_POST['subject']) ? $_POST['subject'] : null;
    $phone = isset($_POST['phone']) ? $_POST['phone'] : null;
    $texteditor = isset($_POST['texteditor']) ? $_POST['texteditor'] : null;
    $priority = isset($_POST['priority']) ? $_POST['priority'] : null;
    $request_type = isset($_POST['request_type']) ? $_POST['request_type'] : null;
    $channel = isset($_POST['channel']) ? $_POST['channel'] : null;
    

    // Handle file upload
    $attachmentPath = null; // Initialize to null
    if (isset($_FILES['attachment']) && $_FILES['attachment']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = '../uploads/'; // Create an 'uploads' directory
        $uploadFile = $uploadDir . basename($_FILES['attachment']['name']);

        if (move_uploaded_file($_FILES['attachment']['tmp_name'], $uploadFile)) {
            $attachmentPath = $uploadFile;
        } else {
            // Handle upload error
            echo "File upload failed.";
            exit;
        }
    }

    // Database conection
    include '../admin/includes/config.php'; // Include your database conection

    $newTicketId = generateTicketId($con);
    try {
        $stmt = $con->prepare("INSERT INTO tickets (id, source, first_name, last_name, email, products, subject, phone, description, priority, request_type, attachment, channel) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("sssssssssssss", $newTicketId, $source, $first_name, $last_name, $email, $products, $subject, $phone, $texteditor, $priority, $request_type, $attachmentPath, $channel);
        $stmt->execute();

        if ($stmt->affected_rows > 0) {
            echo "<script>alert('Ticket submitted successfully.')</script>";
            header("Location: ../home.php");
        } else {
            echo "<script>alert('Failed to submit ticket.')</script>";
            header("Location: ../home.php");
        }

        $stmt->close();
        $con->close();

    } catch (Exception $e) {
        echo "Database error: " . $e->getMessage();
        header("Location: ../home.php");
    }
} else {
    echo "<script>alert('Invalid request.')</script>";
    header("Location: ../home.php");
}
?>