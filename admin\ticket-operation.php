<?php
session_start();
require_once("./includes/config.php");

require_once("./utils/groups-functions.php");
$user_map = getAllUsers($con);
// Function to add ticket history
function addTicketHistory($con, $ticketId, $action, $performedBy, $details = '')
{
    $stmt = $con->prepare("
        INSERT INTO ticket_history (ticket_id, action, performed_by, details) 
        VALUES (?, ?, ?, ?)
    ");
    if ($stmt) {
        $stmt->bind_param("ssss", $ticketId, $action, $performedBy, $details);
        $stmt->execute();
        $stmt->close();
        return true;
    } else {
        error_log("Error adding history: " . $con->error);
        return false;
    }
}

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize and retrieve form data
    $ticketId = isset($_POST['ticketId']) ? $_POST['ticketId'] : 0;
    $status = isset($_POST['status']) ? $_POST['status'] : '';
    $priority = isset($_POST['priority']) ? $_POST['priority'] : '';
    $assignee = isset($_POST['assignee']) ? $_POST['assignee'] : '';
    $dueDate = isset($_POST['due_date']) ? $_POST['due_date'] : null;

    // Validate required fields
    if ($ticketId == 0) {
        die("Error: Required fields (Ticket ID) are missing.");
    }

    // Get current ticket data for comparison
    $currentStmt = $con->prepare("SELECT status, priority, assignee, due_date FROM tickets WHERE id = ?");
    $currentStmt->bind_param("s", $ticketId);
    $currentStmt->execute();
    $currentResult = $currentStmt->get_result();
    $currentTicket = $currentResult->fetch_assoc();
    $currentStmt->close();

    // Prepare the SQL update query
    $sql = "UPDATE tickets SET 
            status = ?, 
            priority = ?, 
            assignee = ?, 
            due_date = ? 
            WHERE id = ?";

    $stmt = $con->prepare($sql);
    if (!$stmt) {
        die("Prepare failed: " . $con->error);
    }

    // Bind parameters
    $stmt->bind_param("sssss", $status, $priority, $assignee, $dueDate, $ticketId);

    // Execute the query
    if ($stmt->execute()) {
        // Get current user info (assuming you have this in session)
        $performedBy = isset($_SESSION['logged_in_user']["id"]) ? $user_map[$_SESSION['logged_in_user']["id"]] : "System";

        // Log changes to history
        if ($currentTicket['status'] !== $status) {
            addTicketHistory(
                $con,
                $ticketId,
                "Status Updated",
                $performedBy,
                "Changed from '{$currentTicket['status']}' to '$status'"
            );
        }
        if ($currentTicket['priority'] !== $priority) {
            addTicketHistory(
                $con,
                $ticketId,
                "Priority Updated",
                $performedBy,
                "Changed from '{$currentTicket['priority']}' to '$priority'"
            );
        }
        // Only check assignee if it’s explicitly provided in the form
        if (isset($_POST['assignee'])) {
            $assigneeInt = (int) $assignee; // Cast POST input to integer
            $currentAssignee = $currentTicket['assignee'] !== null ? (string) $currentTicket['assignee'] : ''; // Normalize current assignee

            // Compare only if there's an actual change
            if ($currentAssignee !== (string) $assigneeInt) {
                $oldAssigneeName = $currentTicket['assignee'] !== null && isset($user_map[$currentTicket['assignee']])
                    ? $user_map[$currentTicket['assignee']]
                    : 'Unassigned';
                $newAssigneeName = isset($user_map[$assigneeInt])
                    ? $user_map[$assigneeInt]
                    : 'Unassigned';

                // Only log if the names differ (avoids logging type-only differences)
                if ($oldAssigneeName !== $newAssigneeName) {
                    addTicketHistory(
                        $con,
                        $ticketId,
                        "Assignee Updated",
                        $performedBy,
                        "Changed from '$oldAssigneeName' to '$newAssigneeName'"
                    );
                }
            }
        }
        if ($currentTicket['due_date'] !== $dueDate) {
            addTicketHistory(
                $con,
                $ticketId,
                "Due Date Updated",
                $performedBy,
                "Changed from '{$currentTicket['due_date']}' to '$dueDate'"
            );
        }

        // Success: Redirect back to tickets page
        $stmt->close();
        header("Location: ./ticket-edit.php?ticketId=" . $ticketId);
        exit();
    } else {
        // Error: Show error message
        $stmt->close();
        die("Error updating ticket: " . $stmt->error);
    }
} else {
    // If not a POST request, redirect or show error
    die("Invalid request method.");
}
?>