<?php
session_start();
include('includes/config.php');

// Enable error reporting for debugging (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (empty($_SESSION['login'])) {
    header('Location: index.php');
    exit();
}

// Array to define the class for each task status
$statuses = [
    "New" => "new",
    "In Progress" => "inprogress",
    "Waiting on Customer" => "waitingoncustomer",
    "Hold" => "hold",
    "Closed" => "closed",
    "Resolved" => "resolved",
    "Low" => "low",
    "High" => "high",
    "Medium" => "medium",
];

?>

<!DOCTYPE html>
<html>

<head>
    <title>Celaeno Technology | Manage Articles</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/knowledge-base.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="./assets/css/texteditor.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
        integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        .page-title-box .page-title {
            margin-bottom: 22px;
            float: left;
        }

        .ticket-table-outer {
            width: 100%;
            overflow-x: auto;
        }

        .ticket-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-family: Arial, sans-serif;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            overflow: auto;
        }

        .ticket-table th,
        .ticket-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .ticket-table th {
            background-color: #007474;
            color: white;
            font-weight: 600;
        }

        .ticket-table td {
            color: #333;
        }

        .ticket-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .ticket-table tr:hover {
            background-color: #f1f3f5;
            transition: background-color 0.2s ease;
        }

        .status-badge {
            width: fit-content;
            padding: 8px 12px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            font-size: 14px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 6px;
            white-space: nowrap;
        }

        .status-badge.urgent {
            color: #c12831;
            background-color: #f0d2d7;
        }

        .status-badge.high {
            color: #db4522;
            background-color: #f1d9cc;
        }

        .status-badge.medium {
            color: #b08106;
            background-color: #f5edce;
        }

        .status-badge.low {
            color: #39b126;
            background-color: #c7ebc7;
        }

        .status-badge img {
            width: 18px;
            height: 18px;
        }

        .status-badge.new {
            color: #398eb7;
            background-color: #d0e7f8;
        }

        .status-badge.inprogress {
            color: #e3751c;
            background-color: #f7e8da;
        }

        .status-badge.onhold {
            color: #d7a229;
            background-color: #fff2de;
        }

        .status-badge.canceled {
            color: #d82e2e;
            background-color: #f9c9c9;
        }

        .status-badge.completed {
            color: #007070;
            background-color: #c1e7e7;
        }

        .status-badge.waitingonclient {
            color: #4c4848;
            background-color: #d5d5d5;
        }

        .status-badge.done {
            color: #6e9f37;
            background-color: #e5f2d3;
        }

        .status-badge.testing {
            color: #9e6dcc;
            background-color: #ebddf4;
        }

        .status-badge.readytotest {
            color: #3b71cf;
            background-color: #e0ecfd;
        }

        .status-badge.readytodeploy {
            color: #38917e;
            background-color: #d3f1f1;
        }

        .status {
            width: 8rem;
            display: flex;
            justify-content: center;
        }

        .priority {
            width: 8rem;
            display: flex;
            justify-content: center;
        }



        /* Responsive design */
        @media (max-width: 600px) {
            .ticket-table {
                display: block;
                overflow-x: auto;
            }
        }
    </style>


</head>

<body class="fixed-left">
    <div id="wrapper">
        <?php include('includes/topheader.php'); ?>
        <?php include('includes/leftsidebar.php'); ?>

        <div class="content-page">
            <div class="content">
                <div class="container">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="page-title-box">
                                <ol class="breadcrumb p-0 m-0">
                                    <li>
                                        <a href="./ticket.php">Ticket</a>
                                    </li>

                                    <li class="active">
                                        Ticket Create
                                    </li>
                                </ol>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>


                    <div class="row">
                        <div class="col-md-10 col-md-offset-1">
                            <div class="p-6">
                                <form name="createticket" method="POST" enctype="multipart/form-data" id="ticketForm">
                                    <!-- Ticket Subject -->
                                    <div class="form-group m-b-20">
                                        <label for="subject">Ticket Subject</label>
                                        <input type="text" class="form-control" id="subject" name="subject"
                                            placeholder="Enter subject" required />
                                    </div>

                                    <!-- Assign To -->
                                    <div class="form-group m-b-20">
                                        <label for="assignto">Assign To</label>
                                        <select class="form-control" name="assignto" id="assignto" required>
                                            <option value="">Select Assignee</option>

                                        </select>
                                    </div>

                                    <!-- Status -->
                                    <div class="form-group m-b-20">
                                        <label for="status">Status</label>
                                        <select class="form-control" name="status" id="status" required>
                                            <option value="">Select Status</option>
                                            <option value="open">Open</option>
                                            <option value="inprogress">In Progress</option>
                                            <option value="closed">Closed</option>
                                        </select>
                                    </div>

                                    <!-- Priority -->
                                    <div class="form-group m-b-20">
                                        <label for="priority">Priority</label>
                                        <select class="form-control" name="priority" id="priority" required>
                                            <option value="">Select Priority</option>
                                            <option value="low">Low</option>
                                            <option value="medium">Medium</option>
                                            <option value="high">High</option>
                                        </select>
                                    </div>

                                    <!-- Channel -->
                                    <div class="form-group m-b-20">
                                        <label for="channel">Channel</label>
                                        <select class="form-control" name="channel" id="channel" required>
                                            <option value="">Select Channel</option>
                                            <option value="email">Email</option>
                                            <option value="phone">Phone</option>
                                            <option value="web">Web</option>
                                            <option value="chat">Chat</option>
                                        </select>
                                    </div>

                                    <!-- Product -->
                                    <div class="form-group m-b-20">
                                        <label for="product">Product</label>
                                        <select class="form-control" name="product" id="product" required>
                                            <option value="">Select Product</option>
                                            <?php
                                            $ret = mysqli_query($con, "SELECT id, ProductName FROM tblarea WHERE Is_Active=1");
                                            while ($result = mysqli_fetch_array($ret)) {
                                                ?>
                                                <option value="<?php echo htmlentities($result['id']); ?>">
                                                    <?php echo htmlentities($result['ProductName']); ?>
                                                </option>
                                            <?php } ?>
                                        </select>
                                    </div>

                                    <!-- Type -->
                                    <div class="form-group m-b-20">
                                        <label for="type">Type</label>
                                        <select class="form-control" name="type" id="type" required>
                                            <option value="">Select Type</option>
                                            <option value="bug">Bug</option>
                                            <option value="feature">Feature Request</option>
                                            <option value="query">Query</option>
                                        </select>
                                    </div>

                                    <!-- Due Date -->
                                    <div class="form-group m-b-20">
                                        <label for="duedate">Due Date</label>
                                        <input type="date" class="form-control" name="duedate" id="duedate" required />
                                    </div>

                                    <!-- Description -->
                                    <div class="row">
                                        <div class="col-sm-12">

                                            <h4 class="m-b-30 m-t-0 header-title">
                                                <b>Ticket Description</b>
                                            </h4>
                                            <div class="form-input-des item6" style="margin-bottom: 20px;">
                                                <input type="hidden" name="newMessage"
                                                    id="texteditor-hidden-Ticket-32ik45" value="">
                                                <div class="wsyig" id="wsyigTicket-32ik45"></div>
                                            </div>

                                        </div>
                                    </div>

                                    <!-- Submit Buttons -->
                                    <button type="submit" name="submit" class="btn btn-success waves-effect waves-light"
                                        onclick="saveTicket()">
                                        Create Ticket
                                    </button>
                                    <button type="reset" class="btn btn-danger waves-effect waves-light"
                                        onclick="discardTicket()">
                                        Cancel
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>


                </div>

            </div>
        </div>
    </div>
    </div>

    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/detect.js"></script>
    <script src="assets/js/fastclick.js"></script>
    <script src="admin/assets/js/jquery.blockUI.js"></script>

    <script src="admin/assets/js/jquery.slimscroll.js"></script>
    <script src="admin/assets/js/jquery.scrollTo.min.js"></script>
    <script src="admin/assets/js/jquery.core.js"></script>
    <script src="admin/assets/js/jquery.app.js"></script>


    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- <script>
        $(document).ready(function() {
            $(".btn-outline-secondary").click(function() {
                $(".cards-container").toggle(); // Toggles visibility
            });
        });
        document.addEventListener("DOMContentLoaded", function() {
            document.querySelector(".btn-outline-first").addEventListener("click", function() {
                document.querySelector(".cards-container").classList.toggle("list-view");
            });
        });
    </script> -->

    <script src="../dist/js/TextEditor.js"></script>
    <script>
        new TextEditor("wsyigTicket-32ik45", "texteditor-hidden-Ticket-32ik45");
        new TextEditor("wsyigResolution-32ik45", "texteditor-hidden-resolution-32ik45");
    </script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function () {
            // List view button
            $("#list-view-btn").click(function () {
                $("#view_mode").val("list"); // Update hidden input
                $(".cards-container").addClass("list-view"); // Apply list view
            });

            // Card view button
            $("#card-view-btn").click(function () {
                $("#view_mode").val("card"); // Update hidden input
                $(".cards-container").removeClass("list-view"); // Apply card view
            });

            // Apply the initial view based on the hidden input value
            if ($("#view_mode").val() === "list") {
                $(".cards-container").addClass("list-view");
            } else {
                $(".cards-container").removeClass("list-view");
            }
        });
    </script>



</body>

</html>