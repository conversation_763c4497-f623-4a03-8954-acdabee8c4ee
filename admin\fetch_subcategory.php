<?php
// include('includes/config.php');
// if (isset($_POST['category_id']) && $_POST['category_id'] != '') {
//     $category_id = intval($_POST['category_id']);
//     $query = mysqli_query($con, "SELECT SubCategoryId, SubcategoryName FROM tblsubcategory WHERE CategoryId='$category_id' AND Is_Active=1");
//     if (mysqli_num_rows($query) > 0) {
//         echo '<option value="">Select Subcategory</option>';
//         while ($row = mysqli_fetch_array($query)) {
//             echo '<option value="' . htmlentities($row['SubCategoryId']) . '">' . htmlentities($row['SubcategoryName']) . '</option>';
//         }
//     } else {
//         echo '<option value="">No Subcategories Found</option>';
//     }
// } else {
//     echo '<option value="">Select Subcategory</option>';
// }


include('includes/config.php');
if (isset($_POST['category_id']) && $_POST['category_id'] != '') {
    $category_id = intval($_POST['category_id']);
    $query = mysqli_query($con, "SELECT SubCategoryId, Subcategory FROM tblsubcategory WHERE CategoryId='$category_id' AND Is_Active=1");
    if (mysqli_num_rows($query) > 0) {
        echo '<option value="">Select Subcategory</option>';
        while ($row = mysqli_fetch_array($query)) {
            echo '<option value="' . htmlentities($row['SubCategoryId']) . '">' . htmlentities($row['Subcategory']) . '</option>';
        }
    } else {
        echo '<option value="">No Subcategories Found</option>';
    }
} else {
    echo '<option value="">Select Subcategory</option>';
}


/*for edit-article form fetchsub category*/

if (!empty($_POST["catid"])) {
    $cat_id = intval($_POST['catid']);
    $query = mysqli_query($con, "SELECT CategoryId, Category FROM tblcategory WHERE ParentId = '$cat_id' AND Is_Active=1");
?>
    <option value="">Select Subcategory</option>
    <?php
    while ($row = mysqli_fetch_array($query)) {
    ?>
        <option value="<?php echo htmlentities($row['CategoryId']); ?>"><?php echo htmlentities($row['Category']); ?></option>
<?php
    }
}
?>