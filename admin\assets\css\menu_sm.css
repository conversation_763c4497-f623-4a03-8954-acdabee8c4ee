/*
File: Menu
*/
.topbar {
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 999;
}
.topbar .topbar-left {
  background: #36404e;
  float: left;
  text-align: center;
  height: 70px;
  position: relative;
  width: 160px;
  z-index: 1;
}
.navbar-default {
  background-color: #f3f3f3;
  border-radius: 0;
  border: none;
  margin-bottom: 0;
  padding: 0 20px;
}
.navbar-default .navbar-left li a.menu-item {
  padding: 0 15px;
  line-height: 68px;
}
.logo {
  color: #ffffff !important;
  font-size: 24px;
  text-transform: uppercase;
  font-family: 'Hind Madurai', sans-serif;
  font-weight: 600;
  letter-spacing: 1px;
  line-height: 70px;
}
.logo i {
  display: none;
}
.logo span span {
  color: #7fc1fc;
}
.user-box a.user-link {
  padding-top: 17px !important;
  padding-bottom: 17px !important;
}
.user-box .user-img {
  position: relative;
  height: 36px;
  width: 36px;
  margin: 0px auto;
}
.navbar-default .right-menu-item {
  height: 36px;
  width: 36px;
  padding: 0;
  font-size: 18px;
  border: 2px solid #ccc !important;
  line-height: 35px;
  text-align: center;
  border-radius: 50%;
  margin: 17px 5px;
}
.navbar-default .right-menu-item .badge {
  position: absolute;
  top: -8px;
  right: 0px;
}
/* Notification */
.notify-list h5 {
  margin: 0 0 5px 0;
  padding: 10px;
  background-color: #f3f3f3;
  text-align: center;
}
.notify-list .all-msgs a {
  color: #313a46;
  padding: 6px 10px;
  display: block;
}
.side-menu {
  width: 160px;
  padding-top: 10px;
  z-index: 10;
  background: #36404e;
  bottom: 50px;
  margin-top: 0;
  padding-bottom: 70px;
  position: fixed;
  top: 0;
  box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
}
.side-menu .waves-effect .waves-ripple {
  background-color: rgba(127, 193, 252, 0.4);
}
.side-menu.left {
  position: absolute;
  top: 60px;
  bottom: 0;
}
body.fixed-left .side-menu.left {
  bottom: 50px;
  margin-bottom: -70px;
  margin-top: 0;
  padding-bottom: 70px;
  position: fixed;
}
.content-page {
  margin-left: 160px;
  overflow: hidden;
}
.content-page .content {
  padding: 0 5px 10px 5px;
  margin-top: 70px;
}
.button-menu-mobile {
  background: transparent;
  border: none;
  color: #313a46;
  padding: 0 20px;
  display: inline-block;
}
.button-menu-mobile i {
  font-size: 24px;
  line-height: 70px;
}
.button-menu-mobile:hover {
  color: #7fc1fc;
}
.sidebar-inner {
  height: 100%;
}
#sidebar-menu,
#sidebar-menu ul,
#sidebar-menu li,
#sidebar-menu a {
  border: 0;
  font-weight: normal;
  line-height: 1;
  list-style: none;
  margin: 0;
  padding: 0;
  position: relative;
  text-decoration: none;
}
#sidebar-menu > ul > li {
  margin: 15px 0;
}
#sidebar-menu {
  padding-bottom: 30px;
  width: 100%;
  text-align: center;
}
#sidebar-menu a {
  line-height: 1.3;
}
#sidebar-menu ul ul {
  display: none;
}
#sidebar-menu ul ul li {
  border-top: 0;
}
#sidebar-menu ul ul li.active a {
  color: #7fc1fc;
}
#sidebar-menu ul ul a {
  color: #98a6ad;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  -ms-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
  border-left: 3px solid transparent;
  display: block;
  padding: 10px 20px;
  font-size: 13px;
}
#sidebar-menu ul ul a:hover {
  color: #ffffff;
}
#sidebar-menu ul ul a i {
  margin-right: 5px;
}
#sidebar-menu ul ul ul a {
  padding-left: 80px;
}
#sidebar-menu .label {
  display: none;
}
#sidebar-menu .subdrop {
  color: #ffffff !important;
  background-color: #313a46;
}
#sidebar-menu > ul > li > a {
  color: #98a6ad;
  display: block;
  padding: 12px 20px;
  margin: 2px 0;
}
#sidebar-menu > ul > li > a:hover {
  color: #ffffff;
  text-decoration: none;
  background-color: #313a46;
}
#sidebar-menu > ul > li > a > span {
  vertical-align: middle;
}
#sidebar-menu ul li a i {
  display: block;
  font-size: 22px;
  line-height: 18px;
  text-align: center;
  margin-bottom: 10px;
  vertical-align: middle;
}
#sidebar-menu ul li a .drop-arrow {
  float: right;
}
#sidebar-menu ul li a .drop-arrow i {
  margin-right: 0;
}
#sidebar-menu > ul > li > a.active {
  color: #ffffff !important;
  background-color: #313a46;
}
#sidebar-menu > ul > li > a.active i {
  color: #7fc1fc;
}
.menu-title {
  display: none;
}
/* Help Box */
.help-box {
  display: none;
}
.footer {
  left: 160px;
}
#wrapper.enlarged .menu-title,
#wrapper.enlarged .menu-arrow,
#wrapper.enlarged .help-box {
  display: none !important;
}
#wrapper.enlarged #sidebar-menu {
  text-align: left;
}
#wrapper.enlarged #sidebar-menu ul ul {
  margin-top: -2px;
  padding-bottom: 5px;
  padding-top: 5px;
  z-index: 9999;
  background-color: #36404e;
}
#wrapper.enlarged #sidebar-menu > ul > li {
  margin: 4px 0;
}
#wrapper.enlarged .left.side-menu {
  width: 70px;
  z-index: 5;
}
#wrapper.enlarged .left.side-menu #sidebar-menu > ul > li > a {
  padding: 15px 20px;
}
#wrapper.enlarged .left.side-menu #sidebar-menu > ul > li > a:hover {
  color: #ffffff !important;
  background-color: #313a46;
}
#wrapper.enlarged .left.side-menu #sidebar-menu > ul > li > a:active {
  color: #ffffff !important;
  background-color: #313a46;
}
#wrapper.enlarged .left.side-menu #sidebar-menu > ul > li > a:focus {
  color: #ffffff !important;
  background-color: #313a46;
}
#wrapper.enlarged .left.side-menu #sidebar-menu > ul > li > a i {
  font-size: 20px;
  float: left;
  margin: 0 25px 0 5px;
}
#wrapper.enlarged .left.side-menu .label {
  display: none !important;
}
#wrapper.enlarged .left.side-menu #sidebar-menu ul > li {
  position: relative;
  white-space: nowrap;
}
#wrapper.enlarged .left.side-menu #sidebar-menu ul > li:hover > a {
  position: relative;
  width: 260px;
  color: #ffffff !important;
  background-color: #313a46;
}
#wrapper.enlarged .left.side-menu #sidebar-menu ul > li:hover > ul {
  display: block;
  left: 70px;
  position: absolute;
  width: 190px;
}
#wrapper.enlarged .left.side-menu #sidebar-menu ul > li:hover > ul a {
  box-shadow: none;
  padding-left: 15px;
  position: relative;
  width: 186px;
  z-index: 6;
}
#wrapper.enlarged .left.side-menu #sidebar-menu ul > li:hover > ul a:hover {
  color: #ffffff;
}
#wrapper.enlarged .left.side-menu #sidebar-menu ul > li:hover a span {
  display: inline;
}
#wrapper.enlarged .left.side-menu #sidebar-menu a.subdrop {
  color: #ffffff !important;
}
#wrapper.enlarged .left.side-menu #sidebar-menu ul > li > ul {
  display: none;
}
#wrapper.enlarged .left.side-menu #sidebar-menu ul ul li:hover > ul {
  display: block;
  left: 190px;
  margin-top: -36px;
  position: absolute;
  width: 190px;
}
#wrapper.enlarged .left.side-menu #sidebar-menu ul ul li > a span.pull-right {
  -ms-transform: rotate(270deg);
  -webkit-transform: rotate(270deg);
  position: absolute;
  right: 20px;
  top: 12px;
  transform: rotate(270deg);
}
#wrapper.enlarged .left.side-menu #sidebar-menu ul ul li.active a {
  color: #ffffff;
}
#wrapper.enlarged .left.side-menu #sidebar-menu ul > li > a span {
  display: none;
  padding-left: 10px;
}
#wrapper.enlarged .left.side-menu .user-details {
  display: none;
}
#wrapper.enlarged .content-page {
  margin-left: 70px;
}
#wrapper.enlarged .footer {
  left: 70px;
}
#wrapper.enlarged .topbar .topbar-left {
  width: 70px !important;
}
#wrapper.enlarged .topbar .topbar-left .logo span {
  display: none;
  opacity: 0;
}
#wrapper.enlarged .topbar .topbar-left .logo i {
  display: block;
  line-height: 70px;
  color: #7fc1fc !important;
}
#wrapper.enlarged #sidebar-menu > ul > li:hover > a.open :after {
  display: none;
}
#wrapper.enlarged #sidebar-menu > ul > li:hover > a.active :after {
  display: none;
}
#wrapper.right-bar-enabled .right-bar {
  right: 0;
}
#wrapper.right-bar-enabled .left-layout {
  left: 0;
}
/* Right sidebar */
.side-bar.right-bar {
  float: right !important;
  right: -266px;
  top: 0px;
}
.side-bar {
  -moz-transition: all 200ms ease-out;
  -webkit-transition: all 200ms ease-out;
  background-color: #ffffff;
  box-shadow: 0 0px 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0px 0 rgba(0, 0, 0, 0.02);
  display: block;
  float: left;
  height: 100%;
  position: fixed;
  transition: all 200ms ease-out;
  width: 240px;
}
.right-bar {
  background: #ffffff !important;
  z-index: 999 !important;
}
.right-bar h4 {
  border-bottom: 1px solid rgba(152, 166, 173, 0.5);
  padding: 4px 10px 10px 18px;
  text-transform: uppercase;
  font-size: 14px;
  letter-spacing: 0.03em;
}
.right-bar .right-bar-toggle {
  float: right;
  line-height: 46px;
  font-size: 20px;
  color: #333;
  padding: 0 10px;
}
.right-bar .setting-list {
  padding: 0 20px 20px 20px;
}
.user-list .user-list-item {
  padding: 10px 12px !important;
  border-bottom: 1px solid #EEEEEE !important;
}
.user-list .user-list-item .avatar {
  float: left;
  margin-right: 5px;
  width: 30px;
  height: 30px;
}
.user-list .user-list-item .avatar img {
  border-radius: 50%;
  width: 100%;
}
.user-list .user-list-item .icon {
  float: left;
  margin-right: 5px;
  height: 30px;
  width: 30px;
  border-radius: 50%;
  text-align: center;
}
.user-list .user-list-item .icon i {
  color: #ffffff;
  line-height: 30px;
  font-size: 16px;
}
.user-list .user-list-item .user-desc {
  margin-left: 40px;
}
.user-list .user-list-item .user-desc span.name {
  color: #313a46;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  width: 100%;
  overflow: hidden;
  font-size: 13px;
}
.user-list .user-list-item .user-desc span.desc {
  color: #98a6ad;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  width: 100%;
  overflow: hidden;
  font-size: 12px;
}
.user-list .user-list-item .user-desc span.time {
  font-size: 11px;
}
/* Seach */
.app-search {
  position: relative;
}
.app-search a {
  position: absolute;
  top: 7px;
  right: 26px;
  color: rgba(152, 166, 173, 0.7);
}
.app-search a:hover {
  color: #98a6ad;
}
.app-search .form-control,
.app-search .form-control:focus {
  border: 1px solid rgba(152, 166, 173, 0.15);
  font-size: 13px;
  height: 34px;
  color: #313a46;
  padding: 7px 40px 7px 20px;
  margin: 19px 12px 0 5px;
  background: rgba(152, 166, 173, 0.1);
  box-shadow: none;
  border-radius: 30px;
  width: 190px;
}
/* Page titles */
.page-title {
  font-size: 20px;
  margin-bottom: 0;
  margin-top: 0;
  font-weight: 600;
}
.page-header {
  border-bottom: 1px solid #DBDDDE;
}
.header-title {
  font-size: 16px;
  line-height: 16px;
  margin-bottom: 8px;
  font-weight: 600;
}
.page-title-box {
  padding: 20px;
  border-bottom: 1px solid #eee;
  margin: 0 -20px 20px -20px;
}
.page-title-box .page-title {
  margin-bottom: 0;
  float: left;
}
.page-title-box .breadcrumb {
  float: right;
  background-color: transparent !important;
}
/* Body min-height set */
body.fixed-left-void {
  min-height: 1250px;
}
