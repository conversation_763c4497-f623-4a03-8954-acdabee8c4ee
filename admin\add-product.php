<?php
session_start();
include('includes/config.php');
error_reporting(0);
if (strlen($_SESSION['login']) == 0) {
    header('location:index.php');
} else {

    if (isset($_POST['submit'])) {
        $product = $_POST['product'];
        $description = $_POST['description'];
        $visibility = $_POST['visibility'];
        $status = 1;

        // Handle logo upload
        $logo_name = "";
        if (isset($_FILES['logo']) && $_FILES['logo']['name'] != "") {
            $logo_name = $_FILES['logo']['name'];
            $logo_tmp = $_FILES['logo']['tmp_name'];
            $upload_dir = "uploads/logos/";
            // Create directory if it doesn't exist
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }
            $logo_path = $upload_dir . time() . "_" . $logo_name;
            move_uploaded_file($logo_tmp, $logo_path);
        }

        $query = mysqli_query($con, "insert into tblarea(ProductName,Description,visibility,Is_Active,Logo) values('$product','$description', '$visibility','$status','$logo_path')");
        if ($query) {
            $msg = "Product created successfully";
        } else {
            $error = "Something went wrong. Please try again.";
        }
    }
    ?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <title>Celaeno Technology | Add Product</title>
        <!-- App css -->
        <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
        <link rel="stylesheet" href="../plugins/switchery/switchery.min.css">
        <script src="assets/js/modernizr.min.js"></script>
        <style>
            .logo-preview {
                max-width: 200px;
                margin-top: 20px;
            }
        </style>
    </head>

    <body class="fixed-left">
        <div id="wrapper">
            <?php include('includes/topheader.php'); ?>
            <?php include('includes/leftsidebar.php'); ?>

            <div class="content-page">
                <div class="content">
                    <div class="container">
                        <div class="row">
                            <div class="col-xs-12">
                                <div class="page-title-box">
                                    <ol class="breadcrumb p-0 m-0">
                                        <li>
                                            <a href="./admin-page.php">Admin</a>
                                        </li>
                                        <li>
                                            <a href="./manage-product.php">Product</a>
                                        </li>

                                        <li class="active">
                                            Add Product
                                        </li>
                                    </ol>
                                    <div class="clearfix"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <div class="card-box">
                                    <h4 class="m-t-0 header-title"><b>Add Product</b></h4>
                                    <hr />

                                    <div class="row">
                                        <div class="col-sm-6">
                                            <?php if ($msg) { ?>
                                                <div class="alert alert-success" role="alert">
                                                    <strong>Well done!</strong> <?php echo htmlentities($msg); ?>
                                                </div>
                                            <?php } ?>
                                            <?php if ($error) { ?>
                                                <div class="alert alert-danger" role="alert">
                                                    <strong>Oh snap!</strong> <?php echo htmlentities($error); ?>
                                                </div>
                                            <?php } ?>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <form class="form-horizontal" name="product" method="post"
                                                enctype="multipart/form-data">
                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Product</label>
                                                    <div class="col-md-10">
                                                        <input type="text" class="form-control" value="" name="product"
                                                            required>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Product Description</label>
                                                    <div class="col-md-10">
                                                        <textarea class="form-control" rows="5" name="description"
                                                            required></textarea>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Product Visibility</label>
                                                    <div class="col-md-10">
                                                        <input type="radio" name="visibility" id="visibility_public"
                                                            value="public" checked>
                                                        <label for="visibility_public">Public</label>
                                                        <input type="radio" name="visibility" id="visibility_private"
                                                            value="private">
                                                        <label for="visibility_private">Private</label>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Logo</label>
                                                    <div class="col-md-10">
                                                        <input type="file" class="form-control" name="logo"
                                                            accept="image/*">
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">&nbsp;</label>
                                                    <div class="col-md-10">
                                                        <button type="submit"
                                                            class="btn btn-custom waves-effect waves-light btn-md"
                                                            name="submit">
                                                            Submit
                                                        </button>
                                                        <a href="manage-product.php"
                                                            class="btn btn-custom waves-effect waves-light btn-md">
                                                            Cancel
                                                        </a>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Logo Display at Bottom -->
                <div class="footer-logo" style="text-align: center; padding: 20px;">
                    <?php if (isset($logo_path) && file_exists($logo_path)) { ?>
                        <img src="<?php echo $logo_path; ?>" alt="Product Logo" class="logo-preview">
                    <?php } ?>
                </div>

                <?php include('includes/footer.php'); ?>
            </div>
        </div>

        <script>
            var resizefunc = [];
        </script>

        <!-- jQuery  -->
        <script src="assets/js/jquery.min.js"></script>
        <script src="assets/js/bootstrap.min.js"></script>
        <script src="assets/js/detect.js"></script>
        <script src="assets/js/fastclick.js"></script>
        <script src="assets/js/jquery.blockUI.js"></script>
        <script src="assets/js/waves.js"></script>
        <script src="assets/js/jquery.slimscroll.js"></script>
        <script src="assets/js/jquery.scrollTo.min.js"></script>
        <script src="../plugins/switchery/switchery.min.js"></script>
        <script src="assets/js/jquery.core.js"></script>
        <script src="assets/js/jquery.app.js"></script>
    </body>

    </html>
<?php } ?>