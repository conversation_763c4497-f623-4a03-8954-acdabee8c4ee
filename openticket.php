<?php
session_start();
include "./admin/includes/config.php";
// Get ticket ID from URL
$ticketId = isset($_GET['ticketid']) ? $_GET['ticketid'] : "";
$currentTicketId = isset($_GET['ticketid']) ? $_GET['ticketid'] : null;

// Prepare the SQL query to fetch ticket details
$sql = "SELECT * FROM tickets WHERE id = ?";
$stmt = $con->prepare($sql);
$stmt->bind_param("s", $ticketId); // "i" for integer
$stmt->execute();
$result = $stmt->get_result();

// Fetch ticket data
$ticket = $result->fetch_assoc();

if (!$ticket) {
    die("Ticket not found.");
}

function getMessagesForTicket(mysqli $con, string $ticketId): array
{
    $messages = [];
    $stmt = $con->prepare("SELECT id, sender_id, sender_type, message, created_at FROM tblticket_conversation WHERE ticket_id = ? ORDER BY created_at ASC");

    if ($stmt) {
        $stmt->bind_param("s", $ticketId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $messages[] = $row;
            }
            $result->free();
        } else {
            error_log("Error fetching messages: " . $stmt->error);
        }
        $stmt->close();
    } else {
        error_log("Error preparing statement: " . $con->error);
    }
    return $messages;
}





if ($currentTicketId) {
    $messages = getMessagesForTicket($con, $currentTicketId);
} else {
    $messages = []; // Or handle the case where no ticket ID is provided
}



?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Ticket Details</title>
    <style>
        /* Conversion */
        #sendMessageForm textarea {
            width: 100%;
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
            resize: none;
            /* Prevent manual resizing */
            overflow: hidden;
            /* Hide scrollbar until needed */
            min-height: 40px;
            /* Initial minimum height */
        }

        #sendMessageForm button {
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
        }

        #sendMessageForm button:hover {
            background-color: #0056b3;
        }

        .send-message-form {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }

        .send-message-form h3 {
            margin-bottom: 10px;
            color: #333;
        }



        .send-message-form button {
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
        }

        .send-message-form button:hover {
            background-color: #0056b3;
        }

        .error {
            color: red;
            margin-bottom: 10px;
        }

        .conversations {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            clear: both;
            /* Prevent floating issues */
        }

        .client-message {
            background-color: #e6f7ff;
            border: 1px solid #ccebff;
            float: left;
            text-align: left;
        }

        .admin-message {
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            float: right;
            text-align: right;
        }

        .system-message {
            background-color: #f9e6ff;
            border: 1px solid #f3cbf7;

            font-style: italic;
            color: #777;
        }

        .sender-info {
            font-size: 0.9em;
            color: #555;
            margin-bottom: 5px;
        }

        .sender-info strong {
            font-weight: bold;
        }

        .timestamp {
            font-size: 0.8em;
            color: #888;
            margin-left: 10px;
        }

        .message-content {
            white-space: pre-line;
            /* Preserve line breaks */
        }

        /* end */
        /* Reset default styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }

        /* Body styles */
        body {
            background-color: #f1f1ec;
            /* Secondary color */
            color: #333;
            line-height: 1.6;
        }

        /* Header styles */
        .header {
            background-color: #007474;
            /* Primary color */
            color: white;
            padding: 1rem 2rem;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .welcome {
            font-size: 1.5rem;
            font-weight: 600;
        }

        /* Container styles */
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        h2 {
            color: #007474;
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
        }

        /* Ticket table styles */
        .ticket-table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            border-radius: 8px;
            overflow: hidden;
        }

        .ticket-table th,
        .ticket-table td {
            padding: 1rem;
            text-align: left;
        }

        .ticket-table th {
            background-color: #007474;
            color: white;
            font-weight: 600;
        }

        .ticket-table tr {
            transition: background-color 0.3s ease;
        }

        .ticket-table tr:hover {
            background-color: #f5f5f5;
        }

        .ticket-table a {
            color: #007474;
            text-decoration: none;
            font-weight: 600;
        }

        .ticket-table a:hover {
            text-decoration: underline;
        }

        .status-open {
            color: #007474;
            font-weight: 600;
        }

        .status-closed {
            color: #666;
            font-weight: 600;
        }

        /* Ticket detail page styles */
        .ticket-detail {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .ticket-info {
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }

        .ticket-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
        }

        .ticket-item {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
        }

        .ticket-item strong {
            color: #007474;
            font-size: 1rem;
            font-weight: 600;
        }

        .ticket-item p {
            margin: 0;
            font-size: 1rem;
            color: #333;
        }

        /* Conversation table styles */
        .conversation {
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }

        .conversation-table {
            width: 100%;
            border-collapse: collapse;
        }

        .conversation-table th,
        .conversation-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .conversation-table th {
            background-color: #007474;
            color: white;
            font-weight: 600;
        }

        .conversation-table tr:hover {
            background-color: #f5f5f5;
        }
    </style>
</head>

<body>
    <header class="header">
        <div class="welcome">Welcome, <?php echo $ticket['first_name'] . " " . $ticket['last_name']; ?></div>
    </header>
    <main class="container ticket-detail">
        <div class="ticket-info">
            <h2>Ticket Details</h2>
            <div class="ticket-grid">
                <div class="ticket-item">
                    <strong>Ticket #</strong>
                    <p><?php echo $ticket['id']; ?></p>
                </div>
                <div class="ticket-item">
                    <strong>Date opened</strong>
                    <p><?php echo $ticket['created_at']; ?></p>
                </div>
                <div class="ticket-item">
                    <strong>Status</strong>
                    <p><?php echo $ticket['status']; ?></p>
                </div>
                <div class="ticket-item">
                    <strong>Created by</strong>
                    <p><?php echo $ticket['first_name'] . " " . $ticket['last_name']; ?></p>
                </div>
                <div class="ticket-item">
                    <strong>Phone</strong>
                    <p><?php echo $ticket['phone']; ?></p>
                </div>
                <div class="ticket-item">
                    <strong>Email</strong>
                    <p><?php echo $ticket['email']; ?></p>
                </div>
            </div>
        </div>
        <div class="conversations">
            <?php if ($messages): ?>
                <?php foreach ($messages as $message): ?>
                    <?php
                    $messageClass = '';
                    $senderName = '';

                    if ($message['sender_type'] === 'client') {
                        $messageClass = 'client-message';
                        $senderName = 'Client #' . $message['sender_id']; // Basic identification
                    } elseif ($message['sender_type'] === 'system') {
                        $messageClass = 'system-message';
                        $senderName = $user_map[$message['sender_id']] ?? 'System';
                    } else {
                        $messageClass = 'admin-message';
                        $senderName = 'Admin #' . $message['sender_id']; // Basic identification
                    }
                    ?>
                    <div class="message <?php echo $messageClass; ?>">
                        <div class="sender-info">
                            <strong><?php echo htmlspecialchars($senderName); ?></strong>
                            <span class="timestamp"><?php echo date('Y-m-d H:i:s', strtotime($message['created_at'])); ?></span>
                        </div>
                        <div class="message-content">
                            <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <p>No messages in this conversation yet.</p>
            <?php endif; ?>
        </div>
        <div class="send-message-form">
            <?php if (isset($errorMessage)): ?>
                <p class="error"><?php echo htmlspecialchars($errorMessage); ?></p>
            <?php endif; ?>
            <form id="sendMessageForm">
                <input type="hidden" name="ticketId" value="<?php echo htmlspecialchars($currentTicketId); ?>">
                <input type="hidden" name="senderType" value="client">
                <textarea name="newMessage" placeholder="Enter your message here" oninput="autoResize(this)"></textarea>
                <button type="button" onclick="sendMessage()">Send</button>
            </form>
        </div>
    </main>

    <script>

        document.addEventListener("DOMContentLoaded", function () {
            const conversationsDiv = document.querySelector('.conversations');
            if (conversationsDiv) {
                conversationsDiv.scrollTop = conversationsDiv.scrollHeight;
            }

            // Initialize autoresize on page load and for any existing content
            const textarea = document.querySelector('#sendMessageForm textarea[name="newMessage"]');
            if (textarea) {
                autoResize(textarea);
            }
        })

        const MAX_HEIGHT = 150; // Set your desired maximum height in pixels

        function autoResize(textarea) {
            textarea.style.height = 'auto';
            if (textarea.scrollHeight > MAX_HEIGHT) {
                textarea.style.height = MAX_HEIGHT + 'px';
                textarea.style.overflowY = 'auto'; // Enable vertical scrollbar
            } else {
                textarea.style.height = (textarea.scrollHeight) + 'px';
                textarea.style.overflowY = 'hidden'; // Hide scrollbar if content is within max height
            }
        }

        function sendMessage() {
            const form = document.getElementById('sendMessageForm');
            const formData = new FormData(form);

            fetch('./client-message.php', {
                method: 'POST',
                body: formData
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(data => {
                    console.log('Message sent successfully:', data);
                    window.location.reload();
                })
                .catch(error => {
                    console.error('Error sending message:', error);
                });
        }

    </script>
</body>

</html>