<?php
require_once("./includes/config.php");
require_once("./utils/groups-functions.php");

$ticket_id = isset($_GET['ticketId']) ? $_GET['ticketId'] : null;

if ($ticket_id) {
    $stmt = $con->prepare("SELECT * FROM tickets WHERE id = ?");
    $stmt->bind_param("s", $ticket_id);
    $stmt->execute();
    $ticket_result = $stmt->get_result();
    $ticket = $ticket_result->fetch_assoc();
}

$user_map = getAllUsers($con);


?>


<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Edit Ticket</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: "Segoe UI", sans-serif;
        }

        body {
            background-color: #f4f6f8;
            padding: 40px;
        }

        .form-container {
            max-width: 800px;
            margin: auto;
            background-color: #ffffff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        h2 {
            margin-bottom: 20px;
            text-align: center;
            color: #333;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ccc;
            border-radius: 8px;
            font-size: 15px;
        }

        .form-group input[readonly],
        .form-group textarea[readonly] {
            background-color: #f0f0f0;
            color: #666;
            cursor: not-allowed;
        }

        .form-row {
            display: flex;
            gap: 20px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 30px;
        }

        .buttons button,
        .buttons a {
            padding: 10px 20px;
            font-size: 15px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: 0.3s ease;
        }

        .buttons .back-btn {
            background-color: #ccc;
            color: #333;
            text-decoration: none;
        }

        .buttons .back-btn:hover {
            background-color: #bbb;
        }

        .buttons .save-btn {
            background-color: #007474;
            color: white;
        }

        .buttons .save-btn:hover {
            background-color: #007474;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
        }
    </style>
</head>

<body>
    <div class="form-container">
        <h2>Edit Ticket</h2>
        <form action="./ticket-operation.php" method="post">
            <div class="form-row">
                <div class="form-group">
                    <label>Ticket ID</label>
                    <input type="hidden" name="ticketId" value="<?php echo $ticket["id"]; ?>" readonly />
                    <input type="text" name="id" value="<?php echo $ticket["id"]; ?>" readonly />
                </div>
                <div class="form-group">
                    <label>Created On</label>
                    <input type="text" value="<?php echo $ticket["created_at"]; ?>" disabled />
                </div>
            </div>

            <div class="form-group">
                <label>Subject</label>
                <input type="text" value="<?php echo $ticket["subject"]; ?>" disabled />
            </div>

            <div class="form-group">
                <label>Description</label>
                <?php echo $ticket["description"]; ?>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>Reporter Name</label>
                    <input type="text" value="<?php echo $ticket["first_name"] . " " . $ticket["last_name"]; ?>"
                        disabled />
                </div>
                <div class="form-group">
                    <label>Reporter Email</label>
                    <input type="email" value="<?php echo $ticket["email"]; ?>" disabled />
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>Channel</label>
                    <input type="text" value="<?php echo $ticket["channel"]; ?>" disabled />
                </div>
                <div class="form-group">
                    <label>Product</label>
                    <input type="text" value="<?php echo $ticket["products"]; ?>" disabled />
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>Type</label>
                    <input type="text" value="<?php echo $ticket["request_type"]; ?>" disabled />
                </div>
                <div class="form-group">
                    <label>Status</label>

                    <select name="status">
                        <option value="New" <?php if ($ticket["status"] === "New")
                            echo "selected"; ?>>Open</option>
                        <option value="In Progress" <?php if ($ticket["status"] === "In Progress")
                            echo "selected"; ?>>In Progress</option>
                        <option value="Waiting on Customer" <?php if ($ticket["status"] === "Waiting on Customer")
                            echo "selected"; ?>>
                            Waiting on Customer</option>
                        <option value="Closed" <?php if ($ticket["status"] === "Closed")
                            echo "selected"; ?>>Closed
                        </option>
                        <option value="Resolved" <?php if ($ticket["status"] === "Resolved")
                            echo "selected"; ?>>Resolved
                        </option>
                        <option value="Hold" <?php if ($ticket["status"] === "Hold")
                            echo "selected"; ?>>Hold
                        </option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>Priority</label>
                    <select name="priority">
                        <option value="Low" <?php echo $ticket["priority"] === "Low" ? "selected" : ""; ?>>Low</option>
                        <option value="Medium" <?php echo $ticket["priority"] === "Medium" ? "selected" : ""; ?>>Medium
                        </option>
                        <option value="High" <?php echo $ticket["priority"] === "High" ? "selected" : ""; ?>>High</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Assignee</label>
                    <select name="assignee">
                        <option value="">Unassigned</option>
                        <?php foreach ($user_map as $user_id => $user_name): ?>
                            <option value="<?php echo htmlspecialchars($user_id); ?>" <?php if ($ticket["assignee"] == $user_id)
                                   echo 'selected'; ?>>
                                <?php echo htmlspecialchars($user_name); ?>
                            </option>
                        <?php endforeach; ?>
                        <option value="team_lead" <?php if ($ticket["assignee"] === 'team_lead')
                            echo 'selected'; ?>>Team
                            Lead</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label>Due Date</label>
                <input type="date" name="due_date" value="<?php echo $ticket["due_date"]; ?>" />
            </div>

            <div class="buttons">
                <a href="tickets.html" class="back-btn">Back</a>
                <button type="submit" class="save-btn">Save Changes</button>
            </div>
        </form>
    </div>
</body>

</html>