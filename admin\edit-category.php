<?php
session_start();
include('includes/config.php');
error_reporting(0);

if (strlen($_SESSION['login']) == 0) {
    header('location:index.php');
} else {
    if (isset($_POST['submit'])) {
        $catid = intval($_GET['scid']);
        $category = $_POST['category'];
        $description = $_POST['description'];
        $accessibility = $_POST['accessibility'];
        $status = $_POST['status'];
        $query = mysqli_query($con, "UPDATE tblcategory SET Category='$category', CategoryDescription='$description', Accessibility='$accessibility', Status='$status' WHERE CategoryId='$catid'");

        if ($query) {
            $msg = "Category Updated successfully";
        } else {
            $error = "Something went wrong. Please try again.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <title>Celaeno Technology | Edit Category</title>
    <!-- App css -->
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../plugins/switchery/switchery.min.css">
    <script src="assets/js/modernizr.min.js"></script>
</head>

<body class="fixed-left">
    <div id="wrapper">
        <!-- Top Bar Start -->
        <?php include('includes/topheader.php'); ?>
        <!-- Top Bar End -->

        <!-- Left Sidebar Start -->
        <?php include('includes/leftsidebar.php'); ?>
        <!-- Left Sidebar End -->

        <div class="content-page">
            <div class="content">
                <div class="container">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="page-title-box">
                                <h4 class="page-title">Edit Category</h4>
                                <ol class="breadcrumb p-0 m-0">
                                    <li><a href="#">Admin</a></li>
                                    <li><a href="manage-categories.php">Category</a></li>
                                    <li class="active">Edit Category</li>
                                </ol>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-12">
                            <div class="card-box">
                                <h4 class="m-t-0 header-title"><b>Edit Category</b></h4>
                                <hr />

                                <div class="row">
                                    <div class="col-sm-6">
                                        <?php if ($msg) { ?>
                                            <div class="alert alert-success" role="alert">
                                                <strong>Well done!</strong> <?php echo htmlentities($msg); ?>
                                            </div>
                                        <?php } ?>

                                        <?php if ($error) { ?>
                                            <div class="alert alert-danger" role="alert">
                                                <strong>Oh snap!</strong> <?php echo htmlentities($error); ?>
                                            </div>
                                        <?php } ?>
                                    </div>
                                </div>

                                <?php
                                $catid = intval($_GET['scid']);
                                $query = mysqli_query($con, "SELECT CategoryId, Category, CategoryDescription, Accessibility, Status, PostingDate, UpdationDate FROM tblcategory WHERE Is_Active = 1 AND CategoryId='$catid'");
                                while ($row = mysqli_fetch_array($query)) {
                                ?>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <form class="form-horizontal" name="editcategory" method="post">
                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Category</label>
                                                    <div class="col-md-10">
                                                        <input type="text" class="form-control"
                                                            value="<?php echo htmlentities($row['Category']); ?>"
                                                            name="category" required>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Category Description</label>
                                                    <div class="col-md-10">
                                                        <textarea class="form-control" rows="5"
                                                            name="description"
                                                            required><?php echo htmlentities($row['CategoryDescription']); ?></textarea>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Accessibility</label>
                                                    <div class="col-md-10">
                                                        <select class="form-control" name="accessibility" required>
                                                            <option value="">Select Accessibility</option>
                                                            <option value="Public" <?php if ($row['Accessibility'] == 'Public') echo 'selected'; ?>>Public</option>
                                                            <option value="Internal" <?php if ($row['Accessibility'] == 'Internal') echo 'selected'; ?>>Internal</option>
                                                            <option value="Private" <?php if ($row['Accessibility'] == 'Private') echo 'selected'; ?>>Private</option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">Status</label>
                                                    <div class="col-md-10">
                                                        <select class="form-control" name="status" required>
                                                            <option value="">Select Status</option>
                                                            <option value="Draft" <?php if ($row['Status'] == 'Draft') echo 'selected'; ?>>Draft</option>
                                                            <option value="Published" <?php if ($row['Status'] == 'Published') echo 'selected'; ?>>Published</option>
                                                            <option value="Retired" <?php if ($row['Status'] == 'Retired') echo 'selected'; ?>>Retired</option>
                                                        </select>
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <div class="col-md-offset-2 col-md-10">
                                                        <button type="submit"
                                                            class="btn btn-custom waves-effect waves-light btn-md"
                                                            name="submit">
                                                            Update
                                                        </button>
                                                        <but>
                                                            <a href="manage-categories.php" class="btn btn-custom waves-effect waves-light btn-md">
                                                                Cancel
                                                            </a>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div> <!-- container -->
            </div> <!-- content -->

            <?php include('includes/footer.php'); ?>
        </div>
    </div>

    <script>
        var resizefunc = [];
    </script>


    <!-- jQuery -->
    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/detect.js"></script>
    <script src="assets/js/fastclick.js"></script>
    <script src="assets/js/jquery.blockUI.js"></script>
    <script src="assets/js/waves.js"></script>
    <script src="assets/js/jquery.slimscroll.js"></script>
    <script src="assets/js/jquery.scrollTo.min.js"></script>
    <script src="../plugins/switchery/switchery.min.js"></script>

    <!-- App js -->
    <script src="assets/js/jquery.core.js"></script>
    <script src="assets/js/jquery.app.js"></script>
</body>

</html>