<?php
session_start();
include('includes/config.php');

// Get the article ID from the query string
$articleId = $_GET['article_id'];


// Fetch the article details from the database
$query = "SELECT * FROM tblarticles WHERE id = ?";
$stmt = $con->prepare($query);
$stmt->bind_param("i", $articleId);
$stmt->execute();
$result = $stmt->get_result();

// Check if the article exists
if ($result->num_rows > 0) {
    $article = $result->fetch_assoc();
} else {
    // Redirect or show an error message
    echo '<p class="error-message">Oops! No article found.</p>';
    exit; // Stop further script execution
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($article['ArticleTitle']); ?></title>
    <link href="assets/css/knowledge-base.css" rel="stylesheet" type="text/css" />
    <style>
        .text-muted img {
            width: 100px;
            height: 100px;
            display: none;

        }
    </style>

</head>

<body>
    <div class="article-container">


        <!-- Tab Navigation -->
        <div class="tab-nav">
            <a href="#article" class="active" onclick="showTab('article')">Article</a>
            <a href="#comment" onclick="showTab('comment')">Comment</a>
            <a href="#history" onclick="showTab('history')">History</a>
        </div>

        <!-- Tab Content -->
        <div id="article" class="tab-content active">
            <h1 class="article-title"><?php echo htmlspecialchars($article['ArticleTitle']); ?></h1>
            <div class="article-meta">

                <span class="date"><?php echo date("F j, Y", strtotime($article['UpdationDate'])); ?></span>
                <!-- In your article listing table -->

                <a href="edit-article.php?pid=<?php echo htmlspecialchars($article['id']); ?>" class="custom-button">Edit</a>

                <style>

                </style>

            </div>
            <div class="article-content">
                <?php if (!empty($article['ImageURL'])): ?>
                    <img src="<?php echo ($article['ImageURL']); ?>" alt="Article Image">
                <?php endif; ?>
                <p><?php echo nl2br(($article['ArticleDetails'])); ?></p>

                <?php if (!empty($article['VideoURL'])): ?>
                    <iframe src="<?php echo ($article['VideoURL']); ?>" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                <?php endif; ?>


                <?php
                // Check if article_id is set (for example, from a GET request)
                $article_id = isset($_GET['article_id']) ? intval($_GET['article_id']) : 0;

                // Check if the `article_id` is valid
                if ($article_id === 0) {
                    die("Error: Invalid article ID.");
                }



                // Handle form submission only when the method is POST
                if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['description'])) {
                    // Retrieve and sanitize input values
                    $name = isset($_SESSION['login']) ? trim($_SESSION['login']) : 'Anonymous';
                    $description = trim($_POST['description']);

                    // Sanitize input to prevent SQL injection & XSS
                    $name = $con->real_escape_string($name);
                    $description = $con->real_escape_string($description);

                    // Insert into the database
                    $sql = "INSERT INTO tblcomments (article_id, name, description) VALUES (?, ?, ?)";
                    $stmt = $con->prepare($sql);
                    $stmt->bind_param("iss", $article_id, $name, $description);

                    if ($stmt->execute()) {
                        // Redirect to prevent duplicate form submission on refresh
                        header("Location: " . $_SERVER['PHP_SELF'] . "?article_id=" . $article_id);
                        exit(); // Ensure script stops execution after redirect
                    } else {
                        $message = "Error: " . $stmt->error;
                    }

                    $stmt->close();
                }
                ?>

                <div class="comments-section">
                    <h3>Leave a Comment</h3>

                    <!-- Display success/error messages -->
                    <?php if (isset($message)): ?>
                        <p><?php echo $message; ?></p>
                    <?php endif; ?>

                    <form action="" method="POST"> <!-- Self-processing form -->
                        <input type="hidden" name="article_id" value="<?php echo $article_id; ?>"> <!-- Keep article ID -->

                        <label for="description">Your Comment:</label>
                        <textarea id="description" name="description" rows="4" cols="50" required></textarea><br>

                        <input type="submit" value="Submit">
                    </form>

                </div>
            </div>
        </div>



        <!-- Comment Section -->
        <div id="comment" class="tab-content">
            <h3>Comments</h3>
            <?php
            // Fetch comments for the article
            $sql = "SELECT name, description, created_at FROM tblcomments WHERE article_id = ? ORDER BY created_at DESC";
            $stmt = $con->prepare($sql);
            $stmt->bind_param("i", $article_id);
            $stmt->execute();
            $result = $stmt->get_result();

            // Check if comments exist
            if ($result->num_rows > 0) {
                while ($comment = $result->fetch_assoc()) {
            ?>
                    <div class="comment-box">
                        <strong><?php echo htmlspecialchars($comment['name']); ?>:</strong>
                        <p><?php echo nl2br(htmlspecialchars($comment['description'])); ?></p>
                        <small><?php echo date("F j, Y, g:i a", strtotime($comment['created_at'])); ?></small>
                        <hr>
                    </div>
            <?php
                }
            } else {
                echo "<p>No comments yet. Be the first to comment!</p>";
            }

            $stmt->close();
            ?>

            <?php

            // Fetch comments specific to the current article (based on articleId)
            // $query = "SELECT * FROM tblcomments WHERE postid = ? ORDER BY postingDate DESC";
            // $stmt = $con->prepare($query);
            // $stmt->bind_param("i", $articleId);  // Bind the articleId to the query
            // $stmt->execute();
            // $result = $stmt->get_result();

            // // Check if there are any comments
            // if ($result->num_rows > 0) {
            //     // Loop through the comments and display them
            //     while ($row = $result->fetch_assoc()) {
            //         $comment_text = htmlspecialchars($row['comment']); // Sanitize input
            //         $comment_author = htmlspecialchars($row['name']); // Sanitize input
            //         $comment_date = htmlspecialchars($row['postingDate']); // Sanitize input

            //         echo '<div class="comment">';
            //         echo '<p><strong>' . $comment_author . '</strong> commented on ' . date("F j, Y, g:i a", strtotime($comment_date)) . ':</p>';
            //         echo '<p>' . nl2br($comment_text) . '</p>'; // Use nl2br to preserve line breaks in comments
            //         echo '</div>';
            //     }
            // } else {
            //     echo '<p>No comments available.</p>';
            // }
            ?>
        </div>


        <!-- History Section -->
        <div id="history" class="tab-content">

            <?php
            // Query to select all records from tblarticleshistory
            $query = "SELECT * FROM tblarticleshistory where id=" . $_GET["article_id"];
            $result = $con->query($query);

            // Check if there are records
            if ($result->num_rows > 0) {


                // Fetch and display each row
                while ($article = $result->fetch_assoc()) { ?>
                    <div class="border-bottom pb-2 mb-3">
                        <h2 class="text-success">
                            <a href="edit-article.php?pid=<?php echo htmlentities($article['id']); ?>&hid=<?php echo htmlentities($article['history_id']); ?>&history=true"
                                style="text-decoration: none;">
                                <?php echo htmlentities($article['ArticleTitle']); ?>
                                --<?php echo htmlentities($article['updateby']); ?>
                                --<?php echo htmlentities($article['UpdationDate']); ?>

                            </a>
                        </h2>
                        <h4>

                            <!-- <div class="text-muted"><?php echo ($article['ArticleDetails']); ?></div> -->
                        </h4>


                    </div>
            <?php  }
            } else {
                echo "No history found.";
            }
            ?>
        </div>
    </div>


    <script>
        function showTab(tabName) {
            // Hide all tabs
            var tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(function(tab) {
                tab.classList.remove('active');
            });

            // Remove active class from all nav items
            var navItems = document.querySelectorAll('.tab-nav a');
            navItems.forEach(function(nav) {
                nav.classList.remove('active');
            });

            // Show the clicked tab
            document.getElementById(tabName).classList.add('active');

            // Add active class to the clicked nav item
            var activeNav = document.querySelector(`.tab-nav a[href='#${tabName}']`);
            activeNav.classList.add('active');
        }
    </script>
</body>

</html>