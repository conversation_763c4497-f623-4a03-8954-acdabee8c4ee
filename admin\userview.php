<?php
session_start();
include('includes/config.php');

// Enable error reporting for debugging (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (empty($_SESSION['login'])) {
    header('Location: index.php');
    exit();
}

?>

<!DOCTYPE html>
<html>

<head>
    <title>Celaeno Technology | Manage Articles</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/knowledge-base.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/input.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
        integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        /* Popup css */

        .project-form-inviteEmployee {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .inv-head-jnksmc898 {
            display: flex;
            justify-content: space-between;
            align-items: center;

        }

        .inv-head-jnksmc898 i {
            font-size: 26px;
            cursor: pointer;
        }

        .popup-overlay-inviteEmployee {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .popup-form-inviteEmployee {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            min-width: 30vw;
            /* height: 50vh; */
            /* max-height: 90vh;
  min-height: 40vh; */
            overflow-y: auto;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            z-index: 1000;
            overflow: auto;
        }

        .btn-container-popup {
            display: flex;
            align-items: center;
            justify-content: end;
            gap: 1rem;
        }

        .btn-container-popup .cancle-btn-0po90 {
            color: #007474;
            border: 2px solid #007474;
            font-size: small;
            font-weight: bold;
            padding: 4px 12px;
            cursor: pointer;
        }

        .btn-container-popup .cancle-btn-0po90:hover {
            background-color: #f1f1ec;
        }

        .btn-container-popup .save-btn-0po90 {
            color: white;
            background-color: #007474;
            font-size: small;
            font-weight: bold;
            padding: 4px 12px;
            cursor: pointer;
        }

        .btn-container-popup .save-btn-0po90:hover {
            color: white;
        }

        /* End */

        .page-title-box .page-title {
            margin-bottom: 22px;
            float: left;
        }

        .ticket-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-family: Arial, sans-serif;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            overflow: hidden;
        }

        .ticket-table th,
        .ticket-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .ticket-table th {
            background-color: #007474;
            color: white;
            font-weight: 600;
        }

        .ticket-table td {
            color: #333;
        }

        .ticket-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .ticket-table tr:hover {
            background-color: #f1f3f5;
            transition: background-color 0.2s ease;
        }

        /* Responsive design */
        @media (max-width: 600px) {
            .ticket-table {
                display: block;
                overflow-x: auto;
            }
        }
    </style>


</head>

<body class="fixed-left">
    <div id="wrapper">
        <?php include('includes/topheader.php'); ?>
        <?php include('includes/leftsidebar.php'); ?>

        <div class="content-page">
            <div class="content">
                <div class="container">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="page-title-box">
                                <ol class="breadcrumb p-0 m-0">
                                    <li>
                                        <a href="./admin-page.php">Admin</a>
                                    </li>

                                    <li class="active">
                                        User
                                    </li>
                                </ol>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>


                    <div class="row">
                        <div class="col-sm-12">
                            <div class="filter-articles">
                                <a href="#">
                                    <button id="addToTable" class="btn btn-success waves-effect waves-light"
                                        style="margin-bottom: 1rem; margin-left: auto; display: block;"
                                        onclick="openinviteEmployeePopup(event)">
                                        Invite Employee
                                    </button>
                                </a>
                                <a href="add-user.php">
                                    <button id="addToTable" class="btn btn-success waves-effect waves-light"
                                        style="margin-bottom: 1rem; margin-left: auto; display: block;">
                                        <i class="fa-solid fa-plus"></i> Add user
                                    </button>
                                </a>

                            </div>
                        </div>
                    </div>



                    <table class="ticket-table">
                        <thead>
                            <tr>
                                <th>User ID</th>
                                <th>name</th>
                                <th>Title</th>
                                <th>Role</th>
                                <th>Active</th>
                                <th>Email</th>
                                <!-- <th>Phone</th> -->
                            </tr>
                        </thead>
                        <tbody>

                            <?php
                            // SQL query to retrieve users
                            $sql = "SELECT user_id, name, title, role, isActive, email, business_phone FROM tblusers";
                            $result = $con->query($sql);
                            if ($result->num_rows > 0) {
                                // Output data of each row
                                while ($row = $result->fetch_assoc()) {
                                    echo "<tr>
                                    <td>" . $row["user_id"] . "</td>
                                    <td><a href='./user-profile.php?userId=" . $row['user_id'] . "'>" . $row["name"] . "</a></td>
                                    <td>" . $row["title"] . "</td>
                                    <td>" . $row["role"] . "</td>
                                    <td>" . ($row["isActive"] ? 'True' : 'False') . "</td>
                                    <td>" . $row["email"] . "</td>
                                </tr>";
                                }
                            } else {
                                echo "<tr><td colspan='8'>0 results</td></tr>";
                            }

                            ?>
                            <!-- Add more rows here if needed -->
                        </tbody>
                    </table>

                </div>

            </div>
        </div>
    </div>
    <!-- invite employee -->
    <div class="popup-overlay-inviteEmployee" onclick="closeinviteEmployeePopup()"></div>
    <div class="popup-form-inviteEmployee">
        <form action="handle-invite.php" class="project-form-inviteEmployee" method="POST">
            <div class="inv-head-jnksmc898">
                <h2>Invite Employee</h2>
                <div>
                    <i class="fa-solid fa-xmark" onclick="closeinviteEmployeePopup()"></i>
                </div>
            </div>
            <div class="meber_form_fields">

                <div class="form-input">
                    <input type="text" name="name" id="name" placeholder="Name">
                    <label for="name">Name<span class="astrik-important">*</span></label>
                </div>
                <div class="form-input">
                    <input type="email" name="email" id="email" placeholder="Email">
                    <label for="email">Email<span class="astrik-important">*</span></label>
                </div>

                <div class="select-contaner">
                    <select class="role-select" name="role" id="role" data-mandatory="true">
                        <option value="">Select Role</option>
                        <option value="System Admin">System Admin</option>
                        <option value="Admin">Admin</option>
                        <option value="Manager">Manager</option>
                        <option value="Client">Client</option>
                        <option value="User">User</option>
                    </select>
                </div>


                <div class="btn-container-popup">
                    <button type="button" class="cancle-btn-0po90" onclick="closeinviteEmployeePopup()">Cancel</button>
                    <button type="submit" name="invite_user" class="save-btn-0po90">Save</button>
                </div>
            </div>

        </form>
    </div>

    <script src="assets/js/jquery.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/detect.js"></script>
    <script src="assets/js/fastclick.js"></script>
    <script src="admin/assets/js/jquery.blockUI.js"></script>

    <script src="admin/assets/js/jquery.slimscroll.js"></script>
    <script src="admin/assets/js/jquery.scrollTo.min.js"></script>
    <script src="admin/assets/js/jquery.core.js"></script>
    <script src="admin/assets/js/jquery.app.js"></script>


    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- <script>
        $(document).ready(function() {
            $(".btn-outline-secondary").click(function() {
                $(".cards-container").toggle(); // Toggles visibility
            });
        });
        document.addEventListener("DOMContentLoaded", function() {
            document.querySelector(".btn-outline-first").addEventListener("click", function() {
                document.querySelector(".cards-container").classList.toggle("list-view");
            });
        });
    </script> -->

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function () {
            // List view button
            $("#list-view-btn").click(function () {
                $("#view_mode").val("list"); // Update hidden input
                $(".cards-container").addClass("list-view"); // Apply list view
            });

            // Card view button
            $("#card-view-btn").click(function () {
                $("#view_mode").val("card"); // Update hidden input
                $(".cards-container").removeClass("list-view"); // Apply card view
            });

            // Apply the initial view based on the hidden input value
            if ($("#view_mode").val() === "list") {
                $(".cards-container").addClass("list-view");
            } else {
                $(".cards-container").removeClass("list-view");
            }
        });

        function openinviteEmployeePopup(event) {
            event.preventDefault();
            document.querySelector(".popup-overlay-inviteEmployee").style.display = "block";
            document.querySelector(".popup-form-inviteEmployee").style.display = "block";
        }

        function closeinviteEmployeePopup() {
            document.querySelector(".popup-overlay-inviteEmployee").style.display = "none";
            document.querySelector(".popup-form-inviteEmployee").style.display = "none";
        }

    </script>



</body>

</html>