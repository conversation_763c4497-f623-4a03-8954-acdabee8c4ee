<?php
require 'vendor/autoload.php';

use PhpImap\Mailbox;

// Use environment variables for credentials
$username = "<EMAIL>";
$password = "Celtech@2023!";
$mailbox = '{imap.hostinger.com:993/imap/ssl}INBOX';

try {
    $mailbox = new Mailbox($mailbox, $username, $password);
    $mailsIds = $mailbox->searchMailbox('ALL');

    if (!$mailsIds) {
        echo 'Mailbox is empty';
        exit;
    }

    foreach ($mailsIds as $mailId) {
        $mail = $mailbox->getMail($mailId);
        $styledContent = styleEmailContent($mail->textHtml, $mail->subject, $mail->fromAddress, $mail->date);
        echo $styledContent;
    }
} catch (Exception $e) {
    echo 'Error: ' . $e->getMessage();
}

function styleEmailContent($content, $subject, $from, $date)
{
    // Define your CSS styles
    $styles = '
    <style type="text/css">
    body {
        font-family: Arial, sans-serif;
        background-color: #f9f9f9;
        margin: 0;
        padding: 0;
    }
    .email-container {
        background-color: #ffffff;
        border: 1px solid #dddddd;
        border-radius: 5px;
        margin: 20px auto;
        padding: 20px;
        max-width: 80vw;
    }
    .email-header {
        border-bottom: 1px solid #dddddd;
        margin-bottom: 20px;
    }
    .email-header h2 {
        font-size: 24px;
        color: #333333;
    }
    .email-meta {
        font-size: 14px;
        color: #888888;
        margin-bottom: 20px;
    }
    .email-body {
        font-size: 16px;
        color: #333333;
    }
    </style>
    ';

    // Wrap the content in a styled template
    $styledContent = "
    $styles
    <div class='email-container'>
        <div class='email-header'>
            <h2>$subject</h2>
            <p class='email-meta'>From: $from<br>Date: $date</p>
        </div>
        <div class='email-body'>
            $content
        </div>
    </div>
    ";

    return $styledContent;
}
