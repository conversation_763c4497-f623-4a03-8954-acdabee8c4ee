<?php
use <PERSON><PERSON><PERSON><PERSON>er\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

require '../vendor/autoload.php'; // Adjust path if not using Composer

class EmailHelper
{

    public static function sendGenericEmail($toEmail, $subject, $body, $altBody, $fromEmail = null, $fromName = null)
    {
        $mail = new PHPMailer(true);

        try {
            // Server settings (update with your SMTP details)
            $mail->isSMTP();
            $mail->Host = 'smtp.gmail.com'; // Replace with your SMTP host
            $mail->SMTPAuth = true;
            $mail->Username = '<EMAIL>'; // Replace with your email
            $mail->Password = 'kqzs ndwu awsk qleh'; // Replace with your app password
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = 587;

            // Recipients
            $fromEmail = $fromEmail ?? '<EMAIL>'; // Use provided or default
            $fromName = $fromName ?? 'Celaeno HelpDesk  '; // use provided or default.

            $mail->setFrom($fromEmail, $fromName);
            $mail->addAddress($toEmail);

            // Content
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $body;
            $mail->AltBody = $altBody;

            $mail->send();
            return true;
        } catch (Exception $e) {
            error_log("Email sending failed: {$mail->ErrorInfo}");
            return false;
        }
    }

    public static function sendInvitationEmail($toEmail, $token)
    {
        $acceptLink = "http://localhost/celaeno_cms/accept_invite.php?token=" . urlencode($token);

        $subject = 'Invitation to Join Celaeno HelpDesk';
        $body = "
        <h2>You've Been Invited!</h2>
        <p>You have been invited to join our Celaeno HelpDesk.</p>
        <p>Please click the link below to accept the invitation and set up your account:</p>
        <p><a href='$acceptLink'>Accept Invitation</a></p>
        <p>This link will expire in 24 hours.</p>
    ";
        $altBody = "You've been invited to join our Celaeno HelpDesk. Please visit $acceptLink to accept the invitation and set up your account. This link will expire in 24 hours.";

        return self::sendGenericEmail($toEmail, $subject, $body, $altBody, '<EMAIL>', 'Client Management System');
    }

    public static function sendEmail($toEmail, $subject, $body, $altBody)
    {
        return self::sendGenericEmail($toEmail, $subject, $body, $altBody);
    }

    public static function sendAccountCreationEmail($toEmail, $password)
    {
        $subject = 'Account Created';
        $body = "
            <h2>Account Created</h2>
            <p>Your account has been successfully created.</p>
            <p>Your password is: <strong>$password</strong></p>
            <p>Please change your password after logging in.</p>
        ";
        $altBody = "Your account has been successfully created. Your password is: $password. Please change your password after logging in.";

        return self::sendGenericEmail($toEmail, $subject, $body, $altBody);
    }
}
?>