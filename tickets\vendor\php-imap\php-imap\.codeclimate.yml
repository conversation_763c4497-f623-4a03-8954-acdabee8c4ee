# Advanced Configuration for CodeClimate: https://docs.codeclimate.com/docs
languages:
  PHP: true
exclude_paths: ["tests/*", "examples/*"]
version: "2"         # required to adjust maintainability checks
checks:
  argument-count:
    config:
      threshold: 5
  complex-logic:
    config:
      threshold: 4
  file-lines:
    config:
      threshold: 250
  method-complexity:
    config:
      threshold: 5
  method-count:
    config:
      threshold: 20
  method-lines:
    config:
      threshold: 40
  nested-control-flow:
    config:
      threshold: 4
  return-statements:
    config:
      threshold: 4
  similar-code:
    config:
      threshold: # language-specific defaults. an override will affect all languages.
  identical-code:
    config:
      threshold: # language-specific defaults. an override will affect all languages.
