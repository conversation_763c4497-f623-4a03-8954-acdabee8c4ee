/* Base footer styles */
.footer {
  background-color: #007474;
  color: white;
  padding: 20px 0;
  border-top: 5px solid white;
}

.footer-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  padding: 20px;
  border-bottom: 1px solid white;
}

.footer-section {
  margin: 20px;
  min-width: 200px;
  font-family: Arial, sans-serif;
}

.footer-section h3 {
  color: white;
  font-size: 2em;
}

.footer-section p {
  font-size: 1em;
  margin: 10px 0;
}

.social-icons {
  display: flex;
  gap: 10px;
  margin: 20px 0;
}

.social-icons a {
  text-decoration: none;
  color: #fff;
}

.social-icons a i {
  font-size: 35px;
  color: #fff;
  margin: 5px;
  transition: color 0.3s ease;
}

.social-icons a i:hover {
  color: #000;
}

.footer-bottom {
  text-align: center;
  padding: 62px 0;
  font-size: 0.9em;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0px 60px;
  padding-bottom: unset;
}

.footer-bottom a {
  color: #fff;
  text-decoration: none;
}

.footer-bottom p a:hover {
  color: #000;
}

.footer-flex {
  display: flex;
  align-items: center;
}

.footer-flex i {
  font-size: 20px;
  margin-right: 20px;
  color: white;
}

.footer-timing-div {
  display: flex;
  align-items: flex-start;
}

.footer-timing-div i {
  font-size: 20px;
  margin-right: 10px;
  color: white;
}

.footer-link {
  color: rgba(255, 255, 255, 0.8);
  margin: 0 15px;
  text-decoration: none;
  font-size: 16px;
}

.company-name {
  font-size: 28px;
  font-weight: 300;
  text-align: center;
}

.highlight {
  font-weight: bold;
  color: white;
}

.custom-divider {
  width: 60%;
  border-top: 1px solid rgba(234, 220, 220, 0.3);
  margin: 10px auto;
  color: white;
  font-size: 30px;
  text-align: center;
  font-size: 50px;
}

/* Tool cards */
.tool-card {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 15px;
  width: 300px;
  margin: 10px;
  text-align: center;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}

.card-content h5,
.card-content p {
  color: black;
  text-align: center;
}

.d-flex {
  display: flex;
  justify-content: center; /* Centers the tool cards */
  align-items: center;
  flex-wrap: nowrap; /* Ensures the tool cards remain in a single row */
  gap: 20px; /* Adds spacing between the cards */
  overflow-x: auto; /* Enables horizontal scrolling if needed */
  padding: 10px;
}

/* Responsive Design */

/* For Tablets (Max width: 768px) */
@media (max-width: 768px) {
  .footer-container {
    padding: 10px;
    flex-direction: column;
    align-items: center;
  }

  .footer-section {
    margin: 10px;
    min-width: 200px;
  }

  .footer-section h3 {
    font-size: 1.8em;
  }

  .footer-section p {
    font-size: 0.9em;
  }

  .social-icons {
    margin: 10px 0;
    justify-content: center;
  }

  .social-icons a i {
    font-size: 30px;
  }

  .footer-bottom {
    flex-direction: column;
    padding: 30px 0;
    margin: 0;
  }

  .footer-bottom p {
    font-size: 0.8em;
  }

  .tool-card {
    width: 250px;
    margin: 10px 5px;
  }

  .d-flex {
    flex-wrap: wrap;
    gap: 10px;
  }
}

/* For Mobile (Max width: 480px) */
@media (max-width: 480px) {
  .footer-container {
    padding: 10px;
    flex-direction: column;
    align-items: center;
  }

  .footer-section {
    margin: 10px;
    min-width: 200px;
    text-align: center;
  }

  .footer-section h3 {
    font-size: 1.5em;
  }

  .footer-section p {
    font-size: 0.8em;
  }

  .social-icons {
    justify-content: center;
  }

  .social-icons a i {
    font-size: 25px;
  }

  .footer-bottom {
    flex-direction: column;
    padding: 20px 0;
    margin: 0;
  }

  .footer-bottom p {
    font-size: 0.75em;
  }

  .tool-card {
    width: 200px;
    margin: 10px 5px;
  }

  .d-flex {
    flex-wrap: wrap;
    gap: 10px;
  }
}

/* For Large Screens (Min width: 1024px) */
@media (min-width: 1024px) {
  .footer-container {
    flex-direction: row;
    /* justify-content: space-between; */
    padding: 30px;
  }

  .footer-section {
    margin: 20px;
    min-width: 250px;
  }

  .footer-section h3 {
    font-size: 2.5em;
  }

  .footer-section p {
    font-size: 1.1em;
  }

  .social-icons {
    margin: 20px 0;
  }

  .social-icons a i {
    font-size: 40px;
  }

  .footer-bottom {
    flex-direction: row;
    padding: 62px 0;
    margin: 0px 60px;
  }

  .tool-card {
    width: 300px;
  }

  .d-flex {
    flex-wrap: wrap;
    justify-content: flex-start;
  }
}
