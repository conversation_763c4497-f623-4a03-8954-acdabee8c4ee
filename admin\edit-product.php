<?php
session_start();
include('includes/config.php');
error_reporting(0);

// Check if user is logged in
if (strlen($_SESSION['login']) == 0) {
    header('location:index.php');
    exit(); // Good practice to add exit after header redirect
} else {
    // Initialize message variables
    $msg = '';
    $error = '';

    if (isset($_POST['submit'])) {
        // Get and validate area ID
        $areaId = isset($_GET['id']) ? intval($_GET['id']) : 0;

        // Sanitize inputs
        $product = mysqli_real_escape_string($con, $_POST['product']);
        $description = mysqli_real_escape_string($con, $_POST['description']);

        // Fixed query syntax (removed extra comma)
        $query = mysqli_query($con, "UPDATE tblarea SET ProductName='$product', Description='$description' WHERE id='$areaId'");

        if ($query) {
            $msg = "Area Updated successfully!";
        } else {
            $error = "Something went wrong. Please try again. " . mysqli_error($con);
        }
    }
?>


    <!DOCTYPE html>
    <html lang="en">

    <head>

        <title>Celaeno Technology | Edit Product</title>

        <!-- App css -->
        <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
        <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
        <link rel="stylesheet" href="../plugins/switchery/switchery.min.css">
        <script src="assets/js/modernizr.min.js"></script>

    </head>


    <body class="fixed-left">

        <!-- Begin page -->
        <div id="wrapper">

            <!-- Top Bar Start -->
            <?php include('includes/topheader.php'); ?>
            <!-- Top Bar End -->


            <!-- ========== Left Sidebar Start ========== -->
            <?php include('includes/leftsidebar.php'); ?>
            <!-- Left Sidebar End -->

            <div class="content-page">
                <!-- Start content -->
                <div class="content">
                    <div class="container">


                        <div class="row">
                            <div class="col-xs-12">
                                <div class="page-title-box">
                                    <h4 class="page-title">Edit Product</h4>

                                    <div class="clearfix"></div>
                                </div>
                            </div>
                        </div>
                        <!-- end row -->


                        <div class="row">
                            <div class="col-sm-12">
                                <div class="card-box">
                                    <h4 class="m-t-0 header-title"><b>Edit Product </b></h4>
                                    <hr />



                                    <div class="row">
                                        <div class="col-sm-6">
                                            <!---Success Message--->
                                            <?php if ($msg) { ?>
                                                <div class="alert alert-success" role="alert">
                                                    <strong>Well done!</strong> <?php echo htmlentities($msg); ?>
                                                </div>
                                            <?php } ?>

                                            <!---Error Message--->
                                            <?php if ($error) { ?>
                                                <div class="alert alert-danger" role="alert">
                                                    <strong>Oh snap!</strong> <?php echo htmlentities($error); ?>
                                                </div>
                                            <?php } ?>


                                        </div>
                                    </div>

                                    <?php
                                    $areaId = intval($_GET['id']);
                                    $query = mysqli_query($con, "SELECT id, ProductName, Description, PostingDate, UpdationDate FROM tblarea WHERE Is_Active = 1 AND id='$areaId'");
                                    $cnt = 1;
                                    while ($row = mysqli_fetch_array($query)) {
                                    ?>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <form class="form-horizontal" name="category" method="post">
                                                    <div class="form-group">
                                                        <label class="col-md-2 control-label">Product</label>
                                                        <div class="col-md-10">
                                                            <input type="text" class="form-control"
                                                                value="<?php echo htmlentities($row['ProductName']); ?>"
                                                                name="product" required>
                                                        </div>
                                                    </div>

                                                    <div class="form-group">
                                                        <label class="col-md-2 control-label">Area Description</label>
                                                        <div class="col-md-10">
                                                            <textarea class="form-control" rows="5" name="description"
                                                                required><?php echo htmlentities($row['Description']); ?></textarea>
                                                        </div>
                                                    </div>
                                                <?php } ?>
                                                <div class="form-group">
                                                    <label class="col-md-2 control-label">&nbsp;</label>
                                                    <div class="col-md-10">

                                                        <button type="submit"
                                                            class="btn btn-custom waves-effect waves-light btn-md"
                                                            name="submit">
                                                            Update
                                                        </button>
                                                        <a href="manage-product.php" class="btn btn-custom waves-effect waves-light btn-md">
                                                            Cancel
                                                        </a>

                                                    </div>
                                                </div>

                                                </form>
                                            </div>


                                        </div>

                                </div>
                            </div>
                        </div>
                        <!-- end row -->


                    </div> <!-- container -->

                </div> <!-- content -->

                <?php include('includes/footer.php'); ?>

            </div>


        </div>
        <!-- END wrapper -->



        <script>
            var resizefunc = [];
        </script>

        <!-- jQuery  -->
        <script src="assets/js/jquery.min.js"></script>
        <script src="assets/js/bootstrap.min.js"></script>
        <script src="assets/js/detect.js"></script>
        <script src="assets/js/fastclick.js"></script>
        <script src="assets/js/jquery.blockUI.js"></script>
        <script src="assets/js/waves.js"></script>
        <script src="assets/js/jquery.slimscroll.js"></script>
        <script src="assets/js/jquery.scrollTo.min.js"></script>
        <script src="../plugins/switchery/switchery.min.js"></script>

        <!-- App js -->
        <script src="assets/js/jquery.core.js"></script>
        <script src="assets/js/jquery.app.js"></script>

    </body>

    </html>
<?php } ?>