name: PHP Unit Tests

on:
  push:
    branches:
      - master
  pull_request:

jobs:
  phpunit:
    name: PHP ${{ matrix.php-versions }} Unit Tests
    runs-on: ${{ matrix.operating-system }}
    strategy:
      matrix:
        operating-system: ['ubuntu-20.04']
        php-versions: ['7.4', '8.0', '8.1']

    steps:
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-versions }}
        coverage: none

    - uses: actions/checkout@v2

    - name: Validate composer.json and composer.lock
      run: composer validate

    - name: Get Composer Cache Directory
      id: composer-cache
      run: |
        echo "::set-output name=dir::$(composer config cache-files-dir)"

    - name: Cache Files
      uses: actions/cache@v2
      with:
        path: |
          ${{ steps.composer-cache.outputs.dir }}
          **/.php_cs.cache
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-composer-

    - name: Install dependencies
      if: steps.composer-cache.outputs.cache-hit != 'true'
      run: composer install --prefer-dist --no-progress --no-suggest

    - name: Run tests
      run: ./vendor/bin/phpunit --testdox --stop-on-failure
