<?php
session_start();
include('../dist/include/config.php');

if (!isset($_SESSION['user'])) {
    header("Location:../login.php");
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Topic</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link href="../dist/css/main.css" rel="stylesheet">
    <link rel="stylesheet" href="../dist/css/input.css">
    <link rel="stylesheet" href="../dist/css/texteditor.css">
    <link rel="stylesheet" href="../dist/css/tags.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"
        integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script src="https://cdn.ckeditor.com/4.16.2/standard/ckeditor.js"></script>
    <style>
        .btn-container-8976000 {
            display: flex;
            align-items: center;
            justify-content: end;
            gap: 1rem;

        }

        .topic-add-btn {
            width: 5rem;
            padding: 2px;
            background: #007474;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
        }

        .topic-add-btn:hover {
            background: white;
            color: #007474;
            font-weight: 700;
        }

        .topic-cancle-btn {
            width: 6rem;
            padding: 2px;
            border: 1px solid #007474;
            background: white;
            color: #007474;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
        }

        .topic-cancle-btn:hover {
            background-color: #f1f1ec;
            font-weight: 700;
        }

        .ct-help-ticket {
            display: flex;
            justify-content: center;
        }

        .ct-ticket-body {
            width: 60%;
        }

        .ct-add-topic-head {
            display: flex;
            justify-content: start;
            gap: 1rem;
            margin-bottom: 20px;
        }

        .astrick-M {
            color: red !important;
        }
    </style>
</head>

<body>
    <section class="jumbotron jumbotron-fluid text-center">
        <nav class="ct-nav" style="">
            <div class="navbar navbar-dark navbar-static-top navbar-expand-lg">
                <a href="/celaeno_helpdesk/index.php" class="navbar-brand">
                    <small>Celaeno Assist</small>
                </a>
                <div class="ct-nav-list">
                    <a href="../index.php" class="">Home</a>
                    <a href="../knowledgebase.php" class="">Knowledge Base</a>
                </div>
                <div class="ct-head-btn-01">
                    <button class="ct-help-btn">Sign In</button>
                </div>
            </div>
        </nav>

        <div class="cover"></div>

        <div class="container">
            <p class="lead">Welcome to</p>
            <h1 class="jumbotron-heading">Celaeno Assist</h1>

            <div class="row justify-content-center text-center">
                <div class="col-md-10">
                    <form id="searchForm" action="/" class="search-form" method="GET">
                        <div class="input-group search">
                            <input type="text" name="q" class="search-field" aria-label="Search field"
                                placeholder="Search anything here..." aria-required="true" required
                                autoComplete="off" />
                            <button class="input-group-addon" aria-label="Search">
                                <i class="fa fa-search">&nbsp;</i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <div class="section text-muted">
        <div class="container">
            <div class="row content justify-content-center">
                <div class="ct-help-ticket">
                    <div class="ct-ticket-body">
                        <div class="ct-add-topic-head">
                            <a href="../knowledgebase.php">
                                <img src="../dist/images/backbtn.svg" alt="back" style="width: 32px; height: 32px;">
                            </a>
                            <h3>Add Topic</h3>
                        </div>


                        <form>
                            <div class="grid-container">
                                <div class="custom-select">

                                    <select name="project_recurrence" id="">
                                        <option value="none">Type </option>
                                        <option value="Discussion">Discussion</option>
                                        <option value="Idea">Idea</option>
                                        <option value="Question">Question</option>
                                    </select>
                                </div>
                                <div class="form-input">
                                    <input type="text" id="topic_title" name="topic_title" placeholder="Topic title"
                                        required />
                                    <label for="topic_title">Topic <small class="astrick-M">*</small></label>
                                </div>
                                <h4 style="margin-bottom: 1rem;">Description <small class="astrick-M">*</small></h4>
                                <div class="form-input-des item6" style="margin-bottom: 20px;">
                                    <input type="hidden" name="texteditor" id="texteditor-hidden-AddTopic-7856sd">
                                    <div class="wsyig" id="wsyigAddTopic-7856sd"></div>
                                </div>
                                <div class="custom-select">
                                    <select name="products" id="" required>
                                        <option value="none">Product </option>
                                        <option value="celaeno_booking">Celaeno Booking</option>
                                        <option value="celaeno_invoice">Celaeno Invoice</option>
                                        <option value="celaeno_timesheet">Celaeno Timesheet</option>
                                    </select>
                                </div>
                                <div class="custom-select">

                                    <select name="project_recurrence" id="">
                                        <option value="none">Category </option>
                                        <option value="Feature request">Feature request</option>
                                        <option value="General">General</option>
                                        <option value="Getting started">Getting started</option>
                                        <option value="Suggestion">Suggestion</option>
                                        <option value="Uncategorized">Uncategorized</option>
                                    </select>
                                </div>


                                <div class="tags-input" id="addTopicTagsDiv">
                                    <input type="hidden" id="addtopic_tags" name="addtopic_tags">
                                    <ul>
                                        <input type="text" id="addtopic_tags" name="addtopic_tags" placeholder="Tags">
                                    </ul>
                                </div>

                            </div>

                            <div class="btn-container-8976000">

                                <button type="button" onclick="closePopup()" class="topic-cancle-btn">Cancel</button>
                                <button type="button" id="add-topic-btn" class="topic-add-btn">Save</button>
                            </div>
                        </form>



                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    Celaeno HelpDesk
                    <p class="mt-1"><strong><small class="text-muted">Copyright © 2024 , All Right Reserved by <a
                                    href="#">Celaeno
                                    Technology</a></small></strong></p>
                </div>
                <div class="col-md-6 text-right">
                    <p>
                        <a href="#">Back to top</a>
                    </p>
                </div>
            </div>
        </div>
    </footer>


    <script src="../dist/js/TextEditor.js"></script>
    <script src="../dist/js/TagInput.js"></script>
    <script>
        new TextEditor("wsyigAddTopic-7856sd", "texteditor-hidden-AddTopic-7856sd");
        new TagsInput("addTopicTagsDiv", "addtopic_tags");
    </script>
    <script>
        var x, i, j, l, ll, selElmnt, a, b, c;
        x = document.getElementsByClassName("custom-select");
        l = x.length;
        for (i = 0; i < l; i++) {
            selElmnt = x[i].getElementsByTagName("select")[0];
            ll = selElmnt.length;

            // Create the selected item DIV
            a = document.createElement("DIV");
            a.setAttribute("class", "select-selected");
            a.innerHTML = selElmnt.options[selElmnt.selectedIndex].innerHTML + "<small class='astrick-M'>*</small>";
            x[i].appendChild(a);

            // Create the options container DIV
            b = document.createElement("DIV");
            b.setAttribute("class", "select-items select-hide");


            // Loop through all options in the original select element
            for (j = 0; j < ll; j++) {
                c = document.createElement("DIV");
                c.innerHTML = selElmnt.options[j].innerHTML;

                if (selElmnt.options[j].disabled) {
                    c.classList.add("disabled-option");
                    c.style.color = "#aaa";
                    c.style.pointerEvents = "none";
                }

                // Add click event to each option
                c.addEventListener("click", function (e) {
                    var y, i, k, s, h, sl, yl;
                    s = this.parentNode.parentNode.getElementsByTagName("select")[0];
                    sl = s.length;
                    h = this.parentNode.previousSibling;
                    for (i = 0; i < sl; i++) {
                        if (s.options[i].innerHTML == this.innerHTML) {
                            s.selectedIndex = i;
                            h.innerHTML = this.innerHTML;
                            y = this.parentNode.getElementsByClassName("same-as-selected");
                            yl = y.length;
                            for (k = 0; k < yl; k++) {
                                y[k].removeAttribute("class");
                            }
                            this.setAttribute("class", "same-as-selected");
                            break;
                        }
                    }
                    h.click();
                });
                b.appendChild(c);
            }
            x[i].appendChild(b);

            // Add event listener to toggle dropdown
            a.addEventListener("click", function (e) {
                e.stopPropagation();
                closeAllSelect(this);
                this.nextSibling.classList.toggle("select-hide");
                this.classList.toggle("select-arrow-active");
            });
        }

        // Function to close all dropdowns
        function closeAllSelect(elmnt) {
            var x, y, i, xl, yl, arrNo = [];
            x = document.getElementsByClassName("select-items");
            y = document.getElementsByClassName("select-selected");
            xl = x.length;
            yl = y.length;
            for (i = 0; i < yl; i++) {
                if (elmnt == y[i]) {
                    arrNo.push(i)
                } else {
                    y[i].classList.remove("select-arrow-active");
                }
            }
            for (i = 0; i < xl; i++) {
                if (arrNo.indexOf(i)) {
                    x[i].classList.add("select-hide");
                }
            }
        }

        document.addEventListener("click", closeAllSelect);
    </script>
</body>

</html>