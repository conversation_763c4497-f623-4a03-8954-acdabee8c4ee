<?php
session_start();
require_once('./includes/config.php');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {

    if (isset($_POST['create-group'])) {

        // Process data from groups-create.php form
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $department_id = $_POST['department_id'] ?? '';
        $created_by = $_SESSION['logged_in_user']['id'];
        $updated_by = $_SESSION['logged_in_user']['id'];

        // Check if the required user ID is available
        if ($created_by === null || $updated_by === null) {
            echo "Error: User session information is missing.";
            exit;
        }

        // Prepare the SQL query using a prepared statement
        $sql = "INSERT INTO tblgroups (name, description, created_by, updated_by) VALUES (?, ?, ?, ?)";

        // Prepare the statement
        $stmt = $con->prepare($sql);

        if ($stmt === false) {
            // Handle preparation error
            echo "Error preparing statement: " . $con->error;
        } else {
            // Bind the parameters to the prepared statement
            $stmt->bind_param("ssii", $name, $description, $created_by, $updated_by);

            // Execute the prepared statement
            if ($stmt->execute()) {
                // Insertion was successful
                $group_id = $stmt->insert_id; // Get the ID of the newly inserted row
                // echo "Group created successfully. Group ID: " . $group_id;
                if ($department_id != '') {
                    $sql = "INSERT INTO tbldepartment_group (department_id, group_id) VALUES (?, ?)";
                    $stmt = $con->prepare($sql);
                    $stmt->bind_param("ii", $department_id, $group_id);
                    if ($stmt->execute()) {
                        // Insertion was successful
                        // echo "Group created successfully. Group ID: " . $group_id;
                    } else {
                        // Handle execution error
                        echo "Error inserting record: " . $stmt->error;
                    }
                } else {
                    // Handle execution error
                    echo "Error inserting record: " . $stmt->error;
                }

                echo `
                    <script>
                        alert('Group created successfully');
                    </script>
                `;
                header("Location:./groups-view.php");

            } else {
                // Handle execution error
                echo "Error inserting record: " . $stmt->error;
            }

            // Close the prepared statement
            $stmt->close();
        }


    } elseif (isset($_POST['update-group'])) {

        // Process data from the update form
        $group_id = $_POST['group_id'] ?? null;
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        $updated_by = $_SESSION['logged_in_user']['id'] ?? null;

        if ($group_id === null || $updated_by === null) {
            echo "Error: Group ID or user session information is missing for update.";
            exit;
        }
        $sql = "UPDATE tblgroups SET name = ?, description = ?, updated_by = ? WHERE id = ?";

        $stmt = $con->prepare($sql);

        if ($stmt === false) {
            echo "Error preparing update statement: " . $con->error;
        } else {
            $stmt->bind_param("ssii", $name, $description, $updated_by, $group_id);

            if ($stmt->execute()) {
                echo `
                    <script>
                    alert('Group updated successfully');
                    window.location.href = './groups-view.php';
                    </script>
                `;
                header("Location: ./groups-update.php?groupId=$group_id");
                exit();
            } else {
                echo "Error updating record: " . $stmt->error;
            }

            $stmt->close();
        }


    } elseif (isset($_POST['add-team-member'])) {

        $user_id_to_add = $_POST['user_id'] ?? null;
        $group_id = $_POST['group_id'] ?? null;

        if ($user_id_to_add && is_numeric($user_id_to_add)) {
            // Check again if the member already exists (redundant but safe)

            // Add the new member
            $insertSql = "INSERT INTO tblgroup_team_member (group_id, user_id) VALUES (?, ?)";
            $insertStmt = $con->prepare($insertSql);
            $insertStmt->bind_param("ii", $group_id, $user_id_to_add);
            if ($insertStmt->execute()) {
                $addMemberSuccess = "Team member added successfully.";
                // Optionally, redirect to refresh the member list and the select box
                header("Location: ./groups-update.php?groupId=$group_id");
                exit();
            } else {
                $addMemberError = "Error adding team member: " . $insertStmt->error;
            }
            $insertStmt->close();

        }


    } else {
        echo "No known form submitted.";
    }
} else if ($_SERVER['REQUEST_METHOD'] === 'GET') {

    if (isset($_GET['remove_member']) && isset($_GET['groupId']) && is_numeric($_GET['remove_member']) && is_numeric($_GET['groupId'])) {
        $member_to_remove = $_GET["remove_member"];
        $group_id = $_GET["groupId"];

        // Prepare the SQL query to remove the member
        $deleteSql = "DELETE FROM tblgroup_team_member WHERE group_id = ? AND user_id = ?";
        $deleteStmt = $con->prepare($deleteSql);
        if ($deleteStmt === false) {
            echo "Error preparing delete statement: " . $con->error;
        } else {
            $deleteStmt->bind_param("ii", $group_id, $member_to_remove);
            if ($deleteStmt->execute()) {
                echo `
                    <script>
                        alert('Team member removed successfully.');
                        window.location.href = './groups-update.php?groupId=$group_id';
                    </script>
                `;
                header("Location: ./groups-update.php?groupId=$group_id");
            } else {
                echo "Error removing team member: " . $deleteStmt->error;
            }
            $deleteStmt->close();
        }
    }

} else {
    echo "This script only handles POST requests.";
}

?>