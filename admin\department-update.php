<?php
require_once('./includes/config.php');
require_once("./utils/groups-functions.php");


$department_id = $_GET['departmentId'];
// $group = getGroupById($con, $group_id);
// Function to fetch department details by ID (if needed)
function getDepartmentById($con, $department_id) {
    $sql = "SELECT * FROM tbldepartment WHERE id = ?";
    $stmt = $con->prepare($sql);
    $stmt->bind_param("i", $department_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $department = $result->fetch_assoc();
    $stmt->close();
    return $department;
}

$department = getDepartmentById($con, $department_id);

// Fetch current groups in the department (assuming a table like tbldepartment_groups)
$currentGroupsSql = "SELECT dg.group_id
                     FROM tbldepartment_group dg
                     WHERE dg.department_id = ?";
$currentGroupsStmt = $con->prepare($currentGroupsSql);
$currentGroupsStmt->bind_param("i", $department_id);
$currentGroupsStmt->execute();
$currentGroupsResult = $currentGroupsStmt->get_result();
$currentGroupIds = [];
while ($row = $currentGroupsResult->fetch_assoc()) {
    $currentGroupIds[] = $row['group_id'];
}
$currentGroupsStmt->close();

// Fetch all groups, excluding the ones already in the department
$allGroupsSql = "SELECT id AS id, name AS name FROM tblgroups";
if (!empty($currentGroupIds)) {
    $allGroupsSql .= " WHERE id NOT IN (" . implode(',', array_map('intval', $currentGroupIds)) . ")";
}
$allGroupsResult = $con->query($allGroupsSql);
$availableGroups = [];
if ($allGroupsResult) {
    while ($row = $allGroupsResult->fetch_assoc()) {
        $availableGroups[$row['id']] = $row['name'];
    }
    $allGroupsResult->free();
} else {
    error_log("Error fetching available groups: " . $con->error);
}


// Fetch current groups for the table display
$currentGroupsSql = "SELECT dg.group_id AS id, g.name AS group_name
                     FROM tbldepartment_group dg
                     JOIN tblgroups g ON dg.group_id = g.id
                     WHERE dg.department_id = ?";
$currentGroupsStmt = $con->prepare($currentGroupsSql);
$currentGroupsStmt->bind_param("i", $department_id);
$currentGroupsStmt->execute();
$currentGroupsResult = $currentGroupsStmt->get_result();
$currentGroupsInDepartment = $currentGroupsResult->fetch_all(MYSQLI_ASSOC);
$currentGroupsStmt->close();

?>

<head>

    <title> Manage Resources</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
    <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="../plugins/switchery/switchery.min.css">
    <script src="assets/js/modernizr.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
        integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        /* Table css */
        .table-gen {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-family: Arial, sans-serif;
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            overflow: hidden;
        }

        .table-gen th,
        .table-gen td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .table-gen th {
            background-color: #007474;
            color: white;
            font-weight: 600;
        }

        .table-gen td {
            color: #333;
        }

        .table-gen tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .table-gen tr:hover {
            background-color: #f1f3f5;
            transition: background-color 0.2s ease;
        }

        /* End */
    </style>

</head>

<body class="fixed-left">

    <!-- Begin page -->
    <div id="wrapper">

        <!-- Top Bar Start -->
        <?php include('includes/topheader.php'); ?>

        <!-- ========== Left Sidebar Start ========== -->
        <?php include('includes/leftsidebar.php'); ?>
        <!-- Left Sidebar End -->

        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->
        <div class="content-page">
            <!-- Start content -->
            <div class="content">
                <div class="container">

                    <div class="row">
                        <div class="col-xs-12">
                            <div class="page-title-box">
                                <ol class="breadcrumb p-0 m-0">
                                    <li>
                                        <a href="./admin-page.php">Admin</a>
                                    </li>
                                    <li>
                                        <a href="./department-view.php">Department</a>
                                    </li>

                                    <li class="active">
                                        Update Department
                                    </li>
                                </ol>
                                <div class="clearfix"></div>
                            </div>
                        </div>

                    </div>


                    <!-- Groups containers -->
                    <section class="row">
                        <div class="col-sm-12">
                            <form action="./department-operations.php" style="width: 30%;" method="POST">
                                <input type="hidden" name="department_id"
                                    value="<?php echo htmlspecialchars($department['id']); ?>">
                                <div class="form-group">
                                    <label for="name">Name</label>
                                    <input type="text" name="name" id="name" class="form-control"
                                        value="<?php echo $department['name']; ?>">
                                </div>
                                <div class="form-group">
                                    <label for="description">Description</label>
                                    <textarea name="description" id="description"
                                        class="form-control"><?php echo $department['description']; ?></textarea>
                                </div>
                                <div class="form-group">
                                    <button type="submit" name="update-department"
                                        class="btn btn-success waves-effect waves-light">
                                        Save
                                    </button>
                                </div>
                            </form>
                        </div>
                        <div class="col-sm-12">
                            <div class="">

                                <div class="add-member-form">
                                    <form action="./department-operations.php" method="post">
                                        <input type="hidden" name="department_id"
                                            value="<?php echo htmlspecialchars($department_id); ?>">
                                        <label for="user_id">Select Groups:</label>
                                        <select name="group_id" id="user_id">
                                            <option value="">-- Select Group --</option>
                                            <?php if (!empty($availableGroups)): ?>
                                                <?php foreach ($availableGroups as $group_id => $group_name): ?>
                                                    <option value="<?php echo htmlspecialchars($group_id); ?>">
                                                        <?php echo htmlspecialchars($group_name); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <option value="">No group available to add</option>
                                            <?php endif; ?>
                                        </select>
                                        <button type="submit" name="add-group">Add Group</button>
                                    </form>
                                    <?php if (isset($addMemberError)): ?>
                                        <p style="color: red;"><?php echo htmlspecialchars($addMemberError); ?></p>
                                    <?php endif; ?>
                                    <?php if (isset($addMemberSuccess)): ?>
                                        <p style="color: green;"><?php echo htmlspecialchars($addMemberSuccess); ?></p>
                                    <?php endif; ?>
                                </div>

                                <div class="current-members-table">
                                    <h4>Groups</h4>
                                    <?php if (!empty($currentGroupsResult)): ?>
                                        <table class="table-gen">
                                            <thead>
                                                <tr>
                                                    <th>Group ID</th>
                                                    <th>Name</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($currentGroupsInDepartment as $group): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($group['id']); ?></td>
                                                        <td><?php echo htmlspecialchars($group['group_name']); ?></td>
                                                        <td><a
                                                                href="./department-operations.php?departmentId=<?php echo htmlspecialchars($department_id); ?>&remove_group=<?php echo htmlspecialchars($group['id']); ?>">Remove</a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    <?php else: ?>
                                        <p>No groups added yet.</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </section>



                    <script>
                        var resizefunc = [];
                    </script>

                    <!-- jQuery  -->
                    <script src="assets/js/jquery.min.js"></script>
                    <script src="assets/js/bootstrap.min.js"></script>
                    <script src="assets/js/detect.js"></script>
                    <script src="assets/js/fastclick.js"></script>
                    <script src="assets/js/jquery.blockUI.js"></script>
                    <script src="assets/js/waves.js"></script>
                    <script src="assets/js/jquery.slimscroll.js"></script>
                    <script src="assets/js/jquery.scrollTo.min.js"></script>
                    <script src="../plugins/switchery/switchery.min.js"></script>

                    <!-- App js -->
                    <script src="assets/js/jquery.core.js"></script>
                    <script src="assets/js/jquery.app.js"></script>

</body>