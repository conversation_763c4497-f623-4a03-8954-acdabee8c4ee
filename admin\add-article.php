<?php

session_start();
include('includes/config.php');
$msg = "";
$error = "";
// error_reporting(0);
if (strlen($_SESSION['login']) == 0) {
  header('location:index.php');
} else {
  // For adding article
  if (isset($_POST['submit'])) {
    $articletitle = mysqli_real_escape_string($con, $_POST['articletitle']); // Escape input
    $areaid = intval($_POST['area']); // Ensure integer
    $catid = intval($_POST['category']); // Ensure integer
    $subcatid = intval($_POST['subcategory']); // Ensure integer
    $articledetails = $con->real_escape_string($_POST['articledescription']); // Escape input
    $status = 1;

    // Basic validation (RECOMMENDED)
    if (empty($articletitle) || empty($areaid) || empty($catid) || empty($subcatid) || empty($articledetails)) {
      $error = "All fields are required.";
    } else {

      $query = mysqli_query($con, "INSERT INTO tblarticles (ArticleTitle, AreaId, CategoryId, SubCategoryId, ArticleDetails, Is_Active) 
                                            VALUES ('$articletitle', '$areaid', '$catid', '$subcatid', '$articledetails', '$status')");
      if ($query) {
        $msg = "Article successfully added ";
      } else {
        $error = "Something went wrong. Please try again.  Error: " . mysqli_error($con); //Show detailed error
      }
    }
  }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="A fully featured admin theme which can be used to build CRM, CMS, etc.">


  <!-- App favicon -->
  <link rel="shortcut icon" href="assets/images/favicon.ico">
  <!-- App title -->
  <title>Celaeno Technology | Add Article</title>

  <!-- Quill.js stylesheet -->
  <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">

  <!-- App css -->
  <link href="assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/core.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/components.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/icons.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/pages.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/menu.css" rel="stylesheet" type="text/css" />
  <link href="assets/css/responsive.css" rel="stylesheet" type="text/css" />






  <!-- Modernizr js -->
  <script src="assets/js/modernizr.min.js"></script>
</head>

<body class="fixed-left">

  <!-- Begin page -->
  <div id="wrapper">
    <!-- Top Bar Start -->
    <?php include('includes/topheader.php'); ?>
    <!-- ========== Left Sidebar Start ========== -->
    <?php include('includes/leftsidebar.php'); ?>
    <!-- Left Sidebar End -->

    <!-- Start right Content here -->
    <div class="content-page">
      <!-- Start content -->
      <div class="content">
        <div class="container">
          <div class="row">
            <div class="col-xs-12">
              <div class="page-title-box">

                <ol class="breadcrumb p-0 m-0">
                  <li>
                    <a href="./knowledge-base.php">Knowledge Base</a>
                  </li>


                  <li class="active">
                    Add Article
                  </li>
                </ol>
                <hr />


                <div class="row">
                  <div class="col-sm-6">
                    <?php if ($msg) { ?>
                      <div class="alert alert-success" role="alert">
                        <strong>Well done!</strong> <?php echo htmlentities($msg); ?>
                      </div>
                    <?php } ?>
                    <?php if ($error) { ?>
                      <div class="alert alert-danger" role="alert">
                        <strong>Oh snap!</strong> <?php echo htmlentities($error); ?>
                      </div>
                    <?php } ?>
                  </div>
                </div>




                <div class="row">
                  <div class="col-md-10 col-md-offset-1">
                    <div class="p-6">
                      <div class="">
                        <form name="addarticle" method="POST" enctype="multipart/form-data" id="articleForm">
                          <div class="form-group m-b-20">
                            <label for="exampleInputEmail1">Article Title</label>
                            <input type="text" class="form-control" id="articletitle" name="articletitle"
                              placeholder="Enter title" required>
                          </div>

                          <div class="form-group m-b-20">
                            <label for="exampleInputEmail1">Product Name</label>
                            <select class="form-control" name="area" id="area" required>
                              <option value="">Select Product</option>
                              <?php
                              // Fetching active products
                              $ret = mysqli_query($con, "SELECT id, ProductName FROM tblarea WHERE Is_Active=1");
                              while ($result = mysqli_fetch_array($ret)) {
                              ?>
                                <option value="<?php echo htmlentities($result['id']); ?>">
                                  <?php echo htmlentities($result['ProductName']); ?>
                                </option>
                              <?php } ?>
                            </select>
                          </div>

                          <div class="form-group m-b-20">
                            <label for="exampleInputEmail1">Category</label>
                            <select class="form-control" name="category" id="category" required>
                              <option value="">Select Category</option>
                            </select>
                          </div>
                          <div class="form-group m-b-20">
                            <label for="subcategory">Subcategory</label>
                            <select class="form-control" name="subcategory" id="subcategory" required>
                              <option value="">Select Subcategory</option>
                            </select>
                          </div>

                          <div class="form-group1 m-b-20">
                            <label for="Accessibility">Accessibility</label><br>
                            <label>
                              <input type="radio" name="accessibility" value="public"> Public
                            </label><br>
                            <label>
                              <input type="radio" name="accessibility" value="internal"> Internal
                            </label><br>
                            <label>
                              <input type="radio" name="accessibility" value="private"> Private
                            </label>
                          </div>



                          <div class="row">
                            <div class="col-sm-12">
                              <div class="card-box">
                                <h4 class="m-b-30 m-t-0 header-title"><b>Article Details</b></h4>
                                <!-- Quill.js editor container -->
                                <div id="editor"></div>
                                <input type="hidden" name="articledescription" id="articledescription">
                              </div>
                            </div>
                          </div>

                          <!-- <div class="row">
                      <div class="col-sm-12">
                        <div class="card-box">
                          <h4 class="m-b-30 m-t-0 header-title"><b>Feature Image</b></h4>
                          <input type="file" class="form-control" id="articleimage" name="articleimage"  required>
                        </div>
                      </div>
                    </div> -->

                          <button type="submit" name="submit" class="btn btn-success waves-effect waves-light"
                            onclick="saveArticle()">Save Article</button>
                          <button type="button" class="btn btn-danger waves-effect waves-light"
                            onclick="discardArticle()">Discard</button>
                        </form>
                      </div>
                    </div> <!-- end p-20 -->
                  </div> <!-- end col -->
                </div>
                <!-- end row -->
              </div> <!-- container -->
            </div> <!-- content -->
          </div>
        </div>
      </div>


      <?php include('includes/footer.php'); ?>

    </div>
    <!-- ============================================================== -->
    <!-- End Right content here -->
    <!-- ============================================================== -->

  </div>
  <!-- END wrapper -->



  <script>
    function saveArticle() {
      // Get the HTML content from the Quill editor
      var articleDescriptionHTML = document.querySelector('#editor .ql-editor').innerHTML;
      // Set the value of the hidden input field with the Quill editor content
      document.getElementById("articledescription").value = articleDescriptionHTML;
      // Submit the form
      document.getElementById("articleForm").submit();
    }

    function discardArticle() {
      // Redirect to main page
      window.history.back();
    }
  </script>
  <!-- jQuery  -->
  <script src="assets/js/jquery.min.js"></script>
  <script src="assets/js/bootstrap.min.js"></script>
  <script src="assets/js/detect.js"></script>
  <script src="assets/js/fastclick.js"></script>
  <script src="assets/js/jquery.blockUI.js"></script>

  <script src="assets/js/jquery.slimscroll.js"></script>


  <!-- Quill.js script -->
  <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>

  <!-- App js -->
  <script src="assets/js/jquery.core.js"></script>
  <script src="assets/js/jquery.app.js"></script>

  <script>
    // Initialize Quill.js editor
    var quill = new Quill('#editor', {
      theme: 'snow', // Snow theme for a clean and simple interface
      modules: {
        toolbar: [
          ['bold', 'italic', 'underline', 'strike'], // Basic formatting buttons
          [{
            'header': 1
          }, {
            'header': 2
          }], // Headings
          [{
            'list': 'ordered'
          }, {
            'list': 'bullet'
          }], // Numbered and bullet lists
          ['link', 'image', 'video'], // Image and video embedding
          //   [{ 'align': [] }],                         // Alignment options
          //   [{ 'size': ['small', false, 'large', 'huge'] }] // Font size options
          [{
            'color': []
          }, {
            'background': []
          }], // Text and background color

          [{
            'indent': '-1'
          }, {
            'indent': '+1'
          }], // Indentation
          [{
            'script': 'sub'
          }, {
            'script': 'super'
          }], // Subscript and superscript
          ['clean']
        ]
      }
    });
  </script>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script>
    $(document).ready(function() {
      $('#area').change(function() {
        var areaId = $(this).val();
        if (areaId !== '') {
          $.ajax({
            type: 'POST',
            url: 'fetch_category.php',
            data: {
              area_id: areaId
            },
            success: function(response) {
              $('#category').html(response);
              $('#subcategory').html('<option value="">Select Subcategory</option>');
            }
          });
        } else {
          $('#category').html('<option value="">Select Category</option>');
          $('#subcategory').html('<option value="">Select Subcategory</option>');
        }
      });
      $('#category').change(function() {
        var categoryId = $(this).val();
        if (categoryId !== '') {
          $.ajax({
            type: 'POST',
            url: 'fetch_subcategory.php',
            data: {
              category_id: categoryId
            },
            success: function(response) {
              $('#subcategory').html(response);
            }
          });
        } else {
          $('#subcategory').html('<option value="">Select Subcategory</option>');
        }
      });
    });
  </script>


</body>

</html>