<?php
session_start();
if (!isset($_SESSION['user'])) {
    header("Location: index.php");
    exit;
}

include "./admin/includes/config.php";
$user = $_SESSION['user'];


$sql = "SELECT * FROM tickets WHERE email = ?";
$stmt = $con->prepare($sql);


$stmt->bind_param("s", $user['email']);

$stmt->execute();

$result = $stmt->get_result();
$loggedInUserEmail = $_SESSION['user']['email'];
$loggedInUserFirstName = explode(" ", $_SESSION['user']['name'])[0];
$loggedInUserLastName = explode(" ", $_SESSION['user']['name'])[1];


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Client Portal - Home</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
        integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="./dist/css/input.css" rel="stylesheet">
    <link href="./dist/css/texteditor.css" rel="stylesheet">
    <style>
        .form-container {
            position: fixed;
            top: 0;
            right: -500px;
            /* Hidden by default */
            width: 500px;
            height: 100%;
            background-color: #f4f4f4;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.3);
            transition: right 0.3s ease-in-out;
            padding: 20px;
            box-sizing: border-box;
        }

        .form-container.active {
            right: 0;
            overflow-y: auto;
            /* Slide in */
        }

        .ticket-form-head-mnsd889 {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;

        }

        .ticket-form-head-mnsd889 i {
            font-size: 1.6rem;
            cursor: pointer;
        }

        .topic-add-btn {
            width: 5rem;

            background: #007474;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
        }

        .topic-add-btn:hover {
            background: white;
            color: #007474;
            font-weight: 700;
        }

        .topic-cancle-btn {
            width: 6rem;

            border: 1px solid #007474;
            background: white;
            color: #007474;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
        }

        .topic-cancle-btn:hover {
            background-color: #f1f1ec;
            font-weight: 700;
        }

        form {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        input,
        button {
            padding: 10px;
            margin: 5px 0;
        }

        button {
            cursor: pointer;
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px;
        }

        button:hover {
            background-color: #0056b3;
        }



        /* Reset default styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }

        /* Body styles */
        body {
            background-color: #f1f1ec;
            /* Secondary color */
            color: #333;
            line-height: 1.6;
        }

        .ticket-sub-heading-098k {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .ticket-sub-heading-098k button {
            background-color: #007474;
        }

        /* Header styles */
        .header {
            background-color: #007474;
            /* Primary color */
            color: white;
            padding: 1rem 2rem;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .welcome {
            font-size: 1.5rem;
            font-weight: 600;
        }

        /* Container styles */
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        h2 {
            color: #007474;
            font-size: 1.8rem;
        }

        /* Ticket table styles */
        .ticket-table {
            width: 100%;
            border-collapse: collapse;
            background-color: white;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            border-radius: 8px;
            overflow: hidden;
        }

        .ticket-table th,
        .ticket-table td {
            padding: 1rem;
            text-align: left;
        }

        .ticket-table th {
            background-color: #007474;
            color: white;
            font-weight: 600;
        }

        .ticket-table tr {
            transition: background-color 0.3s ease;
        }

        .ticket-table tr:hover {
            background-color: #f5f5f5;
        }

        .ticket-table a {
            color: #007474;
            text-decoration: none;
            font-weight: 600;
        }

        .ticket-table a:hover {
            text-decoration: underline;
        }

        .status-open {
            color: #007474;
            font-weight: 600;
        }

        .status-closed {
            color: #666;
            font-weight: 600;
        }

        /* Filters */
        .filter-section {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-group label {
            font-weight: bold;
        }

        .filter-group select,
        .filter-group input {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>

</head>

<body>
    <header class="header">
        <div class="welcome">Welcome, <?php echo $user['name']; ?></div>
    </header>
    <main class="container">
        <div class="ticket-sub-heading-098k">
            <h2>Your Tickets</h2>
            <div>

                <button id="openFormBtn">Create Ticket</button>
            </div>

        </div>
        <div class="filter-section">
            <div class="filter-group">
                <label for="priorityFilter">Priority:</label>
                <select id="priorityFilter">
                    <option value="all">All</option>
                    <option value="URGENT">Urgent</option>
                    <option value="HIGH">High</option>
                    <option value="MEDIUM">Medium</option>
                    <option value="LOW">Low</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="statusFilter">Status:</label>
                <select id="statusFilter">
                    <option value="all">All</option>
                    <option value="New">New</option>
                    <option value="Open">Open</option>
                    <option value="Closed">Closed</option>
                    <option value="In Progress">In Progress</option>
                </select>
            </div>
            <div class="filter-group">
                <label for="searchInput">Search:</label>
                <input type="text" id="searchInput" placeholder="Search by subject or name...">
            </div>
        </div>
        <table class="ticket-table">
            <thead>
                <tr>
                    <th>Ticket ID</th>
                    <th>Subject</th>
                    <th>Name</th>
                    <th>Status</th>
                    <th>Priority</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($result->num_rows > 0) {
                    while ($row = $result->fetch_assoc()) {
                        // Determine status class
                        $statusClass = ($row['status'] === 'Open') ? 'status-open' : 'status-closed';
                ?>
                        <tr>
                            <td><a href="openticket.php?ticketid=<?php echo $row['id']; ?>"><?php echo $row['id']; ?></a></td>
                            <td><?php echo htmlspecialchars($row['subject']); ?></td>
                            <td><?php echo htmlspecialchars($row['first_name']) . " " . htmlspecialchars($row['last_name']); ?>
                            <td><?php echo htmlspecialchars($row['priority']); ?></td>
                            </td>
                            <td class="<?php echo $statusClass; ?>"><?php echo htmlspecialchars($row['status']); ?></td>
                        </tr>
                <?php
                    }
                } else {
                    echo '<tr><td colspan="4">No tickets found</td></tr>';
                }
                ?>
            </tbody>
        </table>
    </main>
    <div class="form-container" id="formContainer">
        <div class="ticket-form-head-mnsd889">
            <h2>Contact Form</h2>
            <div id="closeFormBtn">
                <i class="fa-solid fa-xmark"></i>
            </div>

        </div>
        <form method="POST" action="./pages/ticket_submit.php" enctype="multipart/form-data">
            <input type="hidden" name="source" value="KNOWLEDGE_BASE">
            <input type="hidden" name="channel" value="Web Form">
            <input type="hidden" name="redirect_url" value="../home.php">
            <div class="grid-container">
                <div class="form-input">
                    <input type="text" id="first_name" name="first_name" placeholder="First Name"
                        value="<?php echo $loggedInUserFirstName; ?>" required readonly />
                    <label for="first_name">First Name</label>
                </div>
                <div class="form-input">
                    <input type="text" id="last_name" name="last_name" placeholder="Last Name"
                        value="<?php echo $loggedInUserLastName; ?>" required readonly />
                    <label for="last_name">Last Name</label>
                </div>
                <div class="form-input">
                    <input type="email" id="email" name="email" placeholder="Email"
                        value="<?php echo $loggedInUserEmail; ?>" required readonly />
                    <label for="email">Email</label>
                </div>
                <div class="custom-select">
                    <select name="products" id="" required>
                        <option value="none">Product</option>
                        <option value="celaeno_booking">Celaeno Booking</option>
                        <option value="celaeno_invoice">Celaeno Invoice</option>
                        <option value="celaeno_timesheet">Celaeno Timesheet</option>
                    </select>
                </div>
                <div class="form-input">
                    <input type="text" id="subject" name="subject" placeholder="Subject" required />
                    <label for="subject">Subject</label>
                </div>
                <div class="form-input">
                    <input type="tel" id="phone" name="phone" placeholder="Phone" required />
                    <label for="Phone">Phone</label>
                </div>
                <h4 style="margin-bottom: 1rem;">Description</h4>
                <div class="form-input-des item6" style="margin-bottom: 20px;">
                    <input type="hidden" name="texteditor" id="texteditor-hidden-Ticket-32ik45">
                    <div class="wsyig" id="wsyigTicket-32ik45"></div>
                </div>
                <div class="custom-select">
                    <select name="priority" id="" required>
                        <option value="NONE">Priority</option>
                        <option value="URGENT">Urgent</option>
                        <option value="HIGH">High</option>
                        <option value="MEDIUM">Medium</option>
                        <option value="LOW">Low</option>
                    </select>
                </div>
                <div class="custom-select">
                    <select name="request_type" id="" required>
                        <option value="NONE">Request Type</option>
                        <option value="QUESTION">Question</option>
                        <option value="NOT_WORKING">Something isn't working</option>
                        <option value="PAYMENT">Payment</option>
                        <option value="SUBSCRIPTION">Subscription</option>
                        <option value="OTHER">Other</option>
                    </select>
                </div>

            </div>
            <div class="form-input">
                <input type="file" class="form-control" id="attachment" name="attachment">
                <label for="attachment" class="form-label">Attachment</label>
            </div>
            <div class="btn-container-8976000">

                <button type="button" id="closeFormBtn" class="topic-cancle-btn">Cancel</button>
                <button type="submit" class="topic-add-btn">Save</button>
            </div>

        </form>
    </div>

    <script src="./dist/js/TextEditor.js"></script>
    <script>
        new TextEditor("wsyigTicket-32ik45", "texteditor-hidden-Ticket-32ik45");
    </script>
    <script>
        document.getElementById("openFormBtn").addEventListener("click", () => {
            document.getElementById("formContainer").classList.add("active");
        });

        document.getElementById("closeFormBtn").addEventListener("click", () => {
            document.getElementById("formContainer").classList.remove("active");
        });
        document.addEventListener('DOMContentLoaded', () => {
            const priorityFilter = document.getElementById('priorityFilter');
            const statusFilter = document.getElementById('statusFilter');
            const searchInput = document.getElementById('searchInput');
            const tableRows = document.querySelectorAll('.ticket-table tbody tr');

            function filterTable() {
                const priority = priorityFilter.value.toLowerCase();
                const status = statusFilter.value.toLowerCase();
                const search = searchInput.value.toLowerCase();

                tableRows.forEach(row => {
                    const priorityCell = row.cells[3].textContent.toLowerCase(); // Priority column
                    const statusCell = row.cells[4].textContent.toLowerCase(); // Status column
                    const subjectCell = row.cells[1].textContent.toLowerCase(); // Subject column
                    const nameCell = row.cells[2].textContent.toLowerCase(); // Name column

                    const matchesPriority = priority === 'all' || priorityCell === priority;
                    const matchesStatus = status === 'all' || statusCell === status;
                    const matchesSearch = subjectCell.includes(search) || nameCell.includes(search);

                    row.style.display = matchesPriority && matchesStatus && matchesSearch ? '' : 'none';
                });
            }

            priorityFilter.addEventListener('change', filterTable);
            statusFilter.addEventListener('change', filterTable);
            searchInput.addEventListener('input', filterTable);
        });
    </script>
    <script>
        var x, i, j, l, ll, selElmnt, a, b, c;
        x = document.getElementsByClassName("custom-select");
        l = x.length;
        for (i = 0; i < l; i++) {
            selElmnt = x[i].getElementsByTagName("select")[0];
            ll = selElmnt.length;

            // Create the selected item DIV
            a = document.createElement("DIV");
            a.setAttribute("class", "select-selected");
            a.innerHTML = selElmnt.options[selElmnt.selectedIndex].innerHTML;
            x[i].appendChild(a);

            // Create the options container DIV
            b = document.createElement("DIV");
            b.setAttribute("class", "select-items select-hide");

            // Loop through all options in the original select element
            for (j = 0; j < ll; j++) {
                c = document.createElement("DIV");
                c.innerHTML = selElmnt.options[j].innerHTML;

                if (selElmnt.options[j].disabled) {
                    c.classList.add("disabled-option");
                    c.style.color = "#aaa";
                    c.style.pointerEvents = "none";
                }

                // Add click event to each option
                c.addEventListener("click", function(e) {
                    var y, i, k, s, h, sl, yl;
                    s = this.parentNode.parentNode.getElementsByTagName("select")[0];
                    sl = s.length;
                    h = this.parentNode.previousSibling;
                    for (i = 0; i < sl; i++) {
                        if (s.options[i].innerHTML == this.innerHTML) {
                            s.selectedIndex = i;
                            h.innerHTML = this.innerHTML;
                            y = this.parentNode.getElementsByClassName("same-as-selected");
                            yl = y.length;
                            for (k = 0; k < yl; k++) {
                                y[k].removeAttribute("class");
                            }
                            this.setAttribute("class", "same-as-selected");
                            break;
                        }
                    }
                    h.click();
                });
                b.appendChild(c);
            }
            x[i].appendChild(b);

            // Add event listener to toggle dropdown
            a.addEventListener("click", function(e) {
                e.stopPropagation();
                closeAllSelect(this);
                this.nextSibling.classList.toggle("select-hide");
                this.classList.toggle("select-arrow-active");
            });
        }

        // Function to close all dropdowns
        function closeAllSelect(elmnt) {
            var x, y, i, xl, yl, arrNo = [];
            x = document.getElementsByClassName("select-items");
            y = document.getElementsByClassName("select-selected");
            xl = x.length;
            yl = y.length;
            for (i = 0; i < yl; i++) {
                if (elmnt == y[i]) {
                    arrNo.push(i)
                } else {
                    y[i].classList.remove("select-arrow-active");
                }
            }
            for (i = 0; i < xl; i++) {
                if (arrNo.indexOf(i)) {
                    x[i].classList.add("select-hide");
                }
            }
        }

        document.addEventListener("click", closeAllSelect);
    </script>
</body>

</html>