body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f0f0f0;
}

.login-container {
    width: 300px;
    margin: 100px auto;
    padding: 20px;
    background: white;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
}

.form-group input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

button {
    width: 100%;
    padding: 10px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

button:hover {
    background: #0056b3;
}

.error {
    color: red;
    margin-top: 10px;
}

.home-container {
    display: flex;
    height: 100vh;
}

.sidebar {
    width: 300px;
    background: #fff;
    padding: 20px;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.main-content {
    flex: 1;
    padding: 20px;
}

.ticket-item {
    padding: 10px;
    margin: 5px 0;
    background: #f8f9fa;
    cursor: pointer;
    border-radius: 4px;
}

.ticket-item:hover {
    background: #e9ecef;
}

.conversation {
    height: 400px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    margin-bottom: 20px;
    background: white;
}

.message {
    margin: 10px 0;
    padding: 10px;
    border-radius: 4px;
}

.message.client {
    background: #e3f2fd;
    margin-left: 20px;
}

.message.admin {
    background: #f8d7da;
    margin-right: 20px;
}

textarea {
    width: 100%;
    height: 100px;
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.logout-btn {
    display: block;
    margin-top: 20px;
    color: #dc3545;
    text-decoration: none;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: #f4f7fa;
    color: #333;
    line-height: 1.6;
}

/* Dashboard Layout */
.dashboard {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: #fff;
    padding: 20px;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    margin-bottom: 30px;
}

.sidebar-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
}

.sidebar-header p {
    font-size: 0.9rem;
    opacity: 0.8;
}

.ticket-section h3 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    font-weight: 500;
}

.ticket-list {
    margin-bottom: 20px;
}

.ticket-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 12px;
    margin-bottom: 10px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.ticket-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
}

/* .logout-btn {
    display: block;
    color: #e74c3c;
    text-decoration: none;
    font-weight: 500;
    padding: 10px;
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    transition: background 0.3s ease;
} */

.logout-btn:hover {
    background: rgba(231, 76, 60, 0.2);
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    padding: 30px;
}

.main-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.main-header h1 {
    font-size: 2rem;
    font-weight: 600;
    color: #2c3e50;
}

.user-info {
    background: #fff;
    padding: 8px 15px;
    border-radius: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.user-info span {
    font-size: 0.9rem;
    color: #7f8c8d;
}

/* Conversation Section */
.conversation-section {
    background: #fff;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.conversation-section h2 {
    font-size: 1.4rem;
    margin-bottom: 20px;
    color: #34495e;
}

.conversation-box {
    height: 400px;
    overflow-y: auto;
    background: #f9fbfc;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #eceff1;
}

.message {
    margin: 10px 0;
    padding: 12px 15px;
    border-radius: 8px;
    max-width: 70%;
}

.message.client {
    background: #3498db;
    color: #fff;
    margin-left: auto;
}

.message.admin {
    background: #ecf0f1;
    color: #333;
    margin-right: auto;
}

.message small {
    display: block;
    font-size: 0.8rem;
    opacity: 0.7;
    margin-top: 5px;
}

.message-form {
    display: flex;
    gap: 10px;
}

.message-form textarea {
    flex: 1;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    resize: none;
    font-family: 'Poppins', sans-serif;
    font-size: 0.9rem;
    background: #f9fbfc;
    transition: border-color 0.3s ease;
}

.message-form textarea:focus {
    border-color: #3498db;
    outline: none;
}

.send-btn {
    padding: 12px 25px;
    background: #3498db;
    color: #fff;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.3s ease;
}

.send-btn:hover {
    background: #2980b9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        padding-bottom: 80px;
    }

    .main-content {
        margin-left: 0;
        padding: 20px;
    }

    .main-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .conversation-box {
        height: 300px;
    }

    .message-form {
        flex-direction: column;
    }

    .send-btn {
        width: 100%;
    }
}

/* ... Previous styles remain unchanged ... */

/* Logout Button */
.logout-btn {
    display: block;
    color: #fff;
    background: #e74c3c;
    border: none;
    font-weight: 500;
    padding: 12px;
    text-align: center;
    border-radius: 8px;
    
    cursor: pointer;
    transition: background 0.3s ease;
}

.logout-btn:hover {
    background: #c0392b;
}