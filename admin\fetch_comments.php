<?php
include('includes/config.php');

if (isset($_GET['article_id'])) {
    $articleId = intval($_GET['article_id']);

    $sql = "SELECT name, description, created_at FROM tblcomments WHERE article_id = ? ORDER BY created_at DESC";
    $stmt = $con->prepare($sql);
    $stmt->bind_param("i", $articleId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        while ($comment = $result->fetch_assoc()) {
            echo '<div class="comment-box">';
            echo '<strong>' . htmlspecialchars($comment['name']) . ':</strong>';
            echo '<p>' . nl2br(htmlspecialchars($comment['description'])) . '</p>';
            echo '<small>' . date("F j, Y, g:i a", strtotime($comment['created_at'])) . '</small>';
            echo '<hr>';
            echo '</div>';
        }
    } else {
        echo "<p>No comments yet. Be the first to comment!</p>";
    }

    $stmt->close();
    $con->close();
} else {
    echo "<p>Error: Article ID not provided.</p>";
}
