name: PHP Code Coverage

on:
  push:
    branches:
      - master
  pull_request:

jobs:
  coverage:
    name: Coverage
    runs-on: ${{ matrix.operating-system }}
    strategy:
      matrix:
        operating-system: ['ubuntu-20.04']
        php-versions: ['8.1']

    steps:
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ matrix.php-versions }}
        coverage: xdebug

    - uses: actions/checkout@v2

    - name: Validate composer.json and composer.lock
      run: composer validate

    - name: Get Composer Cache Directory
      id: composer-cache
      run: |
        echo "::set-output name=dir::$(composer config cache-files-dir)"

    - name: Cache Files
      uses: actions/cache@v2
      with:
        path: |
          ${{ steps.composer-cache.outputs.dir }}
          **/.php_cs.cache
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-composer-
    - name: Install dependencies
      if: steps.composer-cache.outputs.cache-hit != 'true'
      run: composer install --prefer-dist --no-progress --no-suggest

    - name: Run tests
      uses: paambaati/codeclimate-action@v2.7.2
      env:
        CC_TEST_REPORTER_ID: "945dfb58a832d233a3caeb84e3e6d3be212e8c7abcb48117fce63b9adcb43647"
      with:
        coverageCommand: ./vendor/bin/phpunit --testdox --stop-on-failure --coverage-clover=clover.xml
