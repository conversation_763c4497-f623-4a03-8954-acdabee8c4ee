<?php
session_start();
include('config.php');

// Get the area_id from AJAX request
if(isset($_POST['area_id'])){
    $area_id = intval($_POST['area_id']);
    
    // Fetch categories for the selected area
    $query = "SELECT `CategoryId`, `AreaId`, `Category` FROM `tblcategory` WHERE Is_Active = 1 AND AreaId = " . $area_id;
    $stmt = $con->prepare($query);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $options = "<option value=''>Select Category</option>";
    while ($row = $result->fetch_assoc()) {
        $options .= "<option value='{$row['CategoryId']}'>{$row['Category']}</option>";
    }
    
    // echo $options;
    
    // Fetch Articles from particular Area

    if($area_id == -1){
        $selectArticles = "SELECT
                            tblarticles.id AS articleid,
                            tblarticles.ArticleTitle AS title,
                            tblarea.AreaName AS category,
                            tblcategory.Category AS subcategory
                        FROM
                            tblarticles
                        LEFT JOIN tblarea ON tblarea.id = tblarticles.AreaId
                        LEFT JOIN tblcategory ON tblcategory.CategoryId = tblarticles.CategoryId
                        WHERE
                            tblarticles.Is_Active = 1";
    }else{
        $selectArticles = "SELECT
                            tblarticles.id AS articleid,
                            tblarticles.ArticleTitle AS title,
                            tblarea.AreaName AS category,
                            tblcategory.Category AS subcategory
                        FROM
                            tblarticles
                        LEFT JOIN tblarea ON tblarea.id = tblarticles.AreaId
                        LEFT JOIN tblcategory ON tblcategory.CategoryId = tblarticles.CategoryId
                        WHERE
                            tblarticles.Is_Active = 1 AND tblarticles.AreaId = " . $area_id;
    }

    
    $execQuery = mysqli_query($con, $selectArticles);
    
    $rowCount = mysqli_num_rows($execQuery);
    
    $articles = "";
    if($rowCount != 0){
        while($row = mysqli_fetch_assoc($execQuery)){
            $articles .= "<tr>
            <td><b>{$row['title']}</b></td>
            <td>{$row['category']}</td>
            <td>{$row['subcategory']}</td>
            <td><a
                    href='edit-article.php?pid={$row['articleid']}'><i
                        class='fa fa-pencil' style='color: #29b6f6;'></i></a>
                &nbsp;<a
                    href='manage-articles.php?pid={$row['articleid']}&&action=del'
                    onclick='return confirm('Do you really want to delete ?')'> <i
                        class='fa fa-trash-o' style='color: #f05050'></i></a> </td>
        </tr>";
        }
    }
    
    echo $options . " *=* " . $articles;
}else if(isset($_POST['category_id'])){
    $category_id = $_POST['category_id'];

    $selectArticles = "SELECT
                        tblarticles.id AS articleid,
                        tblarticles.ArticleTitle AS title,
                        tblarea.AreaName AS category,
                        tblcategory.Category AS subcategory
                    FROM
                        tblarticles
                    LEFT JOIN tblarea ON tblarea.id = tblarticles.AreaId
                    LEFT JOIN tblcategory ON tblcategory.CategoryId = tblarticles.CategoryId
                    WHERE
                        tblarticles.Is_Active = 1 AND tblarticles.CategoryId = " . $category_id;
    
    $execQuery = mysqli_query($con, $selectArticles);
    
    $rowCount = mysqli_num_rows($execQuery);
    
    $articles = "";
    if($rowCount != 0){
        while($row = mysqli_fetch_assoc($execQuery)){
            $articles .= "<tr>
            <td><b>{$row['title']}</b></td>
            <td>{$row['category']}</td>
            <td>{$row['subcategory']}</td>
            <td><a
                    href='edit-article.php?pid={$row['articleid']}'><i
                        class='fa fa-pencil' style='color: #29b6f6;'></i></a>
                &nbsp;<a
                    href='manage-articles.php?pid={$row['articleid']}&&action=del'
                    onclick='return confirm('Do you really want to delete ?')'> <i
                        class='fa fa-trash-o' style='color: #f05050'></i></a> </td>
        </tr>";
        }
    }

    echo $articles;
}

$stmt->close();
$con->close();
?>