<?php
session_start();
include "./includes/config.php";
include "./EmailHelper.php";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['invite_user'])) {
    try {
        $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
        $role = $_POST['role'];
        $name = $_POST['name'];

        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = "Invalid email address.";
        } else {
            // Check if the email is already invited or exists in users
            $stmt = $con->prepare("SELECT COUNT(*) FROM invitations WHERE email = ?");
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $stmt->bind_result($invitationCount);
            $stmt->fetch();
            $stmt->close();

            if ($invitationCount > 0) {
                $error = "An invitation has already been sent to this email.";
            } else {
                $stmt = $con->prepare("SELECT COUNT(*) FROM tblusers WHERE email = ?");
                $stmt->bind_param("s", $email);
                $stmt->execute();
                $stmt->bind_result($userCount);
                $stmt->fetch();
                $stmt->close();

                if ($userCount > 0) {
                    $error = "This email is already registered.";
                } else {
                    // Generate a unique token
                    $token = bin2hex(random_bytes(32));

                    // Set expiration (24 hours from now)
                    $expiresAt = date('Y-m-d H:i:s', strtotime('+24 hours'));

                    // Insert invitation
                    $stmt = $con->prepare("
                        INSERT INTO invitations (email, name, role, token, invited_by, expires_at)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ");
                    $invitedBy = $_SESSION['logged_in_user']['id']; // Assuming Auth::getUserId() is available

                    $stmt->bind_param("ssssis", $email, $name, $role, $token, $invitedBy, $expiresAt);

                    if ($stmt->execute()) {
                        // Send email
                        if (EmailHelper::sendInvitationEmail($email, $token)) {
                            $success = "Invitation sent successfully to $email.";
                            header("Location: userview.php");
                            exit;
                        } else {
                            $error = "Failed to send invitation email.";
                        }
                    } else {
                        $error = "Database insertion failed: " . $stmt->error;
                    }
                    $stmt->close();
                }
            }
        }
        $con->close();
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

?>