<?php
session_start();
include('../dist/include/config.php');
header('Content-Type: application/json'); // Ensure JSON response

$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($id > 0) {
    $query = "SELECT likes, dislikes FROM tblreviews WHERE id = ?";
    $stmt = $con->prepare($query);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($row = $result->fetch_assoc()) {
        echo json_encode($row);
    } else {
        echo json_encode(["likes" => 0, "dislikes" => 0]); // Default if no record found
    }
} else {
    echo json_encode(["error" => "Invalid ID"]);
}
