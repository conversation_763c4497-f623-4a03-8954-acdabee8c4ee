<?php
session_start();
include('dist/include/config.php');

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Celaeno HelpDesk</title>

    <link rel="shortcut icon" href="/dist/images/favicon.ico">

    <script src="https://kit.fontawesome.com/1b5fdf4bb2.js" crossorigin="anonymous"></script>
    <link href="dist/css/main.css" rel="stylesheet">
    <style>
        .col-md-6 {
            text-align: center;
        }
    </style>
</head>

<body>
    <section class="jumbotron jumbotron-fluid text-center">
        <nav class="ct-nav" style="">
            <div class="navbar navbar-dark navbar-static-top navbar-expand-lg">
                <a href="#" class="navbar-brand">
                    <img src="./dist/images/logo.jpg" alt="" srcset="">
                </a>
                <div class="ct-nav-list">
                    <a href="index.php" class="">Home</a>
                    <a href="knowledgebase.php" class="">Knowledge Base</a>
                </div>
                <div class="ct-head-btn-01">
                    <?php
                    if (isset($_SESSION['user'])) {
                        echo '<a href="./logout.php"><button class="ct-help-btn">Log out</button></a>';
                    } else {
                        echo '<a href="./login.php"><button class="ct-help-btn">Log In</button></a>';
                    }
                    ?>
                </div>
            </div>
        </nav>

        <div class="cover"></div>

        <div class="container">
            <h1 class="jumbotron-heading">Welcome to Celaeno Assist</h1>

            <div class="row justify-content-center text-center">
                <div class="col-md-10">
                    <form id="searchForm" action="/" class="search-form" method="GET">
                        <div class="input-group search">
                            <input type="text" name="q" class="search-field" aria-label="Search field"
                                placeholder="Search anything here..." aria-required="true" required
                                autoComplete="off" />
                            <button class="input-group-addon" aria-label="Search">
                                <i class="fa fa-search">&nbsp;</i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <div class="section text-muted">
        <div class="container">
            <div class="row content justify-content-center">
                <div class="col-md-12 text-center">
                    <div class="ct-help-button">
                        <a href="pages/addtopicform.php">Add Topic</a>
                        <a href="pages/supportticketform.php">Create New Ticket</a>
                    </div>
                    <div class="heading">
                        <h1>Products</h1>
                    </div>
                </div>

                <!-- <?php
                        $selectAreaQuery = "SELECT id, ProductName FROM tblarea";
                        $selectAreaFetch = mysqli_query($con, $selectAreaQuery);
                        while ($row = mysqli_fetch_assoc($selectAreaFetch)) {
                            echo '<div class="col-sm-12 col-md-6 col-lg-4 mb-3">
          <a href="pages/area.php?pid=' . $row["id"] . '" class="card-link">
            <div class="card card-minimal category-box text-center">
              <div class="card-body justify-content-center">
                <img src="dist/images/Punctual Logo Final .png" alt="' . $row["ProductName"] . '" srcset="">
                <h4>' . $row["ProductName"] . '</h4>
              </div>
            </div>
          </a>
        </div>';
                        }
                        ?> -->

                <?php
                $selectAreaQuery = "SELECT id, ProductName, Logo FROM tblarea WHERE visibility = 'public'";
                $selectAreaFetch = mysqli_query($con, $selectAreaQuery);

                if (!$selectAreaFetch) {
                    die("Query failed: " . mysqli_error($con));
                }

                while ($row = mysqli_fetch_assoc($selectAreaFetch)) {
                    // Fetch values and sanitize
                    $id = htmlspecialchars($row["id"]);
                    $productName = htmlspecialchars($row["ProductName"]);
                    $logoPath = htmlspecialchars($row["Logo"]);

                    // Set a default image if the Logo field is empty or null
                    if (empty($logoPath)) {
                        $logoPath = 'dist/images/Punctual Logo Final .png'; // Default placeholder image
                    } else {
                        // Assuming images are stored in the "uploads" directory, update the path accordingly
                        $logoPath = 'admin/' . $logoPath;
                    }

                    // Display the data
                    echo '<div class="col-sm-12 col-md-6 col-lg-4 mb-3">    
            <a href="pages/area.php?pid=' . $id . '" class="card-link">
                <div class="card card-minimal category-box text-center">
                    <div class="card-body justify-content-center">
                        <img src="' . $logoPath . '" alt="' . $productName . '" class="img-fluid" style="max-width: 150px; height: auto;">
                        <h4>' . $productName . '</h4>
                    </div>
                </div>
            </a>
        </div>';
                }
                ?>



            </div>
        </div>
        </a>
    </div>



    <footer>
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    Celaeno HelpDesk
                    <p class="mt-1"><strong><small class="text-muted">Copyright © 2024 , All Right Reserved by <a
                                    href="#">Celaeno
                                    Technology</a></small></strong></p>
                </div>

            </div>
        </div>
    </footer>

    <script src="./dist/js/main.js"></script>
</body>

</html>