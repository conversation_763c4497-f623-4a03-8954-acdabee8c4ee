<?php
header('Content-Type: application/json');

// Include database configuration
include "./includes/config.php";

try {
    // Ensure PDO is available from config.php
    if (!isset($pdo)) {
        throw new Exception("Database connection not established");
    }

    // Set PDO error mode to exception
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Get form data
    $ticket_id = $_POST['ticket_id'] ?? null;
    $status = $_POST['status'] ?? null;
    $priority = $_POST['priority'] ?? null;
    $due_date = $_POST['due_date'] ?? null;

    // Validate input
    if (!$ticket_id || !$status || !$priority) {
        throw new Exception("Missing required fields");
    }

    // Update query
    $query = "UPDATE tickets SET status = :status, priority = :priority, due_date = :due_date, updated_at = NOW() 
            WHERE id = :ticket_id";
    $stmt = $pdo->prepare($query);
    $stmt->execute([
        'status' => $status,
        'priority' => $priority,
        'due_date' => $due_date ?: null, // Handle empty due_date
        'ticket_id' => $ticket_id
    ]);

    echo json_encode(['success' => true]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
